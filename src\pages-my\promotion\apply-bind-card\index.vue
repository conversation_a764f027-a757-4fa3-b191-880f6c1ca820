<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-cols-1 grid-rows-[auto_1fr_auto] box-border px-24rpx bg-#F6F6F6"
    :style="{ paddingBottom: `${bottom || 15}px` }"
  >
    <SimpleNavBar :title="navBarTitle" />

    <div class="size-full pt-20rpx box-border of-auto">
      <wd-form :model="bindModel" :rules="bindRules" ref="bindFormRef">
        <div class="size-full grid grid-cols-1 gap-y-20rpx">
          <!-- 身份证 -->
          <div
            class="bg-#ffffff w-full rd-20rpx px-28rpx py24rpx box-border flex flex-col gap-y-5px"
          >
            <span class="text-28rpx text-#333333">请上传身份证正反面</span>
            <div class="flex items-center justify-around">
              <wd-cell required prop="identityFrontImage">
                <wd-upload
                  :file-list="bindModel.forShowidentityImag1 as UploadFile[]"
                  :upload-method="uploadIdCardFront"
                  name="iFile"
                  :limit="1"
                  @success="handleIdCardFrontImgSuccess"
                  @change="handleIdCardFrontImgChange"
                  accept="image"
                  :action="VITE_UPLOAD_BASEURL"
                >
                  <wd-img
                    src="https://file.shanqianqi.com/image/2025/06/10/477deac72e994d209074cc1cf94211a0.png"
                    width="220rpx"
                    height="220rpx"
                    mode="aspectFill"
                  ></wd-img>
                </wd-upload>
              </wd-cell>
              <wd-cell required prop="identityBackImage">
                <wd-upload
                  :file-list="bindModel.forShowidentityImag2 as UploadFile[]"
                  :upload-method="uploadIdCardBack"
                  name="iFile"
                  :limit="1"
                  @success="handleIdCardBackImgSuccess"
                  @change="handleIdCardBackImgChange"
                  accept="image"
                  :action="VITE_UPLOAD_BASEURL"
                >
                  <wd-img
                    src="https://file.shanqianqi.com/image/2025/06/10/c34243d6ab9249f9a4035e2b8b1bd8eb.png"
                    width="220rpx"
                    height="220rpx"
                    mode="aspectFill"
                  ></wd-img>
                </wd-upload>
              </wd-cell>
            </div>
          </div>
          <!-- 银行卡 -->
          <div
            class="bg-#ffffff w-full rd-20rpx px-28rpx py24rpx box-border flex flex-col gap-y-5px"
          >
            <span class="text-28rpx text-#333333">请上传银行卡卡号面</span>
            <div class="flex items-center justify-around">
              <wd-cell required prop="bankCardImage">
                <wd-upload
                  :file-list="bindModel.forShowbankCardImageId1 as UploadFile[]"
                  :upload-method="uploadBankCardFront"
                  name="iFile"
                  :limit="1"
                  @success="handleuploadBankCardFrontImgSuccess"
                  @change="handleuploadBankCardFrontImgChange"
                  accept="image"
                  :action="VITE_UPLOAD_BASEURL"
                >
                  <wd-img
                    src="https://file.shanqianqi.com/image/2025/06/17/68b49993e4b9446194076e33eba6f4b1.png"
                    width="220rpx"
                    height="220rpx"
                    mode="aspectFill"
                  ></wd-img>
                </wd-upload>
              </wd-cell>
            </div>
          </div>
          <!-- 信息 -->
          <div
            class="bg-#ffffff w-full rd-20rpx px-28rpx py24rpx box-border flex flex-col gap-y-5px"
          >
            <wd-input
              label="姓名"
              label-width="100px"
              prop="name"
              required
              clearable
              v-model="bindModel.name"
              placeholder="请输入姓名"
            />
            <wd-input
              label="身份证号"
              label-width="100px"
              prop="identity"
              required
              clearable
              v-model="bindModel.identity"
              placeholder="请输入身份证号"
            />
            <wd-cell title="身份证有效期" title-width="100px" required prop="idValidity">
              <wd-radio-group
                custom-class="!flex"
                v-model="bindModel.idValidity"
                shape="dot"
                @change="handleValidityChange"
                inline
              >
                <wd-radio :value="0">非永久</wd-radio>
                <wd-radio :value="1">永久</wd-radio>
              </wd-radio-group>
            </wd-cell>
            <wd-cell title="开始时间" title-width="100px" required prop="idCardStartDate">
              <wd-datetime-picker
                custom-class="!flex  !border-none"
                custom-cell-class="!p-unset"
                placeholder="请选择开始时间"
                @confirm="handleIdcardStDtChange"
                v-model="bindModel.forShowIdCardStDt"
                prop=""
                :min-date="minDate"
                :max-date="maxDate"
                type="date"
              />
            </wd-cell>
            <wd-cell
              v-if="bindModel.idValidity === 0"
              title="结束时间"
              title-width="100px"
              required
              prop="idCardExpiredDate"
            >
              <wd-datetime-picker
                custom-class="!flex  !border-none"
                custom-cell-class="!p-unset"
                @confirm="handleIdcardExpDtChange"
                placeholder="请选择结束时间"
                :min-date="minDate"
                :max-date="maxDate"
                v-model="bindModel.forShowIdCardExpDt"
                type="date"
              />
            </wd-cell>
            <wd-input
              label="银行卡号"
              label-width="100px"
              prop="bankCard"
              required
              clearable
              v-model="bindModel.bankCard"
              placeholder="请输入银行卡号"
            />
            <wd-input
              label="开户银行"
              label-width="100px"
              prop="bankName"
              required
              clearable
              v-model="bindModel.bankName"
              placeholder="请输入正确的开户银行"
            />
          </div>
        </div>
      </wd-form>
    </div>

    <div class="w-full box-border pt-20rpx">
      <wd-button @click="handleSubmit" block custom-class="!h-96rpx !rd-48rpx !bg-#8445C7">
        提交
      </wd-button>
    </div>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import type { FormRules } from 'wot-design-uni/components/wd-form/types'
import type { UploadFile } from 'wot-design-uni/components/wd-upload/types'
import { createUploadHandler, getEnvBaseUploadUrl } from '@/utils'
import { picRecognize } from '@/service'
import { agentApply } from '@/service/api/agent'
import dayjs from 'dayjs'

const now = dayjs()

const minDate = ref(now.subtract(30, 'year').valueOf())

const maxDate = ref(now.add(30, 'year').valueOf())

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const navBarTitle = ref('请填写提现信息')

const bindFormRef = ref()

const bindModel = ref<Api.aggent.ApplyBody>({
  // 身份证正面
  forShowidentityImag1: [],
  identityFrontImage: 0,
  // 身份证反面
  forShowidentityImag2: [],
  identityBackImage: 0,
  // 身份证有效期
  idValidity: 0,
  // 身份证有效期开始时间
  idCardStartDate: '',
  forShowIdCardStDt: 0,
  // 身份证有效期结束时间
  idCardExpiredDate: '',
  forShowIdCardExpDt: 0,
  // 银行卡卡号
  forShowbankCardImageId1: [],
  bankCardImage: 0,
  //姓名
  name: '',
  // 身份证号
  identity: '',
  //银行卡号
  bankCard: '',
  //开户行相关
  forShowBankInfo: [],
  settleProvinceCode: '',
  settleProvinceName: '',
  settleCityCode: '',
  settleCityName: '',
  openningBankCode: '',
  bankName: '',
  clearingBankCode: '',
})

const bindRules = ref<FormRules>({
  identityFrontImage: [
    {
      required: true,
      message: '请上传身份证人面像',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请上传身份证正面')
        }
      },
    },
  ],
  identityBackImage: [
    {
      required: true,
      message: '请上传身份证国微像',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请上传身份证反面')
        }
      },
    },
  ],
  bankCardImage: [
    {
      required: true,
      message: '请上传银行卡卡号面',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请上传银行卡卡号面')
        }
      },
    },
  ],
  name: [
    {
      required: true,
      message: '请输入姓名',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请输入姓名')
        }
      },
    },
  ],
  bankCard: [
    {
      required: true,
      message: '请输入姓名',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请输入姓名')
        }
      },
    },
  ],
  identity: [
    {
      required: true,
      message: '请输入身份证号',
      validator: (value) => {
        if (!value) {
          return Promise.reject('请输入身份证号')
        } else if (
          !/^[1-9]\d{5}(18|19|20|21)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(
            value,
          )
        ) {
          return Promise.reject('请正确输入身份证号')
        } else {
          return Promise.resolve()
        }
      },
    },
  ],
  idCardStartDate: [
    {
      required: true,
      message: '请选择有效期',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请选择有效期')
        }
      },
    },
  ],
  idCardExpiredDate: [
    {
      required: true,
      message: '请选择有效期',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请选择有效期')
        }
      },
    },
  ],
  bankName: [
    {
      required: true,
      message: '请选择开户银行',
      validator: (value) => {
        if (value) {
          return Promise.resolve()
        } else {
          return Promise.reject('请选择开户银行')
        }
      },
    },
  ],
})

// 人像面
const uploadIdCardFront = createUploadHandler('image')
const handleIdCardFrontImgChange = async ({ fileList }) => {
  bindModel.value.forShowidentityImag1 = fileList
  bindModel.value.identityFrontImage = fileList[0]?.response?.fileId
}
const handleIdCardFrontImgSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'IdCard' })
      bindModel.value.name = data?.subImages[0]?.kvInfo?.data?.name
      bindModel.value.identity = data?.subImages[0]?.kvInfo?.data?.idNumber
    } catch (error) {}
  }
}
// 国徽面
const uploadIdCardBack = createUploadHandler('image')
const handleIdCardBackImgChange = async ({ fileList }) => {
  bindModel.value.forShowidentityImag2 = fileList
  bindModel.value.identityBackImage = fileList[0]?.response?.fileId
}
const handleIdCardBackImgSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'IdCard' })
      const sT = data?.subImages[0]?.kvInfo?.data?.validPeriod?.split('-')?.[0]
      const eT = data?.subImages[0]?.kvInfo?.data?.validPeriod?.split('-')?.[1] ?? ''

      bindModel.value.idCardStartDate = dayjs(sT, 'YYYY.MM.DD').format('YYYY-MM-DD')
      bindModel.value.forShowIdCardStDt = dayjs(sT, 'YYYY.MM.DD').valueOf()

      if (!eT) {
        bindModel.value.idCardExpiredDate = '9999-12-31'
        bindModel.value.idValidity = 1
        return
      }

      bindModel.value.idCardExpiredDate = dayjs(eT).format('YYYY-MM-DD')
      bindModel.value.forShowIdCardExpDt = dayjs(eT).valueOf()
    } catch (error) {}
  }
}

//有效期
const handleValidityChange = ({ value }) => {
  if (value === 1) bindModel.value.idCardExpiredDate = '9999-12-31'
}
// 身份证有效期开始时间
const handleIdcardStDtChange = ({ value }) => {
  bindModel.value.idCardStartDate = dayjs(value).format('YYYY-MM-DD')
}
// 身份证有效期结束时间
const handleIdcardExpDtChange = ({ value }) => {
  bindModel.value.idCardExpiredDate = dayjs(value).format('YYYY-MM-DD')
}
// 正面
const uploadBankCardFront = createUploadHandler('image')
const handleuploadBankCardFrontImgChange = async ({ fileList }) => {
  bindModel.value.forShowbankCardImageId1 = fileList
  bindModel.value.bankCardImage = fileList[0]?.response?.fileId
}
const handleuploadBankCardFrontImgSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'BankCard' })
      bindModel.value.bankCard = data?.subImages[0]?.kvInfo?.data?.cardNumber
    } catch (error) {}
  }
}

const handleSubmit = () => {
  bindFormRef.value
    .validate()
    .then(async ({ valid, errors }) => {
      console.log(valid)
      if (valid) {
        try {
          await agentApply(bindModel.value)
          uni.navigateBack()
        } catch (error) {}
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
</script>

<style lang="scss" scoped>
:deep(.wd-col-picker__cell) {
  padding: unset !important;
}
:deep(.wd-col-picker__cell::after) {
  display: none !important;
}
:deep(.wd-picker__cell) {
  padding: unset !important;
}
</style>
