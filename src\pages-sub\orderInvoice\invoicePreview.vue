<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '发票预览',
  },
}
</route>

<template>
  <view class="flex justify-center">
    <view class="w-722rpx h480rpx">
      <wd-img
        v-if="invoiceDetail.ticketFilePath && invoiceDetail.ticketFilePath != ''"
        width="722rpx"
        height="480rpx"
        :src="invoiceDetail.ticketFilePath"
      />
      <wd-img v-else width="722rpx" height="480rpx" src="@/static/images/privew.png" />
    </view>
  </view>
</template>

<script lang="ts" setup>
//
import { getOrderTicketsById } from '@/service'
import { useUserStore } from '@/store'
const userStore = useUserStore()
const orderTicketId = ref<any>(null)
onLoad((options) => {
  if (options.id) {
    orderTicketId.value = options.id
  }
  fetchData()
})
const invoiceDetail = ref<any>(null)
const fetchData = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    let { data } = await getOrderTicketsById({
      appId: import.meta.env.VITE_APP_ID,
      userId: userStore?.userInfo?.userId,
      orderTicketId: Number(orderTicketId.value),
    })
    invoiceDetail.value = data

    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
//
</style>
