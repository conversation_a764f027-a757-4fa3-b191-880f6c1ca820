<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '提交评价',
  },
}
</route>

<template>
  <view class="evaluate p30rpx bg-white box-border" v-if="!loading">
    <form @submit="formSubmit" @reset="formReset">
      <view class="evaluate-item" v-for="(item, index) in tableData" :key="index">
        <view class="product flex items-center gap20rpx">
          <view class="cover">
            <image class="w-160rpx h-160rpx" :src="item.productImage" mode="aspectFit"></image>
          </view>
          <view class="flex flex-col justify-center gap10rpx text-28rpx">
            <view class="">{{ item.productName }}</view>
            <view class="text-red">
              ¥
              <text class="text-40rpx">{{ item.productPrice }}</text>
            </view>
          </view>
        </view>
        <view class="flex flex-col gap20rpx py30rpx text-24rpx text-#000000">
          <view class="flex items-center gap20rpx">
            描述相符：
            <wd-rate :num="5" custom-class="flex-1" v-model="describe_score" @change="choosees" />
          </view>
          <view class="flex items-center gap20rpx">
            服务态度：
            <wd-rate :num="5" custom-class="flex-1" v-model="server_score" @change="choosees" />
          </view>
          <view class="flex items-center gap20rpx">
            配送服务：
            <wd-rate :num="5" custom-class="flex-1" v-model="express_score" @change="choosees" />
          </view>
        </view>
        <view class="grade flex items-center justify-between">
          <view
            :class="item.score == 10 ? 'd-c-c active' : 'd-c-c'"
            @click="gradeFunc(item, 10, index)"
          >
            <view class="flex items-center gap10rpx">
              <text class="i-hugeicons-happy"></text>
              <text class=" ">好评</text>
            </view>
          </view>
          <view
            :class="item.score == 20 ? 'd-c-c active' : 'd-c-c'"
            @click="gradeFunc(item, 20, index)"
          >
            <view class="flex items-center gap10rpx">
              <text class="i-hugeicons-meh"></text>
              <text class=" ">中评</text>
            </view>
          </view>
          <view
            :class="item.score == 30 ? 'd-c-c active' : 'd-c-c'"
            @click="gradeFunc(item, 30, index)"
          >
            <view class="flex items-center gap10rpx">
              <text class="i-hugeicons-sad-01"></text>
              <text class=" ">差评</text>
            </view>
          </view>
        </view>
        <view class="textarea-box text-28rpx">
          <textarea
            class="p20rpx box-s-b border flex-1"
            v-model="item.content"
            placeholder="请输入评价内容"
          />
        </view>

        <wd-upload
          :file-list="(item as any).uploadFileList || []"
          image-mode="aspectFill"
          :action="action"
          name="iFile"
          :limit="6"
          :form-data="formData"
          @change="(files) => handleChange(files, index)"
          @before-upload="(file) => handleBeforeUpload(file, index)"
          @success="(res) => handleSuccess(res, index)"
          @fail="(err) => handleFail(err, index)"
        ></wd-upload>
      </view>
      <view class="foot-btns py30rpx box-border">
        <button form-type="submit" class="rounded-30rpx text-white bg-red fw-bold">提交评价</button>
      </view>
    </form>

    <!--上传图片-->
    <!-- <Upload v-if="isUpload" @getImgs="getImgsFunc"></Upload> -->
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
// import Upload from '@/components/upload/upload.vue'
import { GetUserCommentToOrder, SubmitUserCommentToOrder } from '@/service'
import { getEnvBaseUploadUrl } from '@/utils'

interface FormDataItem {
  orderProductId: number | string
  productId: number | string
  score: number | string
  imageList: { filePath: string }[]
  expressScore: number | string
  serverScore: number | string
  describeScore: number | string
  orderId: number | string
  userId: number
  content: string
}
// 移除全局fileList，改为使用tableData中每个商品的imageList

const action = ref(getEnvBaseUploadUrl()) // 等同于 useUpload 中的 VITE_UPLOAD_BASEURL
const formData = ref({
  appId: 10001,
  timestamp: Date.now(),
})
// 响应式数据
const loading = ref(true)
const orderId = ref('')
let defauleScore = ref(10)
let tableData = ref<Api.User.UserCommentToOrder[]>([])
// let formData = reactive({
//       orderProductId:  null,
//     productId:  null,
//     score:  null,
//     imageList:  null,
//     expressScore:   5,
//     serverScore:  5,
//     describeScore:  5,
//     orderId:  null,
//     userId:  null,
//     content: null
// })
let describe_score = ref(5)
let server_score = ref(5)
let express_score = ref(5)
// 生命周期钩子
onLoad((e: any) => {
  orderId.value = e.orderId
})

onMounted(() => {
  // uni.showLoading({
  //   title: '加载中'
  // })
  // 获取页面数据
  getData()
})
const handleChange = ({ fileList: files }, productIndex: number) => {
  console.log('文件列表变化:', files, '商品索引:', productIndex)

  // 更新对应商品的wot-design-uni格式的图片列表
  ;(tableData.value[productIndex] as any).uploadFileList = files

  // 为了兼容原有的API接口，也更新imageList（虽然最终提交时会重新构建）
  tableData.value[productIndex].imageList = files
    .filter((file: any) => file.status === 'success' && file.url)
    .map((file: any) => ({
      filePath: file.url,
    }))
}

// 工具方法已移除，直接使用uni API

// 获取数据
const getData = async () => {
  uni.showLoading({
    title: '正在处理',
  })

  try {
    const res = await GetUserCommentToOrder({
      orderId: orderId.value,
      appId: import.meta.env.VITE_APPID,
    })
    res.data.forEach((item) => {
      item.score = defauleScore.value
      // 初始化每个商品的imageList为空数组，确保格式正确
      // 注意：这里需要兼容wot-design-uni的UploadFile格式和API的{ filePath: string }格式
      item.imageList = []
      // 添加一个临时的uploadFileList来存储wot-design-uni格式的文件
      ;(item as any).uploadFileList = []
    })
    tableData.value = res.data

    console.log('res', res.data)

    uni.hideLoading()
    loading.value = false
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '查询失败请重试',
      duration: 2000,
      icon: 'none',
    })
  }
}
const handleBeforeUpload = (file: any, productIndex: number) => {
  console.log('准备上传:', file, '商品索引:', productIndex)
  return true
}

const handleSuccess = (res: any, productIndex: number) => {
  console.log('上传成功:', res, '商品索引:', productIndex)
  if (res.file.response) {
    const response = JSON.parse(res.file.response)
    // 更新对应商品uploadFileList中对应文件的真实fileId和filePath
    const uploadFileList = (tableData.value[productIndex] as any).uploadFileList || []
    const fileIndex = uploadFileList.findIndex((file: any) => file.uid === res.file.uid)
    if (fileIndex !== -1) {
      // 更新文件信息，保留组件需要的字段，同时添加服务器返回的真实信息
      uploadFileList[fileIndex] = {
        ...uploadFileList[fileIndex],
        uid: response.data.fileId, // 使用服务器返回的fileId作为uid
        url: response.data.filePath, // 使用服务器返回的filePath作为url
        thumb: response.data.filePath,
        response: res.file.response,
        status: 'success',
      }
    }
  }
}

const handleFail = (err: any, productIndex: number) => {
  console.error('上传失败:', err, '商品索引:', productIndex)
  uni.showToast({ title: '上传失败', icon: 'none' })
}
// 选择评价
const gradeFunc = (item: Api.User.UserCommentToOrder, score: number, index: number) => {
  console.log('score', score, item.score)

  tableData.value[index].score = score
}

const choosees = () => {
  // 评分变化处理逻辑（如果需要的话）
}

// 表单提交
const formSubmit = async () => {
  // 遍历验证所有评价内容
  if (
    tableData.value.some((item) => {
      // 额外添加空值保护（处理 item 为 null/undefined 的情况）
      if (!item) return true
      return !item.content || item.content.trim() === ''
    })
  ) {
    uni.showToast({
      title: '评价内容不能为空',
      duration: 1000,
      icon: 'none',
    })
    return
  }

  const formDataObj = tableData.value.map((item) => {
    // 从每个商品的uploadFileList构建fileIdList（保持原来的逻辑）
    const fileIdList = ((item as any).uploadFileList || [])
      .filter((file: any) => file.status === 'success' && file.url)
      .map((file: any) => ({
        fileId: file.uid, // 使用uid作为fileId（保持原来的逻辑）
        filePath: file.url, // 使用url作为filePath
      }))

    return {
      orderProductId: item.orderProductId,
      productId: item.productId,
      score: item.score,
      // 使用构建的fileIdList格式
      imageList: fileIdList,
      expressScore: express_score.value || 5,
      serverScore: server_score.value || 5,
      describeScore: describe_score.value || 5,
      userId: item.userId,
      content: item.content,
      orderId: item.orderId,
    }
  })
  try {
    let res = await SubmitUserCommentToOrder({
      appId: import.meta.env.VITE_APPID,
      orderId: tableData.value[0].orderId,
      params: formDataObj,
    })
    uni.showToast({
      title: res.msg || '评价成功',
    })
    uni.reLaunch({
      url: '/pages-my/userEvaluate/userEvaluate',
    })
  } catch (e) {
    uni.showToast({
      title: '评价失败,请重试！',
      icon: 'none',
    })
  }
}

const formReset = () => {
  // 重置表单逻辑
}

// 图片上传相关函数已移除，现在直接通过wot-upload组件处理
</script>

<style lang="scss" scoped>
.active {
  background: red;
  color: #fff;
  border-radius: 30rpx;
}
.d-c-c {
  padding: 10rpx;
  font-size: 28rpx;
}
</style>
