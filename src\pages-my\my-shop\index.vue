<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full grid grid-cols-1 grid-auto-rows-min">
    <SimpleNavBar title="商户管理" />
    <!-- head -->
    <div
      class="w-full bg-#ff5704 px26rpx py18rpx box-border h-223rpx flex justify-between items-center"
    >
      <div class="flex items-center gap-x-10px">
        <wd-img
          v-if="data?.supplier?.logoFilePath"
          width="102rpx"
          height="102rpx"
          radius="20rpx"
          :src="data?.supplier?.logoFilePath"
        ></wd-img>
        <div v-else class="w102rpx h102rpx rd-20rpx bg-#ffffff flex items-center justify-center">
          <div class="i-carbon-store text-#999999 text-40rpx"></div>
        </div>

        <div class="flex flex-col gap-y-5px text-#ffffff">
          <span class="text-30rpx font-bold">{{ data?.supplier?.name }}</span>
          <span class="text-24rpx">主营品牌：{{ data?.supplier?.categoryName }}</span>
          <span class="text-24rpx">加入时间：{{ data?.supplier?.createTime }}</span>
        </div>
      </div>
      <div
        class="h50rpx bg-#ffffff rd-25rpx opacity-85 text-24rpx text-#ff5704 flex items-center justify-center px20rpx box-border"
      >
        {{ data?.supplier?.isRecycle === 0 ? '正常营业' : '停止营业' }}
      </div>
    </div>
    <!-- data -->
    <div
      class="p10px box-border w-full grid grid-cols-1 grid-auto-rows-min gap-y-24rpx relative top--50rpx"
    >
      <!-- 数据概况 -->
      <div
        class="w-full bg-#ffffff rd-20rpx p30rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-34rpx"
      >
        <div class="text-32rpx text-#333333 font-bold">数据概况</div>
        <div class="flex flex-col w-full items-center justify-center gap-y-10px">
          <span class="text-24rpx text-#333333">累计提现(元)</span>
          <span class="text-36rpx text-#333333 font-bold">{{ data?.supplier?.cashMoney }}</span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex flex-col w-full items-center justify-center gap-y-10px">
            <span class="text-24rpx text-#333333">累计成交(笔)</span>
            <span class="text-36rpx text-#333333 font-bold">{{ data?.totalCount }}</span>
          </div>
          <div class="flex flex-col w-full items-center justify-center gap-y-10px">
            <span class="text-24rpx text-#333333">今日成交(笔)</span>
            <span class="text-36rpx text-#333333 font-bold">{{ data?.todayCount }}</span>
          </div>
          <div class="flex flex-col w-full items-center justify-center gap-y-10px">
            <span class="text-24rpx text-#333333">可提现</span>
            <span class="text-36rpx text-#333333 font-bold">{{ data?.supplier?.money }}</span>
          </div>
        </div>
      </div>
      <!-- 订单管理 -->
      <div
        class="w-full bg-#ffffff rd-20rpx p30rpx box-border grid grid-cols-1 grid-auto-rows-min gap-y-34rpx"
      >
        <div class="text-32rpx text-#333333 font-bold flex items-center justify-between">
          我的订单
          <div class="flex items-center gap-x-5px">
            <wd-button
              type="text"
              custom-class="!m-unset !p-unset !line-height-unset !text-24rpx !text-#666666"
              @click="
                () =>
                  navigatePage(
                    `/pages-my/my-shop/merchantOrders/index?dataType=all&sourceActive=全部订单&shopSupplierId=${shopSupplierId}`,
                  )
              "
            >
              全部
            </wd-button>
            <div class="i-carbon-chevron-right text-#999999"></div>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <div
            v-for="item in orderMenuList"
            :key="item?.url"
            @click="() => navigatePage(item?.url)"
            class="flex flex-col items-center gap-y-7px"
          >
            <wd-img width="54rpx" height="54rpx" :src="item?.image"></wd-img>
            <span class="text-26rpx text-#333333">{{ item?.title }}</span>
          </div>
        </div>
      </div>

      <!-- 跳转 -->
      <div
        class="w-full bg-#ffffff rd-20rpx px30rpx box-border grid grid-cols-1 grid-auto-rows-min"
      >
        <div
          v-for="(item, index) in menuList"
          :key="item?.url"
          @click="() => navigatePage(item?.url)"
          :class="index === menuList.length - 1 ? '' : 'border-b border-b-solid border-b-#eeeeee'"
          class="w-full flex items-center justify-between py10px box-border"
        >
          <div class="flex items-center gap-x-13px">
            <wd-img width="68rpx" height="68rpx" :src="item?.image"></wd-img>
            <span class="text-26rpx text-#333333">{{ item?.title }}</span>
          </div>
          <div class="i-carbon-chevron-right text-#999999"></div>
        </div>
      </div>
      <wd-popup
        v-model="verifyPop"
        custom-style="border-radius:32rpx;width:80%"
        @close="verifyPop = !verifyPop"
      >
        <div class="p-20rpx flex flex-col items-center gap-y-20rpx">
          <wd-img
            src="https://file.shanqianqi.com/image/2025/06/20/22bbdbaa5d2a4486996250fa3f6a7fc0.jpg"
            width="100%"
            enable-preview
            mode="widthFix"
          ></wd-img>
          <span class="text-30rpx text-#333333 font-bold text-center">
            点击图片,长按识别关注“微信支付商家助手”公众号完成认证
          </span>
        </div>
      </wd-popup>
      <wd-popup
        v-model="verifyAliPop"
        custom-style="border-radius:32rpx;width:80%"
        @close="verifyAliPop = !verifyAliPop"
      >
        <div class="p-20rpx flex flex-col items-center gap-y-20rpx">
          <wd-img
            src="https://file.shanqianqi.com/image/2025/07/01/302614b5672e4b98b65e3c54904cd7d6.png"
            width="100%"
            enable-preview
            mode="widthFix"
          ></wd-img>
          <span class="text-30rpx text-#333333 font-bold text-center">
            请使用支付宝扫码完成认证
          </span>
        </div>
      </wd-popup>
    </div>
  </view>
</template>

<script lang="ts" setup>
import { userTradeData } from '@/service'
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { useUserSettingStore } from '@/store'
const userSettingStore = useUserSettingStore()

const { data, run } = useRequest(() => userTradeData())

const shopSupplierId = computed(() => userSettingStore?.userSetting?.supplierUser?.shopSupplierId)

onShow(() => run())

const orderMenuList = computed(() => [
  {
    image: 'https://file.shanqianqi.com/image/2025/06/24/4f113ade3f004a57bab76e9c5c9acda4.png',
    title: '待支付',
    url: `/pages-my/my-shop/merchantOrders/index?dataType=payment&sourceActive=全部订单&shopSupplierId=${shopSupplierId.value}`,
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/06/24/bde57910acad4372ba06d6b54709c84f.png',
    title: '待发货',
    url: `/pages-my/my-shop/merchantOrders/index?dataType=delivery&sourceActive=全部订单&shopSupplierId=${shopSupplierId.value}`,
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/06/24/163db58be80c4f27a090a28a85734cc5.png',
    title: '待收货',
    url: `/pages-my/my-shop/merchantOrders/index?dataType=received&sourceActive=全部订单&shopSupplierId=${shopSupplierId.value}`,
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/06/24/687681614ac6438bb2416507b68ad1e4.png',
    title: '退款/售后',
    url: `/pages-my/my-shop/after_sale/index?tianchong=1&shopSupplierId=${shopSupplierId.value}`,
  },
])

const menuList = computed(() => [
  {
    image: 'https://file.shanqianqi.com/image/2025/06/20/7f0dcc6d80824ae3bf824f76a5a9377b.png',
    title: '商户数据',
    url: `/pages-my/my-shop/shop-data/index?shopSupplierId=${shopSupplierId.value}`,
    title_right: '',
    right_icon: '',
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/06/20/43129fd1631342aea298e75e7600d7da.png',
    title: '商品管理',
    url: `/pages-my/my-shop/shop-order/index?shopSupplierId=${shopSupplierId.value}`,
    title_right: '',
    right_icon: '',
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/06/20/8a10e54d711f451eaa34575d7d3c3655.png',
    title: '门店管理',
    url: `/pages-my/my-shop/store-data/index?shopSupplierId=${shopSupplierId.value}`,
    title_right: '',
    right_icon: '',
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/07/01/e4c4fa073d8f4ad9ad654fd727e1fce0.png',
    title: '微信实名认证',
    url: 'weixin',
    title_right: '',
    right_icon: '',
  },
  {
    image: 'https://file.shanqianqi.com/image/2025/07/01/fb2a1ee91148425286508f6025bf0d32.png',
    title: '支付宝实名认证',
    url: 'ali',
    title_right: '',
    right_icon: '',
  },
])

const verifyPop = ref(false)
const verifyAliPop = ref(false)

const navigatePage = (path: string) => {
  if (path === 'weixin') {
    verifyPop.value = true
    return
  }
  if (path === 'ali') {
    verifyAliPop.value = true
    return
  }
  uni.navigateTo({
    url: path,
  })
}
</script>

<style lang="scss" scoped>
//
</style>
