<template>
  <div class="size-full grid grid-cols-1 grid-rows-[auto_1fr_auto] bg-#ffffff pb-100rpx box-border">
    <wd-navbar
      fixed
      placeholder
      safeAreaInsetTop
      :bordered="false"
      left-arrow
      @click-left="handleClickLeft"
    >
      <template #title>智能语音</template>
    </wd-navbar>
    <div class="flex flex-col items-center justify-center">
      <z-paging
        v-if="isAiMessage"
        ref="paging"
        empty-view-text="嘿，我是你的语音助手！初次见面很开心。我可以答你的各种问题，给你提供各种帮助，可以陪你聊天，你想问点什么呢？"
        v-model="dataList"
        use-chat-record-mode
        chat-adjust-position-offset="0px"
        inside-more
        :paging-style="{
          'background-color': '#ffffff',
          'padding-bottom': `${bottom || 15}px`,
          'box-sizing': 'border-box',
        }"
        auto-to-bottom-when-chat
        bottom-bg-color="#ffffff"
        empty-view-img="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
        :empty-view-img-style="{
          width: '200rpx',
          height: '200rpx',
          'margin-bottom': '24rpx',
        }"
        :empty-view-title-style="{
          'padding-left': '40rpx',
          'padding-right': '40rpx',
          'padding-top': '30rpx',
          'line-height': '40rpx',
          'padding-bottom': '40rpx',
          'margin-left': '24rpx',
          'margin-right': '24rpx',
          background: '#F7F7F7',
          'border-radius': '20rpx',
          color: '#333333',
        }"
        class="flex-1"
        :default-page-size="10"
        @query="queryList"
        :fixed="false"
      >
        <div class="px26rpx box-border flex flex-col gap-y-34rpx">
          <div v-for="(item, index) in dataList" :key="index" class="w-full">
            <div
              v-if="item?.role === 'assistant'"
              style="transform: scaleY(-1)"
              class="flex justify-start"
            >
              <div v-if="item?.content === 'loading'" class="bg-#F7F7F7 rd-20rpx">
                <wd-loading />
              </div>
              <div v-else class="bg-#F7F7F7 rd-20rpx text-#333333 break-all max-w-80%">
                <zero-markdown-view :markdown="convertLatexToDollarSyntax(item?.content)" />
              </div>
            </div>

            <!-- 用户提问 -->
            <div
              v-else-if="item?.role === 'user'"
              style="transform: scaleY(-1)"
              class="flex justify-end"
            >
              <div class="bg-#0099FF rd-20rpx text-white break-all max-w-80%">
                <zero-markdown-view :markdown="convertLatexToDollarSyntax(item?.content)" />
              </div>
            </div>
          </div>
          <div
            v-if="dataList.length"
            class="grid grid-cols-1 place-items-center gap-y-24rpx pt40rpx"
            style="transform: scaleY(-1)"
          >
            <wd-img
              width="200rpx"
              height="200rpx"
              src="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
              mode="aspectFill"
            ></wd-img>
            <div class="bg-#F7F7F7 px40rpx py30rpx lh-40rpx rd-20rpx">
              嘿，我是你的语音助手！初次见面很开心。我可以回答你的各种问题，给你提供各种帮助，可以陪你
              聊天，你想问点什么呢？
            </div>
          </div>
        </div>
        <!-- <template #bottom>
          <div class="w-full px26rpx box-border pt26rpx">
            <div
              class="w-full h-90rpx border-rd-30rpx flex items-center gap-x-20rpx bg-[#ffffff] shadow-[0rpx_8rpx_20rpx_#00000033,0rpx_0rpx_0rpx_8rpx_#def2ff] px-40rpx box-border"
            >
              <wd-img
                @click="stratSpeech = !stratSpeech"
                src="
              https://file.shanqianqi.com/image/2025/06/28/fce2cbf4c2e944e88c200a21f992e6f6.png
              "
                width="60rpx"
                height="60rpx"
                mode="aspectFill"
              ></wd-img>
              <div v-if="!voiceState?.isRecording" class="flex-1 h-full">
                <wd-input
                  v-if="!showVoice"
                  custom-class="!bg-transparent !w-full !h-full custom-input"
                  type="text"
                  @confirm="handleSend"
                  no-border
                  :readonly="isAnswering"
                  confirm-type="send"
                  v-model="sendInputVal"
                  placeholder="请输入您要咨询的内容"
                />
                <div
                  v-else
                  @touchstart.stop.prevent="handleTouchStart"
                  @touchmove.stop.prevent="handleTouchMove"
                  @touchend.stop.prevent="handleTouchEnd"
                  class="w-full h-full flex items-center justify-center text-#5B5EE8 text-30rpx"
                >
                  按住说话
                </div>
              </div>
              <div
                @touchstart.stop.prevent="handleTouchStart"
                @touchmove.stop.prevent="handleTouchMove"
                @touchend.stop.prevent="handleTouchEnd"
                v-else
                class="flex-1"
              >
                <LottieAnimation
                  :custom-style="{ width: '100%', height: '180rpx' }"
                  :animation-data="animationData"
                />
              </div>

              <wd-img
                @click="showVoice = !showVoice"
                :src="
                  !showVoice
                    ? 'https://file.shanqianqi.com/image/2025/06/25/254cb8e73fe8459080585453391bc4ac.png'
                    : 'https://file.shanqianqi.com/image/2025/06/25/5f10636c56374a35aa2c40144bcfc4c2.png'
                "
                width="60rpx"
                height="60rpx"
                mode="aspectFill"
              ></wd-img>
            </div>
          </div>
        </template> -->
      </z-paging>
      <wd-img
        v-else
        src="https://file.shanqianqi.com/image/2025/06/24/30659dcedbe44d7889903529faec0a26.png"
        width="500rpx"
        height="500rpx"
        mode="aspectFill"
      ></wd-img>
    </div>
    <div class="flex flex-col items-center gap-y-20rpx">
      <div class="flex gap-150rpx items-center">
        <div @click="onStop" class="flex flex-col gap-y-20rpx items-center">
          <wd-img
            src="https://file.shanqianqi.com/image/2025/06/30/214afb2b895f464498eae0afe7d4a8e8.png"
            width="100rpx"
            height="100rpx"
            mode="aspectFill"
          ></wd-img>
          <span class="text-28rpx text-#5B5EE8">挂断</span>
        </div>
        <div @click="openMessage" class="flex flex-col gap-y-20rpx items-center">
          <wd-img
            src="https://file.shanqianqi.com/image/2025/07/04/00bf5f9621f546e7863e03238da4a972.png"
            width="100rpx"
            height="100rpx"
            mode="aspectFill"
          ></wd-img>
          <span class="text-28rpx text-#5B5EE8">字幕</span>
        </div>
      </div>
      <div class="voice-box flex items-center justify-center text-28rpx text-#ffffff">
        通话中：{{ formattedTime }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useSpeechTranscription } from '@/hooks/useSpeechTranscription'
import { useTextToSpeech } from '@/hooks/useTextToSpeech'
import { useTimer } from '@/hooks/useTimer'
import { addAiChat, fetchUserAIChatList } from '@/service'
import { useUserStore } from '@/store'
import { convertLatexToDollarSyntax } from '@/utils'

const userStore = useUserStore()
let isAiMessage = ref(false)
const { formattedTime, pause } = useTimer({
  initialSeconds: 0,
  autoStart: true,
})

const {
  safeAreaInsets: { bottom, top },
} = uni.getSystemInfoSync()
// tts配置
const URL = import.meta.env.VITE_APP_ALI_SOCKET_URL
const AKID = import.meta.env.VITE_APP_ALI_AKID
const AKKEY = import.meta.env.VITE_APP_ALI_AKKEY
const APPKEY = import.meta.env.VITE_APP_ALI_APPKEY

const { stResult, initST, startST, stopST, stStart } = useSpeechTranscription({
  AKID,
  AKKEY,
  APPKEY,
  URL,
})

const { startTTS, ttsStart, stopPlayback } = useTextToSpeech({
  AKID,
  AKKEY,
  APPKEY,
  URL,
})

const aiChatId = ref<string | number>('')

watch(
  () => stResult.value,
  async (v) => {
    try {
      uni.showLoading({ title: '思考中...', mask: true })
      const { data } = await addAiChat({
        aiChatId: aiChatId.value,
        audio_txt: v,
        userId: userStore?.userInfo?.userId,
        session_id: userStore?.userInfo?.userId,
        model: 'deepseek-r1:7b',
      })
      console.log('🚀 ~ data:', data)

      uni.hideLoading()

      aiChatId.value = data?.aiChatId

      startTTS(data?.content)
    } catch (error) {
      uni.hideLoading()
    }
  },
)

watch(
  () => ttsStart.value,
  (start) => {
    if (start) {
      stopST()
    } else {
      initST()

      setTimeout(() => {
        startST()
      }, 500)
    }
  },
)
// 聊天列表
const paging = ref()
const dataList = ref<Api.AI.ChatListItem[]>([])
const queryList = async () => {
  try {
    paging?.value?.complete([])
  } catch (err) {
    paging?.value?.complete(false)
  }
}
onMounted(() => {
  initST()

  setTimeout(() => {
    startST()
  }, 500)
})

onUnmounted(() => {
  stopST()
  pause()
  stopPlayback()
})
const openMessage = () => {
  isAiMessage.value = !isAiMessage.value
}
const emits = defineEmits<{
  handleStop: []
}>()

const onStop = () => {
  emits('handleStop')
}

// 返回
const handleClickLeft = () => {
  uni.navigateBack()
}
</script>
<style scoped>
.voice-box {
  width: 590rpx;
  height: 90rpx;
  border-radius: 30rpx;
  background: #5b5ee8;
  animation: voicePulse 1.2s ease-in-out infinite;
  box-shadow:
    0rpx 0rpx 0rpx 0rpx #def2ff,
    0rpx 8rpx 20rpx #00000033;
}

@keyframes voicePulse {
  0% {
    box-shadow:
      0rpx 0rpx 0rpx 0rpx #def2ff,
      0rpx 8rpx 20rpx #00000033;
  }
  50% {
    box-shadow:
      0rpx 0rpx 0rpx 12rpx #def2ff80,
      0rpx 12rpx 24rpx #00000055;
  }
  100% {
    box-shadow:
      0rpx 0rpx 0rpx 0rpx #def2ff,
      0rpx 8rpx 20rpx #00000033;
  }
}
</style>
