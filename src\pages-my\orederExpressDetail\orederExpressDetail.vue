<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '物流详情',
  },
}
</route>

<template>
  <view class="wrap">
    <view class="order d-b-c">
      <view class="order-num">订单编号：{{ detail.orderNo }}</view>
      <view class="order-total" v-if="detail.deliveryType == 40">
        {{ orderDeliverList.length || 0 }}个包裹
      </view>
    </view>
    <view class="package-box">
      <template v-if="detail.deliveryType == 40">
        <scroll-view class="package-wrap" :scroll-x="true">
          <template v-for="(v, idx) in orderDeliverList" :key="idx">
            <view
              class="package-item"
              :class="{ active: currentIndex == idx }"
              @click="changPackage(idx)"
            >
              <view class="itemActive rounded-30rpx flex justify-center items-center">
                <view class="iconfont i-carbon-package-node"></view>
                <view>包裹{{ idx + 1 }}</view>
              </view>
            </view>
          </template>
        </scroll-view>
      </template>
      <view class="package-info">
        <view class="notice">快递走件、催派等物流信息，请联系物流公司，如需平台接入请联系客服</view>
        <view class="base-info">
          <template v-if="detail.deliveryType == 40">
            <view class="base-title">{{ orderDeliver.expressName }}</view>
            <view class="base-order-no">
              <view>运单号:{{ orderDeliver.expressNo }}</view>
              <view class="base-order-copy" @click="copy(orderDeliver.expressNo)">复制</view>
            </view>
          </template>
          <template v-else>
            <view class="base-title">{{ express.expressName }}</view>
            <view class="base-order-no">
              <view>运单号:{{ express.expressNo }}</view>
              <view class="base-order-copy" @click="copy(express.expressNo)">复制</view>
            </view>
          </template>
          <view class="base-product-list d-b-c" v-if="detail.deliveryType != 10">
            <view>
              <template v-for="(img, imgIdx) in orderDeliver.productList" :key="imgIdx">
                <image class="product-img" :src="img.imagePath" mode="aspectFill" />
              </template>
            </view>
            <view class="product-total" @click="openShowGoods">
              共{{ (orderDeliver.productList && orderDeliver.productList.length) || 0 }}种商品
              <text class="iconfont icon-jiantou1"></text>
            </view>
          </view>
        </view>
      </view>
      <!-- 物流步骤条 -->
      <view class="logistics-steps-container">
        <view class="logistics-title">物流信息</view>
        <wd-steps v-if="showExpressData.length > 0" :active="0" vertical dot>
          <wd-step
            v-for="(item, index) in showExpressData"
            :key="index"
            :status="index === 0 ? 'process' : 'finished'"
          >
            <template #title>
              <view class="logistics-time">
                {{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}
              </view>
            </template>
            <template #description>
              <view class="logistics-content">
                <view class="logistics-status">{{ item.status }}</view>
                <view class="logistics-context">{{ item.context }}</view>
              </view>
            </template>
          </wd-step>
        </wd-steps>
        <div v-else class="text-#999999 w-full p30rpx flex justify-center items-center">
          {{ express.express?.message ? express.express.message : '暂无物流信息' }}
        </div>

        <!-- 展开/收起按钮 -->
        <view
          v-if="express?.express?.data && express.express.data.length > 3"
          class="expand-btn"
          @click="toggleExpand"
        >
          <text>{{ isExpanded ? '收起' : '展开更多' }}</text>
          <text
            class="iconfont"
            :class="isExpanded ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'"
          ></text>
        </view>
      </view>
    </view>
    <!-- 商品列表 -->
    <view class="express-mask" v-if="showGoods">
      <view class="express-content">
        <view class="close-icon iconfont icon-guanbi1" @click="closeShowGoods"></view>
        <view class="title">包裹中的商品({{ product.length || 0 }})</view>
        <view class="desc">若有特殊或组合商品，可能会拆分多个包裹发货</view>
        <scroll-view :scroll-y="true" class="product-list">
          <template v-for="v in product" :key="v">
            <view class="product-item d-s-s one-product">
              <image
                class="w-160rpx h-160rpx"
                src="https://file.shanqianqi.com/image/2025/05/28/cb93ceae4b774cfb8e6984980f420360.png"
                model="aspectFit"
              />
              <!-- <image :src="v.imagePath" model="aspectFit" /> -->
              <view class="product-r">
                <view class="product-title">{{ v.productName }}</view>
                <view class="product-num">共{{ v.deliveryNum }}件</view>
              </view>
            </view>
          </template>
        </scroll-view>
        <view class="express-btn theme-btn" @click="closeShowGoods">完成</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  fetchUserSingleDataExpressDetail,
  fetchUserMultiExpressDetail,
  UserOrderDetail,
} from '@/service'
import dayjs from 'dayjs'
//
const state = reactive({
  orderId: '', //订单编号
})
// 订单详情
const detail = ref({
  orderNo: '', // 订单编号
  //配送方式(10单包裹发货 20上门自提 30无需物流 40多包裹发货)
  deliveryType: 10, // 物流类型：40 表示多个包裹
})

// 所有包裹列表（每个包裹包含快递信息、运单号、商品列表）
const orderDeliverList = ref([
  // {
  //   expressName: '中通快递',
  //   expressNo: 'ZTO1234567890',
  //   productList: [
  //     {
  //       imagePath: 'https://example.com/product1.jpg',
  //       productName: '秋冬加厚羊毛大衣',
  //       deliveryNum: 1,
  //     },
  //     {
  //       imagePath: 'https://example.com/product2.jpg',
  //       productName: '高腰显瘦牛仔裤',
  //       deliveryNum: 2,
  //     },
  //   ],
  // },
  // {
  //   expressName: '顺丰速运',
  //   expressNo: 'SF9876543210',
  //   productList: [
  //     {
  //       imagePath: 'https://example.com/product3.jpg',
  //       productName: '智能降噪蓝牙耳机',
  //       deliveryNum: 1,
  //     },
  //   ],
  // },
])

// 快递物流信息（用于非 40 类型订单或默认展示）
const express = ref<any>({
  // expressName: '默认快递公司',
  // expressNo: 'DEFAULT123456',
  // express: {
  //   data: [
  //     { time: '2023-10-01 08:00:00', status: '已签收，签收人：本人' },
  //     { time: '2023-09-30 18:30:00', status: '【杭州市】快件已发往 上海转运中心' },
  //     { time: '2023-09-30 09:00:00', status: '【杭州市】杭州XX公司 已收件' },
  //   ],
  // },
})
let showGoods = ref(false)
const currentIndex = ref(0)
// 当前显示的包裹对象
const orderDeliver = ref(null)

const product = ref([])
onLoad((options) => {
  if (options.orderId) {
    state.orderId = options.orderId
  }
})
onMounted(() => {
  // 当前显示的包裹信息
  fetchData()
})
// 控制物流信息的展开收起
const isExpanded = ref(false)
const maxDisplayCount = 3

// 根据展开状态返回要显示的物流数据
const showExpressData = computed(() => {
  if (!express.value?.express?.data) return []

  return isExpanded.value
    ? express.value?.express?.data
    : express.value?.express?.data.slice(0, maxDisplayCount)
})

// 切换展开/收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
//
//查单包裹物流
const fetchData = async () => {
  // express.value = expressObj.value
  uni.showLoading({
    title: '加载中',
  })
  try {
    let { data: detailData } = await UserOrderDetail({ orderId: Number(state.orderId) })
    orderDeliverList.value = detailData?.detail?.orderDeliverList
    console.log('orderDeliverList.value', orderDeliverList.value)

    detail.value.deliveryType = detailData?.detail?.deliveryType
    detail.value.orderNo = detailData?.detail?.orderNo

    if (orderDeliverList.value && orderDeliverList.value.length > 0) {
      orderDeliver.value = orderDeliverList.value[currentIndex.value]
    }
    if (detailData.detail.deliveryType == 10) {
      await getSingleData()
    } else if (detailData.detail.deliveryType == 40) {
      await getMultiData()
    }
    uni.hideLoading()
  } catch (e) {
    uni.hideLoading()
    console.log('error', e)
  }
}
//多包裹物流
const getMultiData = async () => {
  console.log('orderDeliver', orderDeliver.value)

  let { data: expressObj } = await fetchUserMultiExpressDetail({
    appId: import.meta.env.VITE_APPID,
    orderId: state.orderId,
    expressId: orderDeliver.value?.expressId,
    expressNo: orderDeliver.value?.expressNo,
  })
  express.value = expressObj
}

const getSingleData = async () => {
  let { data: expressObj } = await fetchUserSingleDataExpressDetail({
    appId: import.meta.env.VITE_APPID,
    orderId: state.orderId,
  })
  express.value = expressObj
}
//查单包裹物流
const fetchOrderDetail = async () => {
  uni.showLoading({
    title: '加载中',
  })
  try {
    let { data: detailData } = await UserOrderDetail({ orderId: Number(state.orderId) })
    orderDeliverList.value = detailData?.detail?.orderDeliverList

    detail.value.deliveryType = detailData?.detail?.deliveryType
    detail.value.orderNo = detailData?.detail?.orderNo

    if (orderDeliverList.value && orderDeliverList.value.length > 0) {
      orderDeliver.value = orderDeliverList.value[currentIndex.value]
    }
    uni.hideLoading()
  } catch (e) {
    uni.hideLoading()
  }
}
//选择单包裹物流
const changPackage = async (idx: number) => {
  await fetchOrderDetail()
  if (currentIndex.value == idx) return
  currentIndex.value = idx
  orderDeliver.value = orderDeliverList.value[currentIndex.value]
  await getMultiData()
}

const copy = (text: string) => {
  // 模拟复制功能
  console.log('复制内容:', text)
  uni.showToast({ title: '已复制' })
}

const openShowGoods = () => {
  product.value = orderDeliver.value.productList
  console.log('product.value', product.value)

  showGoods.value = true
}
const closeShowGoods = () => {
  showGoods.value = false
}
</script>

<style lang="scss" scoped>
//

.order {
  height: 94rpx;
  line-height: 94rpx;
  background: #ffffff;
  color: #666666;
  font-size: 24rpx;
  padding: 0 20rpx;
}

.package-box {
  padding: 20rpx;
  box-sizing: border-box;
}

.package-wrap {
  white-space: nowrap;
  width: 100%;
  margin-bottom: 20rpx;
}

.package-item {
  display: inline-block;
  width: 210rpx;
  height: 92rpx;
  line-height: 92rpx;
  background: #ffffff;
  border-radius: 15rpx;
  font-size: 30rpx;
  text-align: center;
  color: #333333;
  position: relative;
  margin-right: 20rpx;

  .iconfont {
    margin-right: 9rpx;
  }

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 7rpx;
    background: transparent;
    border-radius: 28rpx;
  }

  &.active {
    .iconfont {
      color: white;
    }
    .itemActive {
      background: #ff5704;
      color: white;
    }
    &::before {
      //   background: #ff5704;
    }
  }
}

.package-info {
  background: #ffffff;
  border-radius: 25rpx;
  overflow: hidden;

  .notice {
    height: 72rpx;
    background: #fff8ee;
    border-radius: 25rpx 25rpx 0px 0px;
    font-size: 24rpx;
    line-height: 38rpx;
    color: #ff5704;
    padding: 22rpx 19rpx;
  }

  .base-info {
    padding: 20rpx;

    .base-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      padding: 40rpx 0;
    }

    .base-order-no {
      font-size: 24rpx;
      color: #666666;
      padding-bottom: 29rpx;
      display: flex;
      align-items: center;
    }

    .base-product-list {
      border-top: 1px solid #eeeeee;
      padding-top: 20rpx;

      .product-img {
        width: 80rpx;
        height: 80rpx;
        border-radius: 15rpx;
        margin-right: 8rpx;
        display: inline-block;
      }

      .product-total {
        font-size: 24rpx;
        color: #666666;

        .iconfont {
          font-size: 24rpx;
          color: #090000;
        }
      }
    }

    .base-order-copy {
      margin-left: 34rpx;
      color: #333333;
    }
  }
}

.physical-wrap {
  background: #ffffff;
  margin-top: 25rpx;
  padding: 0 20rpx;

  .physical-title {
    font-size: 32rpx;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #333333;
    padding: 20rpx 0;
    border-bottom: 1px solid #eeeeee;
  }

  .step-wrap {
    padding-top: 29rpx;
    padding-bottom: 34rpx;
  }

  .step-item {
    position: relative;
    padding-left: 40rpx;

    &::before {
      content: '';
      position: absolute;
      left: 10rpx;
      width: 2rpx;
      height: 100%;
      background: #999;
    }

    .step-circle {
      width: 20rpx;
      height: 20rpx;
      background: #999;
      border-radius: 10rpx;
      position: absolute;
      top: 0;
      left: 0;
    }

    .step-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #979797;
    }

    .step-desc {
      padding: 12rpx 0;
      color: #a7a7a7;
    }

    &.active {
      .step-circle {
        background: #ff5704;
      }

      .step-title {
        color: #ff5704;
      }

      .step-desc {
        color: #333;
      }
    }
  }
}
//商品列表
.express-mask {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  .express-content {
    background: #ffffff;
    border-radius: 25rpx 25rpx 0px 0px;
    position: absolute;
    width: 100%;
    bottom: 0;
    padding: 0 26rpx;
    box-sizing: border-box;
    padding-bottom: 20rpx;
    .close-icon {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 32rpx;
      color: #999;
      padding: 30rpx;
    }
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      text-align: center;
      padding: 30rpx 0;
    }
    .desc {
      font-size: 28rpx;
      color: #999999;
    }
  }
  .product-list {
    height: 400rpx;
    padding: 30rpx 0;
    .product-item {
      height: 173rpx;
      display: flex;
      .product-r {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        height: 100%;
        padding: 20rpx;
        box-sizing: border-box;
      }
      .product-title {
        font-size: 28rpx;
        color: #333333;
      }
      .product-price {
        color: #f6220c;
      }
      .product-num {
        color: #999999;
      }
    }
  }
}
.express-btn {
  height: 88rpx;
  line-height: 88rpx;
  background: #ff6904;
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  text-align: center;
}

// 物流步骤条样式
.logistics-steps-container {
  background: #ffffff;
  border-radius: 25rpx;
  margin-top: 20rpx;
  padding: 30rpx 20rpx;

  .logistics-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
  }

  .logistics-time {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 6rpx;
  }

  .logistics-content {
    display: flex;
    flex-direction: column;
  }

  .logistics-status {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 6rpx;
  }

  .logistics-context {
    font-size: 26rpx;
    color: #666666;
    line-height: 36rpx;
  }

  .expand-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    color: #ff5704;
    font-size: 28rpx;
    margin-top: 10rpx;

    .iconfont {
      margin-left: 6rpx;
      font-size: 24rpx;
    }
  }
}

// 覆盖Wot UI的默认样式
:deep(.wd-steps__item) {
  &.is-process {
    .wd-step__dot {
      background-color: #ff5704;
    }

    .wd-step__title,
    .wd-step__description {
      color: #333333;
    }
  }

  &.is-finished {
    .wd-step__dot {
      background-color: #cccccc;
    }
  }
}
</style>
