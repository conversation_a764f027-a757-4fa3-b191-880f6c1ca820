<template>
  <wd-config-provider :customStyle="`height: ${winHeight}px;`" :themeVars="themeVars">
    <div class="size-full flex flex-col">
      <slot />
      <TabBar
        v-if="tabs.findIndex((item) => item.url.replace(/^\//, '') === currPage) > -1"
        class="flex-1"
      />
    </div>

    <wd-toast />
    <wd-message-box />

    <wd-popup
      v-model="privateInfo"
      :closeOnClickModal="false"
      custom-style="border-radius:20rpx;width:80%"
      @close="handleClosePrivate"
    >
      <div class="w-full p-40rpx box-border flex flex-col items-center gap-y-20px">
        <wd-img
          width="80rpx"
          height="80rpx"
          src="https://file.shanqianqi.com/image/2025/06/07/4f74b9c1a14e47ddab39aed1ae57c378.png"
          mode="aspectFill"
        ></wd-img>
        <div class="text-30rpx font-bold">用户隐私协议</div>
        <div class="flex flex-col gap-y-1px">
          <div class="w-full indent-md text-24rpx text-#999999">亲爱的会员，欢迎来到善新年</div>
          <div class="w-full indent-md text-24rpx text-#999999">
            为保障您的权利我们更新了
            <span @click="openAgreement" class="text-#FF7D26">《隐私政策》</span>
            。上版
            <span @click="openAgreement" class="text-#FF7D26">《隐私政策》</span>
            为保障您的权利我们更新了
            <span @click="openAgreement" class="text-#FF7D26">《隐私政策》</span>
            。此版
            <span @click="openAgreement" class="text-#FF7D26">《隐私政策》</span>
            的更新主要是向您明示可能涉及的第三方数据共享的情况。
          </div>
          <div class="w-full indent-md text-24rpx text-#999999">
            请您务必在注册或登录过程中仔细阅读、充分理解
            <span @click="openAgreement" class="text-#FF7D26">《隐私政策》</span>
            的条款内容，若您不同意上述
            <span @click="openAgreement" class="text-#FF7D26">《隐私政策》</span>
            内容，请您立即停止注册登录或使用善新年。若您继续登录使用善新年，将视为您已理解并同意更新的内容。
          </div>
        </div>
        <div class="flex items-center justify-between gap-x-10px">
          <wd-button @click="handleCancel" plain>不接受,退出</wd-button>
          <wd-button
            @click="privateInfo = !privateInfo"
            openType="getPhoneNumber"
            @getphonenumber="handleGetPhoneNumber"
          >
            接受
          </wd-button>
        </div>
      </div>
    </wd-popup>
  </wd-config-provider>
</template>

<script lang="ts" setup>
import TabBar from '@/components/TabBar/index.vue'
import { useUserStore, useLoginSettingStore } from '@/store'
import type { ConfigProviderThemeVars } from 'wot-design-uni'

const userStore = useUserStore()
const loginSettingStore = useLoginSettingStore()
const themeVars: ConfigProviderThemeVars = {
  colorTheme: '#FF7D26',
  buttonPrimaryBgColor: '#FF7D26',
  buttonPrimaryColor: '#ffffff',
}

const winHeight = ref<number>()

const currPage = computed(() => {
  const pages = getCurrentPages()
  return pages[pages.length - 1]?.route
})

const tabs = computed(() => [
  { url: '/pages/index/index' },
  { url: '/pages/message/index' },
  { url: '/pages-sub/home/<USER>/index' },
  { url: '/pages/my/index' },
])

onLoad(() => {
  uni.getSystemInfo({
    success: (res) => {
      winHeight.value = res.windowHeight
    },
  })
})

const privateInfo = ref(false)

const handleClosePrivate = () => {
  privateInfo.value = false
}

const env = ref('')
//#ifdef MP-WEIXIN
const {
  miniProgram: { envVersion },
} = uni.getAccountInfoSync()
env.value = envVersion

watch(
  [() => userStore?.userMobile, () => loginSettingStore?.loginSetting?.isAudit],
  ([v, isAudit]) => {
    if (currPage.value !== 'pages/index/index' && currPage.value !== 'pages/my/index') return
    if (!v && !isAudit && env.value !== 'trial') {
      privateInfo.value = true
    } else {
      privateInfo.value = false
    }
  },
  { immediate: true },
)
//#endif

const handleCancel = () => {
  privateInfo.value = !privateInfo.value
  userStore?.setUserCancelPrivateInfo(!userStore?.userCancelPrivateInfo)
}

const handleGetPhoneNumber = (e: {
  cloudID: string
  code: string
  encryptedData: string
  errMsg: string
  iv: string
}) => {
  if (e.errMsg === 'getPhoneNumber:ok') {
    userStore.fetchUserMobile(e)
  }
}

// 打开协议页面
const openAgreement = () => {
  uni.navigateTo({
    url: `/pages-sub/webview/ue?type=privacy`,
  })
}
</script>
