<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '确认订单',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full grid grid-cols-1 grid-auto-rows-min gap-y-10px">
    <SimpleNavBar title="确认订单" />
    <!-- 收货地址 -->
    <template v-if="!storeId">
      <div
        @click="handleGoAddress"
        v-if="OrderInfo?.orderData?.address"
        class="w-full bg-#ffffff py20rpx px30rpx flex items-center justify-between box-border"
      >
        <div class="flex items-center gap-x-10px">
          <div class="i-carbon-location text-#FF7D26 text-44rpx"></div>
          <div class="flex flex-col gap-y-5px">
            <div class="text-#666666 text-28rpx font-bold">
              {{
                `${Object.values(OrderInfo?.orderData?.address?.region).join('')} ${OrderInfo?.orderData?.address?.detail}`
              }}
            </div>
            <span class="text-22rpx text-#999999">
              {{ `${OrderInfo?.orderData?.address?.name} ${OrderInfo?.orderData?.address?.phone}` }}
            </span>
          </div>
        </div>
        <div class="i-carbon-chevron-right text-#333333"></div>
      </div>
      <div
        v-else
        @click="handleGoAddress"
        class="w-full bg-#ffffff py20rpx px30rpx flex items-center justify-between box-border"
      >
        <div class="flex items-center gap-x-10px">
          <div class="i-carbon-location text-#FF7D26 text-44rpx"></div>
          <div class="flex flex-col gap-y-5px">
            <div class="text-#666666 text-28rpx font-bold">请选择收货地址</div>
          </div>
        </div>
        <div class="i-carbon-chevron-right text-#333333"></div>
      </div>
    </template>

    <!-- 确认信息 -->
    <div
      v-for="shop in OrderInfo?.supplierList"
      :key="shop?.shopSupplierId"
      class="grid grid-cols-1"
    >
      <!-- 信息 -->
      <div class="py-30rpx bg-#ffffff box-border grid grid-cols-1">
        <div class="px-30rpx grid grid-cols-1 box-border gap-y-40rpx">
          <div class="flex items-center gap-x-15rpx">
            <div class="i-carbon-store text-28rpx text-#333333"></div>
            <div class="text-#333333 text-28rpx">
              {{ shop?.supplier?.name }}
            </div>
          </div>
          <div class="flex flex-col gap-y-10px">
            <div
              v-for="good in shop?.productList"
              :key="good?.productId"
              class="flex justify-between"
            >
              <div class="flex gap-x-10px">
                <wd-img
                  width="150rpx"
                  height="150rpx"
                  :src="good?.productImage"
                  mode="aspectFit"
                ></wd-img>
                <div class="flex flex-col gap-y-5px">
                  <span class="text-32rpx text-#333333">{{ good?.productName }}</span>
                  <span class="text-#FF7D26 text-26rpx">¥{{ good?.productPrice }}</span>
                  <span class="text-24rpx text-#999999">{{ good?.productSku?.productAttr }}</span>
                </div>
              </div>
              <span class="text-22rpx">×{{ good?.totalNum }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 配送方式 -->
      <div
        v-if="shop?.orderData?.delivery !== 30 && !storeId"
        class="bg-#ffffff box-border grid grid-cols-1"
      >
        <!-- @click="showDeliveryMap[shop?.shopSupplierId] = !showDeliveryMap[shop?.shopSupplierId]" -->
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 box-border border-b border-b-solid border-b-#eeeeee"
        >
          <wd-transition :show="shop?.orderData?.delivery === 10" name="fade-down">
            <div class="flex items-center justify-between text-26rpx text-#333333">
              <div class="flex items-center gap-x-30rpx">
                <span>配送方式</span>
                <span class="text-#9e9e9e">普通快递</span>
              </div>
              <div class="flex items-center gap-x-15rpx">
                <span>
                  {{
                    shop?.orderData?.expressPrice !== '0.00'
                      ? '￥ ' + shop?.orderData.expressPrice
                      : '快递 免费'
                  }}
                </span>
                <!-- <div class="i-carbon-chevron-right text-#333333"></div> -->
              </div>
            </div>
          </wd-transition>

          <wd-transition :show="shop?.orderData?.delivery === 20" name="fade-down">
            <div class="flex items-center justify-between text-26rpx text-#333333">
              <div class="flex items-center gap-x-10px">
                <div class="p10rpx box-border bg-#FF7D26 text-#ffffff rd-8rpx">当前自提点</div>
                <span>
                  {{ storeIdMap?.[shop?.shopSupplierId]?.province
                  }}{{ storeIdMap?.[shop?.shopSupplierId]?.city
                  }}{{ storeIdMap?.[shop?.shopSupplierId]?.region }}
                </span>
              </div>
              <div class="i-carbon-chevron-right text-#333333"></div>
            </div>
          </wd-transition>
        </div>
      </div>
      <div
        v-if="shop?.orderData?.delivery === 30 && !storeId"
        class="bg-#ffffff box-border grid grid-cols-1"
      >
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 box-border border-b border-b-solid border-b-#eeeeee"
        >
          <div class="flex items-center justify-between text-26rpx text-#333333">
            <div class="flex items-center gap-x-30rpx">
              <span>虚拟商品：无需物流</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 优惠券 -->
      <!-- <div class="bg-#ffffff box-border grid grid-cols-1">
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 box-border border-b border-b-solid border-b-#eeeeee"
        >
          <div class="flex items-center justify-between text-26rpx text-#333333">
            <div class="flex items-center gap-x-30rpx">
              <span>优惠券</span>
            </div>
            <div class="flex items-center gap-x-15rpx">
              <span class="text-#999999">无优惠券可用</span>
            </div>
          </div>
        </div>
      </div> -->
      <!-- 善豆抵扣金额 -->
      <wd-transition
        :show="
          pointChecked &&
          !OrderInfo?.settledRule?.forcePoints &&
          Number(shop?.orderData?.pointsMoney) > 0
        "
        name="fade-down"
      >
        <div class="bg-#ffffff box-border grid grid-cols-1 transition-all-300">
          <div
            class="mx-30rpx py-30rpx grid grid-cols-1 box-border border-b border-b-solid border-b-#eeeeee"
          >
            <div class="flex items-center justify-between text-26rpx text-#333333">
              <div class="flex items-center gap-x-30rpx">
                <span>善豆抵扣金额</span>
              </div>
              <div class="flex items-center gap-x-15rpx">
                <span class="text-#FF7D26">-￥{{ shop?.orderData?.pointsMoney }}</span>
              </div>
            </div>
          </div>
        </div>
      </wd-transition>

      <!-- 订单备注 -->
      <div class="bg-#ffffff box-border grid grid-cols-1">
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 box-border border-b border-b-solid border-b-#eeeeee"
        >
          <div class="flex flex-col gap-y-20rpx text-26rpx text-#333333">
            <div class="flex items-center gap-x-30rpx">
              <span>订单备注</span>
            </div>
            <wd-textarea
              clearable
              custom-style="border-color:#eeeeee"
              :maxlength="200"
              show-word-limit
              custom-class="!border !border-solid  !rd-30rpx"
              v-model="storeData[shop.shopSupplierId].remark"
              placeholder="选填,请先和商家协商一致"
            />
          </div>
        </div>
      </div>
      <!-- 实付款 -->
      <div class="bg-#ffffff box-border grid grid-cols-1">
        <div class="mx-30rpx py-30rpx grid grid-cols-1 box-border">
          <div class="flex items-center justify-between text-26rpx text-#333333">
            <div class="flex items-baseline text-#333333">
              <span class="text-26rpx">共{{ shop?.productList.length }}件商品,总价:</span>
              <span class="text-20rpx">￥</span>
              <span class="text-26rpx">{{ shop?.orderData?.orderTotalPrice }}</span>
            </div>
            <div v-if="!OrderInfo?.settledRule?.forcePoints" class="flex items-baseline">
              <span class="text-26rpx text-#333333">实付款：</span>
              <span class="text-#FF7D26">￥</span>
              <span class="text-#FF7D26 text-16px">{{ shop?.orderData?.orderPayPrice }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 配送方式popup -->
      <wd-popup
        v-model="showDeliveryMap[shop?.shopSupplierId]"
        position="bottom"
        safe-area-inset-bottom
        lockScroll
        custom-style="min-height: 200px;max-height:600px;"
        @close="() => handleDeliveryClose(shop?.shopSupplierId)"
      >
        <div class="grid grid-cols-1 grid-auto-rows-min">
          <div class="w-full py70rpx box-border text-39rpx flex items-center justify-center">
            配送方式
          </div>

          <!-- <div class="flex flex-col">
          <div class="flex items-center justify-between text-24rpx text-#333333">
            <span>普通配送</span>
          </div>
        </div> -->

          <div class="p25rpx box-border flex flex-col gap-y-20px">
            <wd-radio-group v-model="deliveryTypeMap[shop.shopSupplierId]">
              <wd-radio
                custom-class="!border-b !border-b-solid !border-b-#eeeeee !py25rpx !box-border"
                :value="10"
              >
                普通配送
              </wd-radio>
              <wd-radio custom-class=" !py25rpx !box-border" :value="20">自提门店</wd-radio>
            </wd-radio-group>

            <!-- 自提点 -->
            <template v-if="deliveryTypeMap[shop.shopSupplierId] === 20">
              <div
                v-if="!storeIdMap?.[shop?.shopSupplierId]"
                @click="() => handlePickupOpen(shop?.shopSupplierId)"
                class="flex items-center justify-between pb25px box-border"
              >
                <div class="flex items-center gap-x-5px">
                  <div class="i-carbon-location text-16px text-#999999"></div>
                  <span class="text-14px text-#333333">请选择自提点</span>
                </div>
                <div class="i-carbon-chevron-right text-#333333"></div>
              </div>

              <div
                v-else
                @click="() => handlePickupOpen(shop?.shopSupplierId)"
                class="flex items-center justify-between text-26rpx text-#333333"
              >
                <div class="flex flex-col gap-y-10px">
                  <div class="flex items-center gap-x-16rpx">
                    <div class="p10rpx box-border bg-#FF7D26 text-#ffffff rd-8rpx">当前自提点</div>
                    <span>
                      {{ storeIdMap?.[shop?.shopSupplierId]?.province
                      }}{{ storeIdMap?.[shop?.shopSupplierId]?.city
                      }}{{ storeIdMap?.[shop?.shopSupplierId]?.region }}
                    </span>
                  </div>
                  <span class="text-32rpx font-bold text-#333333">
                    {{ storeIdMap?.[shop?.shopSupplierId]?.storeName }}
                  </span>
                  <span class="text-24rpx">{{ storeIdMap?.[shop?.shopSupplierId]?.phone }}</span>
                </div>

                <div class="i-carbon-chevron-right text-#333333"></div>
              </div>
            </template>
          </div>

          <wd-button
            @click="() => handleDeliveryComfirm(shop?.shopSupplierId)"
            custom-class="!mx-25rpx"
          >
            确认
          </wd-button>
        </div>
      </wd-popup>

      <!-- 自提点popup -->
      <wd-popup
        position="bottom"
        safe-area-inset-bottom
        lockScroll
        custom-style="min-height: 100px;max-height:500px;"
        v-model="showPickupMap[shop?.shopSupplierId]"
        @close="() => handlePickupClose(shop?.shopSupplierId)"
      >
        <div class="grid grid-cols-1 gap-y-10px p10px box-border">
          <div class="w-full py20px box-border text-39rpx flex items-center justify-center">
            自提点
          </div>
          <div
            v-for="item in singlePickupList?.[shop?.shopSupplierId] ?? []"
            :key="item?.storeId"
            class="flex items-center justify-between"
            @click="() => handleChooseStore(shop?.shopSupplierId, item)"
          >
            <div
              :class="
                storeIdMap?.[shop?.shopSupplierId]?.storeId === item?.storeId ? 'text-#FF7D26' : ''
              "
              class="flex flex-col p-10px box-border gap-y-16rpx border-b border-b-solid text-#333333 transition-all-300 border-b-#eeeeee"
            >
              <span class="text-32rpx font-bold">{{ item?.storeName }}</span>
              <span class="text-24rpx">{{ item?.phone }}</span>
              <span class="text-24rpx">
                {{ item?.province }}{{ item?.city }}{{ item?.region }}{{ item?.address }}
              </span>
            </div>
            <div
              v-if="storeIdMap?.[shop?.shopSupplierId]?.storeId === item?.storeId"
              class="i-carbon-checkmark text-24px text-#FF7D26"
            ></div>
          </div>
        </div>
      </wd-popup>
    </div>
    <!-- 确认订单信息 -->
    <div class="grid grid-cols-1">
      <!-- 订单总金额： -->
      <div class="bg-#ffffff box-border grid grid-cols-1">
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 gap20rpx box-border border-b border-b-solid border-b-#eeeeee"
        >
          <div
            class="flex items-center justify-between text-28rpx text-#333333"
            @click="handleShow"
          >
            <div class="flex items-center gap-x-30rpx">
              <span>订单总金额：</span>
            </div>
            <div class="flex items-center gap-x-15rpx">
              <span class="text-#FF7D26">￥{{ OrderInfo?.orderData?.orderTotalPrice }}</span>
              <view class="i-carbon-chevron-right"></view>
            </div>
          </div>
          <view
            v-if="OrderInfo?.orderData?.productReduceMoney > 0"
            class="flex justify-between items-center gap-x-30rpx text-28rpx"
          >
            <text class="">商品立减：</text>
            <view>
              <text class="flex items-center gap-x-15rpx text-24rpx">
                -￥{{ OrderInfo?.orderData?.productReduceMoney }}
              </text>
            </view>
          </view>
        </div>
      </div>
      <!-- 用户当前可用善豆：商圈订单不显示 -->
      <div v-if="!storeId" class="bg-#ffffff box-border grid grid-cols-1">
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 gap20rpx box-border border-b border-b-solid border-b-#eeeeee"
        >
          <div class="flex items-center justify-between text-28rpx text-#333333">
            <div class="flex items-center gap-x-30rpx">
              <span>用户当前可用善豆：</span>
            </div>
            <div class="flex items-center gap-x-15rpx">
              <span class="text-#FF7D26">{{ OrderInfo?.points }}</span>
              <span class="text-#FF7D26">个</span>
            </div>
          </div>
          <view
            v-if="OrderInfo?.orderData?.productReduceMoney > 0"
            class="flex justify-between items-center gap-x-30rpx text-26rpx"
          >
            <text class="">商品立减：</text>
            <view>
              <text class="flex items-center gap-x-15rpx text-24rpx">
                -￥{{ OrderInfo?.orderData?.productReduceMoney }}
              </text>
            </view>
          </view>
        </div>
      </div>
      <!-- 用户当前可用红包： -->
      <div class="bg-#ffffff box-border grid grid-cols-1">
        <div
          class="mx-30rpx py-30rpx grid grid-cols-1 gap20rpx box-border border-b border-b-solid border-b-#eeeeee"
        >
          <div class="flex items-center justify-between text-28rpx text-#333333">
            <div class="flex items-center gap-x-30rpx">
              <span>用户当前可用红包：</span>
            </div>
            <div class="flex items-center gap-x-15rpx">
              <span class="text-#FF7D26">¥ {{ OrderInfo?.redPacket }}</span>
            </div>
          </div>
          <view
            v-if="OrderInfo?.orderData?.productReduceMoney > 0"
            class="flex justify-between items-center gap-x-30rpx text-26rpx"
          >
            <text class="">商品立减：</text>
            <view>
              <text class="flex items-center gap-x-15rpx text-24rpx">
                -￥{{ OrderInfo?.orderData?.productReduceMoney }}
              </text>
            </view>
          </view>
        </div>
      </div>
      <!-- 抵扣善豆 -->
      <!-- OrderInfo?.orderData?.pointsMoney !== '0.00' -->
      <div v-if="false" class="bg-#ffffff box-border grid grid-cols-1">
        <div class="mx-30rpx py-30rpx grid grid-cols-1 box-border">
          <div class="flex items-center justify-between text-28rpx text-#333333">
            <div class="flex items-baseline text-#333333">
              <span class="text-26rpx">可用善豆抵扣：</span>
            </div>
            <div class="flex items-center gap-x-15rpx">
              <span class="text-#FF7D26">
                -￥{{ toDecimal(OrderInfo?.orderData?.pointsMoney) }}
              </span>
              <wd-switch @change="handlePointCheck" v-model="pointChecked" size="18px" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- btn -->
    <div
      class="h120rpx w-full"
      :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' || 30 + 'px' }"
    ></div>
    <div
      class="h120rpx w-full fixed bottom-0 left-0 bg-#ffffff flex items-center"
      :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' || 30 + 'px' }"
    >
      <div class="flex items-center justify-between px-40rpx box-border w-full">
        <div class="flex items-baseline text-#FF7D26 gap-x-2px">
          ¥
          <span class="text-44rpx font-bold">{{ OrderInfo?.orderData?.orderPayPrice }}</span>
        </div>
        <wd-button @click="goPay" custom-class="!m-unset">提交订单</wd-button>
      </div>
    </div>

    <!-- 付款明细 -->
    <wd-popup
      v-model="showPaymentDetails"
      position="bottom"
      custom-style="min-height: 200px;max-height:600px;"
      custom-class="p30rpx rounded-30rpx flex flex-col justify-between "
      :safe-area-inset-bottom="true"
      @close="handleClose"
    >
      <view class="">
        <!-- <view class="py20rpx" v-if="detail.orderSource == 80">
          <text class="">定金：</text>
          <text>￥{{ detail.advance.payPrice }}</text>
        </view> -->

        <view v-if="OrderInfo?.orderData?.expressPrice" class="py20rpx flex justify-between">
          <text class="">运费</text>
          <text>¥ {{ OrderInfo?.orderData?.expressPrice }}</text>
        </view>
        <!-- <view class="py20rpx flex justify-between">
          <text class="">善豆实付</text>
          <text>¥ {{ detail.payShanBean }}</text>
        </view> -->
        <view v-if="OrderInfo?.orderData?.balancePayment" class="py20rpx flex justify-between">
          <text class="">余额实付</text>
          <text>¥ {{ OrderInfo?.orderData?.balancePayment }}</text>
        </view>
        <!-- <view class="py20rpx flex justify-between">
          <text class="">银行卡实付</text>
          <text>¥ {{ detail.onlineMoney }}</text>
        </view> -->
        <!-- <view class="py20rpx flex justify-between">
          <text class="">红包实付</text>
          <text>¥ {{ detail.payRedPocket }}</text>
        </view> -->
        <view class="py20rpx flex justify-between">
          <text class="">实付</text>
          <text>¥ {{ OrderInfo?.orderData?.orderPayPrice }}</text>
        </view>
        <!-- <view class="py20rpx" v-if="detail.payPrice && detail.orderSource == 80">
          <text class="">尾款：</text>
          <text>￥{{ detail.payPrice }}</text>
        </view> -->
        <!-- <view class="py20rpx" v-if="detail.advance && detail.advance.reduceMoney > 0">
          <text class="">尾款立减</text>
          <text>-¥ {{ detail.advance.reduceMoney }}</text>
        </view> -->
        <!-- <view class="py20rpx" v-if="detail.orderSource == 20">
          <text class="">扣除{{ pointsName }}数：</text>
          <text>-{{  OrderInfo?.orderData?.pointsNum }}{{ pointsName }}</text>
        </view> -->
        <!-- <view class="py20rpx">
				<text class="">订单总额</text>
				<text>¥ {{ detail.totalPrice }}</text>
			</view> -->
        <!-- <view class="py20rpx" v-if="detail.updatePrice != '0.00'">
          <text class="gray9">订单差价</text>
          <text>¥ {{ detail.updatePrice }}</text>
        </view> -->
        <view class="py20rpx" v-if="OrderInfo?.orderData?.productReduceMoney > 0">
          <text class="">商品立减</text>
          <text>-¥ {{ OrderInfo?.orderData?.productReduceMoney }}</text>
        </view>
        <view
          class="py20rpx flex justify-between"
          v-if="Number(OrderInfo?.supplierList[0]?.orderData?.reduce?.reducedPrice) > 0"
        >
          <text class="">满减</text>
          <text>-¥ {{ OrderInfo?.supplierList[0]?.orderData?.reduce?.reducedPrice }}</text>
        </view>
        <!-- <view class="py20rpx" v-if="Number(OrderInfo?.orderData?.pointsMoney) > 0">
          <text class="">{{ pointsName }}抵扣</text>
          <text>-¥ {{ OrderInfo?.orderData?.pointsMoney }}</text>
        </view> -->
        <!-- <view class="py20rpx" v-if="OrderInfo?.orderData?.couponMoney > 0">
          <text class="">商户优惠券</text>
          <text class="theme-price">- ¥ {{ OrderInfo?.orderData?.couponMoney }}</text>
        </view>
        <view class="py20rpx" v-if="OrderInfo?.orderData?.couponMoneySys > 0">
          <text class="">平台优惠券</text>
          <text class="theme-price">- ¥ {{ OrderInfo?.orderData?.couponMoneySys }}</text>
        </view> -->
      </view>
      <view class="flex justify-end">
        <wd-button @click="handleClose" custom-class="!mx-25rpx">确认</wd-button>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import { cartOrderBuy, cartPreBuy, fetchStoreList, orderBuy, preBuy } from '@/service'
import { useUserStore } from '@/store'
import { useConfirmOrderStore } from '@/store/confirm-order'
import { useLocationStore } from '@/store/location'
import { toDecimal } from '@/utils'
import { storeToRefs } from 'pinia'
import { getPlatform } from '@/utils'

const userStore = useUserStore()

const locationStore = useLocationStore()

const confirmOrderStore = useConfirmOrderStore()

defineOptions({
  name: 'ConfirmOrder',
})

// let pointsName = ref('善豆')
const orderType = ref('')
let showPaymentDetails = ref(false)

const storeId = ref('')

onLoad((option) => {
  if (option) {
    orderType.value = option?.orderType ?? ''
    if (option?.storeId === '0') {
      storeId.value = ''
    } else {
      storeId.value = option?.storeId
    }
  }
})

const { OrderInfo, GoodInfo, buyNum, specSkuId } = storeToRefs(useConfirmOrderStore())
const { safeAreaInsets } = uni.getSystemInfoSync()

// 配送方式
const deliveryTypeMap = ref<Record<number, number>>({})

const showDeliveryMap = ref<Record<number, boolean>>({})

const showPickupMap = ref<Record<number, boolean>>({})

OrderInfo?.value?.supplierList.forEach((item) => {
  // 配送方式
  deliveryTypeMap.value[item?.shopSupplierId] = item?.orderData?.delivery

  // 配送方式pop
  showDeliveryMap.value[item?.shopSupplierId] = false

  // 自提点pop
  showPickupMap.value[item?.shopSupplierId] = false
})

const handleDeliveryClose = (id: number) => {
  showDeliveryMap.value[id] = false
}

const handleDeliveryComfirm = (id: number) => {
  updateOrder(id)
  showDeliveryMap.value[id] = false
}

// 自提点

const singlePickupList = ref<Record<number, Api.Order.StoreListItem[]>>({})

const storeIdMap = ref<Record<number, Api.Order.StoreListItem>>({})

const handlePickupOpen = async (shopId: number) => {
  showPickupMap.value[shopId] = !showPickupMap.value[shopId]

  try {
    const { data } = await fetchStoreList({
      appId: import.meta.env.VITE_APPID,
      token: userStore.token,
      longitude: locationStore.locationInfo?.location?.lon,
      latitude: locationStore.locationInfo?.location?.lat,
      shopSupplierId: shopId,
    })

    singlePickupList.value[shopId] = data
  } catch (error) {}
}
//打开金额明细
const handleShow = () => {
  showPaymentDetails.value = true
}
//关闭付款明细模态框
const handleClose = () => {
  showPaymentDetails.value = false
  console.log('handleClose', showPaymentDetails.value)
}
const handlePickupClose = (shopId: number) => {
  showPickupMap.value[shopId] = false
}

const handleChooseStore = (id: number, item: Api.Order.StoreListItem) => {
  storeIdMap.value[id] = item

  showPickupMap.value[id] = false
}

// 店铺信息
const storeData = computed(() => {
  const result = {}
  const supplierList = OrderInfo?.value?.supplierList || []
  for (const item of supplierList) {
    const key = item.shopSupplierId
    result[key] = {
      couponId: item?.orderData?.couponId ?? '',
      delivery: storeId.value ? 20 : deliveryTypeMap.value?.[key] ?? 10,
      storeId: storeId.value ? storeId.value : storeIdMap.value?.[key]?.storeId ?? 0,
      remark: '',
    }
  }
  return result
})

const pointChecked = ref<boolean>(true)

const updateOrder = async (shopId?: number) => {
  // 购物车
  if (orderType.value === 'cart') {
    const dataBody = {
      appId: import.meta.env.VITE_APPID,
      delivery: deliveryTypeMap.value?.[shopId],
      cartIds: uni.getStorageSync('CheckedData').join(',') || [].join(','),
      isUsePoints: Number(pointChecked.value),
      paySource: getPlatform(),
      storeId: shopId ?? 0,
      supplier: storeData.value,
    }

    try {
      const { data } = await cartPreBuy(dataBody)

      confirmOrderStore.setOrderInfo(data)
    } catch (error) {}
  }
  // 直接购买
  else {
    const dataBody = {
      appId: import.meta.env.VITE_APPID,
      delivery: deliveryTypeMap.value?.[shopId],
      isUsePoints: Number(pointChecked.value),
      paySource: getPlatform(),
      productId: `${GoodInfo.value?.detail?.productId}`,
      productNum: `${buyNum.value}`,
      storeId: shopId ?? 0,
      specSkuId: specSkuId.value ?? '0',
      supplier: storeData.value,
    }
    confirmOrderStore.setBuyNum(buyNum.value)

    try {
      const { data } = await preBuy(dataBody)

      confirmOrderStore.setOrderInfo(data)
    } catch (error) {}
  }
}

const handlePointCheck = () => updateOrder()

const preGo = (data: string | string[]) => {
  let ids = ''
  if (Array.isArray(data)) {
    ids = encodeURIComponent(data.join(','))
  } else {
    ids = data
  }
  uni.navigateTo({
    url: `/pages-sub/goods/pay-order/index?orderId=${ids}&storeId=${storeId.value}`,
  })
}

const goPay = async () => {
  if (!storeId.value && !confirmOrderStore?.OrderInfo?.orderData?.address) {
    uni.showToast({
      title: '请先添加收货地址',
      icon: 'none',
      mask: true,
    })
    return
  }
  // 购物车订单
  if (orderType.value === 'cart' && uni.getStorageSync('CheckedData').length) {
    const payBody = {
      appId: import.meta.env.VITE_APPID,
      isUsePoints: Number(pointChecked.value),
      cartIds: uni.getStorageSync('CheckedData').join(',') || [].join(','),
      supplier: storeData.value,
    }

    try {
      const { data } = await cartOrderBuy(payBody)
      preGo(data)
    } catch (error) {}
  }
  // 直接购买
  else {
    const payBody = {
      appId: import.meta.env.VITE_APPID,
      isUsePoints: Number(pointChecked.value),
      productId: `${GoodInfo.value?.detail?.productId}`,
      productNum: `${buyNum.value}`,
      specSkuId: specSkuId.value ?? '0',
      supplier: storeData.value,
    }
    console.log('🚀 ~ goPay ~ payBody.specSkuId:', payBody.specSkuId)
    // if(storeId.value){
    //   payBody.supplier
    // }
    try {
      const { data } = await orderBuy(payBody)
      preGo(data)
    } catch (error) {}
  }
}

const handleGoAddress = () => {
  if (OrderInfo.value?.orderData?.existAddress) {
    uni.navigateTo({
      url: '/pages-sub/user/receivingAddress/receivingAddress?source=order&delta=1',
    })
  } else {
    uni.navigateTo({ url: '/pages-sub/user/receivingAddress/addReceivingAddress?delta=1' })
  }
}
</script>

<style lang="scss" scoped>
//
</style>
