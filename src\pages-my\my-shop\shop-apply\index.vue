<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    class="size-full grid grid-cols-1 grid-rows-[auto_auto_1fr_auto] box-border"
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
  >
    <SimpleNavBar title="商户入驻" />

    <div class="w-full bg-#ffffff p-10px box-border relative">
      <wd-steps :active="step" align-center>
        <wd-step v-for="(item, index) in steps" :key="item?.label" description=" ">
          <template #title>
            <span :class="step === index ? 'text-#FF7D26' : 'text-#999999'">
              {{ item?.label }}
            </span>
          </template>
          <template #icon>
            <wd-img
              width="48rpx"
              height="48rpx"
              mode="aspectFill"
              :src="step === index ? item?.activeIcon : item?.icon"
            ></wd-img>
          </template>
        </wd-step>
      </wd-steps>

      <div class="grid grid-cols-5 size-full absolute top-0 left-0 z-10">
        <div @click="handleStep(index)" v-for="(item, index) in steps" :key="item?.label"></div>
      </div>
    </div>

    <div class="grid grid-cols-1 px10px pb-10px grid-rows-[auto_1fr] box-border of-auto">
      <div class="py10px text-#333333 text-32rpx font-bold flex items-center justify-between">
        {{ steps?.[step]?.label }}
        <wd-button
          v-if="step !== 0 && type !== 'addMer' && type !== 'edit'"
          @click="helpPop = !helpPop"
          custom-class="!rd-10rpx "
        >
          <div class="flex items-center gap-x-10rpx">
            <wd-img
              width="32rpx"
              height="32rpx"
              mode="aspectFill"
              src="https://file.shanqianqi.com/image/2025/06/26/03ae3ba3f3d141fcacf0079065540a5e.png"
            ></wd-img>
            帮忙进件
          </div>
        </wd-button>
      </div>
      <template v-for="(model, index) in formMap" :key="index">
        <wd-form
          v-if="step === index"
          :model="model"
          :rules="formRules[index]"
          :ref="(el) => setFormRef(index, el)"
        >
          <!-- 账号信息 -->
          <template v-if="step === 0">
            <div class="grid grid-cols-1 gap-y-10px">
              <wd-cell-group title="" border>
                <wd-input
                  label="手机号"
                  type="tel"
                  inputmode="tel"
                  @blur="handleMobileBlur"
                  label-width="100px"
                  :maxlength="11"
                  prop="mobile"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.AccountForm).mobile"
                  placeholder="请输入手机号"
                />

                <wd-input
                  label="登录账号"
                  label-width="100px"
                  prop="userName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.AccountForm).userName"
                  placeholder="请输入登录账号"
                />
                <span class="px26rpx text-22rpx text-red font-bold">
                  用于登录PC端管理后台，请务必牢记!(最好使用手机号)
                </span>
                <wd-input
                  label="登录密码"
                  label-width="100px"
                  prop="password"
                  show-password
                  required
                  clearable
                  v-model="(model as Api.ShopApply.AccountForm).password"
                  placeholder="请输入登录密码"
                />
                <span class="px26rpx text-22rpx text-red font-bold">
                  长度至少为8位，要求包括数字，字母，大小写
                </span>
              </wd-cell-group>
            </div>
          </template>
          <!-- 法人信息 -->
          <template v-if="step === 1">
            <div class="grid grid-cols-1 gap-y-10px">
              <!-- 法人身份证人面像 -->
              <wd-cell-group title="法人身份证人面像" border>
                <wd-cell required title="请上传身份证人面像" prop="identityImag1">
                  <!--  @success="handleIdCardFrontImgSuccess" -->
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.PersonalForm).forShowidentityImag1 as UploadFile[]
                    "
                    :upload-method="uploadIdCardFront"
                    name="iFile"
                    :limit="1"
                    @success="handleIdCardFrontImgSuccess"
                    @change="handleIdCardFrontImgChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/10/477deac72e994d209074cc1cf94211a0.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 法人身份证国徽像 -->
              <wd-cell-group title="法人身份证国徽像" border>
                <wd-cell required title="请上传身份证国微像" prop="identityImag2">
                  <!-- @success="handleIdCardBackImgSuccess" -->
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.PersonalForm).forShowidentityImag2 as UploadFile[]
                    "
                    :upload-method="uploadIdCardBack"
                    name="iFile"
                    :limit="1"
                    @success="handleIdCardBackImgSuccess"
                    @change="handleIdCardBackImgChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/10/c34243d6ab9249f9a4035e2b8b1bd8eb.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <wd-cell-group title="" border>
                <wd-input
                  label="法人姓名"
                  label-width="100px"
                  prop="larName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.PersonalForm).larName"
                  placeholder="请输入姓名"
                />

                <wd-input
                  label="法人手机号"
                  type="tel"
                  inputmode="tel"
                  label-width="100px"
                  :maxlength="11"
                  prop="merContactMobile"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.PersonalForm).merContactMobile"
                  placeholder="请输入商户联系人手机号"
                />

                <wd-input
                  label="法人身份证号"
                  label-width="100px"
                  prop="larIdCard"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.PersonalForm).larIdCard"
                  placeholder="请输入身份证号"
                />
                <wd-cell title="身份证有效期" title-width="100px" required prop="idValidity">
                  <wd-radio-group
                    custom-class="!flex"
                    v-model="(model as Api.ShopApply.PersonalForm).idValidity"
                    shape="dot"
                    @change="handleValidityChange"
                    inline
                  >
                    <wd-radio :value="0">非永久</wd-radio>
                    <wd-radio :value="1">永久</wd-radio>
                  </wd-radio-group>
                </wd-cell>
                <wd-cell title="开始时间" title-width="100px" required prop="larIdCardStDt">
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    :min-date="minDate"
                    :max-date="maxDate"
                    placeholder="请选择开始时间"
                    @confirm="handleLarIdcardStDtChange"
                    v-model="(model as Api.ShopApply.PersonalForm).forShowlarIdcardStDt"
                    prop=""
                    type="date"
                  />
                </wd-cell>
                <wd-cell
                  v-if="(model as Api.ShopApply.PersonalForm).idValidity === 0"
                  title="结束时间"
                  title-width="100px"
                  required
                  prop="larIdCardExpDt"
                >
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @confirm="handleLarIdcardExpDtChange"
                    placeholder="请选择结束时间"
                    v-model="(model as Api.ShopApply.PersonalForm).forShowlarIdcardExpDt"
                    prop=""
                    type="date"
                  />
                </wd-cell>
                <wd-input
                  label="法人邮箱"
                  inputmode="email"
                  label-width="100px"
                  prop="merContactEmail"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.PersonalForm).merContactEmail"
                  placeholder="请输入邮箱"
                />
              </wd-cell-group>
            </div>
          </template>
          <!-- 商户信息 -->
          <template v-if="step === 2">
            <div class="grid grid-cols-1 gap-y-10px">
              <!-- 营业执照 -->
              <wd-cell-group title="营业执照" border>
                <wd-cell required title="请上传营业执照" prop="businessId">
                  <!--  -->
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.MerchantForm).forShowbusinessId as UploadFile[]
                    "
                    :upload-method="uploadIdBusinessId"
                    name="iFile"
                    :limit="1"
                    @success="handleBusinessIdSuccess"
                    @change="handleBusinessIdChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/2becd7a9a0d843e9b31972738d379270.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <wd-cell-group title="" border>
                <wd-input
                  label="营业执照名称"
                  label-width="100px"
                  prop="merBlisName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.MerchantForm).merBlisName"
                  placeholder="请输入营业执照名称"
                />

                <wd-input
                  label="营业执照号"
                  label-width="100px"
                  prop="merBlis"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.MerchantForm).merBlis"
                  placeholder="请输入营业执照号"
                />
                <wd-input
                  label="商户名称"
                  label-width="100px"
                  prop="storeName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.MerchantForm).storeName"
                  placeholder="请输入商户名称"
                />
                <wd-textarea
                  label="商户经营内容"
                  label-width="100px"
                  type="textarea"
                  v-model="(model as Api.ShopApply.MerchantForm).merBusiContent"
                  required
                  :maxlength="500"
                  show-word-limit
                  placeholder="请输入商户经营内容"
                  clearable
                  prop="merBusiContent"
                />
                <wd-cell title="营业执照所在地" title-width="100px" required prop="provinceCode">
                  <wd-col-picker
                    v-if="businessColumns.length"
                    v-model="(model as Api.ShopApply.MerchantForm).forShowBussinessInfo"
                    :columns="businessColumns"
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    custom-value-class="!text-left"
                    value-key="code"
                    label-key="name"
                    :column-change="handleBusinessColumnChange"
                    @confirm="handleBusinessConfirm"
                  ></wd-col-picker>
                </wd-cell>
                <wd-cell title="营业执照有效期" title-width="100px" required prop="isPerpetual">
                  <wd-radio-group
                    custom-class="!flex"
                    v-model="(model as Api.ShopApply.MerchantForm).isPerpetual"
                    shape="dot"
                    @change="handlePerpetualChange"
                    inline
                  >
                    <wd-radio :value="0">非永久</wd-radio>
                    <wd-radio :value="1">永久</wd-radio>
                  </wd-radio-group>
                </wd-cell>
                <wd-cell title="开始时间" title-width="100px" required prop="merBlisStDt">
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    placeholder="请选择开始时间"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @confirm="handleMerBlisStDtChange"
                    v-model="(model as Api.ShopApply.MerchantForm).forShowMerBlisStDt"
                    type="date"
                  />
                </wd-cell>
                <wd-cell
                  v-if="(model as Api.ShopApply.MerchantForm).isPerpetual === 0"
                  title="结束时间"
                  title-width="100px"
                  required
                  prop="merBlisExpDt"
                >
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    placeholder="请选择结束时间"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @confirm="handleMerBlisExpDtChange"
                    v-model="(model as Api.ShopApply.MerchantForm).forShowMerBlisExpDt"
                    type="date"
                  />
                </wd-cell>
                <wd-input
                  label="服务费比例"
                  label-width="100px"
                  prop="supplierCommissionRate"
                  required
                  type="number"
                  @blur="hanldeCommissionRateBlur"
                  clearable
                  v-model="(model as Api.ShopApply.MerchantForm).supplierCommissionRate"
                  placeholder="请输入服务费比例(3%-20%)"
                />
                <wd-cell title="主营类别" title-width="100px" required prop="categoryId">
                  <wd-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    required
                    v-model="(model as Api.ShopApply.MerchantForm).forShowcategoryId"
                    :columns="categoryColumns"
                    :column-change="onChangeCategory"
                    :display-format="displayFormat"
                    @confirm="handleCateConfirm"
                  ></wd-picker>
                </wd-cell>
              </wd-cell-group>
            </div>
          </template>
          <!-- 门店信息 -->
          <template v-if="step === 3">
            <div class="grid grid-cols-1 gap-y-10px">
              <wd-cell-group title="" border>
                <wd-input
                  label="门店名称"
                  label-width="100px"
                  prop="storeName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.ShopForm).storeName"
                  placeholder="请输入门店名称"
                />

                <wd-cell title="所在城市" title-width="100px" required prop="provinceId">
                  <wd-picker
                    v-model="(model as Api.ShopApply.ShopForm).forShowarea"
                    :columns="columns"
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    custom-value-class="!text-left"
                    @confirm="handleConfirm"
                    :column-change="onChangeDistrict"
                  ></wd-picker>
                </wd-cell>
                <wd-textarea
                  label="详细地址"
                  label-width="100px"
                  type="textarea"
                  v-model="(model as Api.ShopApply.ShopForm).address"
                  required
                  :maxlength="100"
                  show-word-limit
                  placeholder="请输入详细地址"
                  clearable
                  prop="address"
                />
                <wd-cell title="地理位置" title-width="100px" required prop="longitude">
                  <div
                    @click="hanldeLocationChoose"
                    class="text-14px text-black flex items-center justify-between"
                  >
                    <div>{{ (model as Api.ShopApply.ShopForm).showLocation }}</div>
                    <div class="i-carbon-location text-14px text-#FF7D26" />
                  </div>
                </wd-cell>
                <wd-cell title="营业开始时间" title-width="100px" required prop="shopHoursSTime">
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    type="time"
                    v-model="(model as Api.ShopApply.ShopForm).shopHoursSTime"
                  />
                </wd-cell>
                <wd-cell title="营业结束时间" title-width="100px" required prop="shopHoursETime">
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    type="time"
                    v-model="(model as Api.ShopApply.ShopForm).shopHoursETime"
                  />
                </wd-cell>
                <wd-textarea
                  label="经营内容"
                  label-width="100px"
                  type="textarea"
                  v-model="(model as Api.ShopApply.ShopForm).summary"
                  required
                  :maxlength="100"
                  show-word-limit
                  placeholder="请输入经营内容"
                  clearable
                  prop="summary"
                />
              </wd-cell-group>
              <!-- 门头照 -->
              <wd-cell-group title="门头照" border>
                <wd-cell
                  required
                  title="建议上传带有招牌信息的门头照,并提供左右环境图,方便用户寻找"
                  prop="logoImageId"
                >
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.ShopForm).forShowlogoImageId as UploadFile[]
                    "
                    :upload-method="uploadLogoImageId"
                    name="iFile"
                    :limit="1"
                    @change="handleLogoImageIdChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/246743c964d4415baf8d96633848e8b1.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 收银台 -->
              <wd-cell-group title="收银台" border>
                <wd-cell required title="请上传收银台照片" prop="cashRegisterId">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.ShopForm).forShowCashRegisterId as UploadFile[]
                    "
                    :upload-method="uploadCashRegisterId"
                    name="iFile"
                    :limit="1"
                    @change="handleCashRegisterIdChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/246743c964d4415baf8d96633848e8b1.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 内景照 -->
              <wd-cell-group title="内景照" border>
                <wd-cell required title="请上传店铺内景/工作区域照" prop="interiorShotId">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.ShopForm).forShowInteriorShotId as UploadFile[]
                    "
                    :upload-method="uploadInteriorShotId"
                    name="iFile"
                    @change="handleInteriorShotIdChange"
                    accept="image"
                    :limit="1"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/246743c964d4415baf8d96633848e8b1.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
            </div>
          </template>
          <!-- 结算信息 -->
          <template v-if="step === 4">
            <div class="grid grid-cols-1 gap-y-10px">
              <wd-cell-group title="" border>
                <wd-cell title="结算对象类型" title-width="100px" required prop="acctIdType">
                  <wd-radio-group
                    custom-class="!flex"
                    v-model="(model as Api.ShopApply.SettlementForm).acctIdType"
                    shape="dot"
                    @change="handleAcctIdTypeChange"
                    inline
                  >
                    <wd-radio :value="0">法人</wd-radio>
                    <wd-radio :value="1">非法人</wd-radio>
                  </wd-radio-group>
                </wd-cell>
                <wd-cell title="开户银行类型" title-width="100px" required prop="acctTypeCode">
                  <wd-radio-group
                    custom-class="!flex"
                    :disabled="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                    v-model="(model as Api.ShopApply.SettlementForm).acctTypeCode"
                    shape="dot"
                    inline
                  >
                    <wd-radio :value="58">对私</wd-radio>
                    <wd-radio :value="57">对公</wd-radio>
                  </wd-radio-group>
                </wd-cell>
              </wd-cell-group>
              <!-- 非法人正面身份证 -->
              <wd-cell-group
                v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                title="非法人身份证人像面"
                border
              >
                <wd-cell required title="请上传非法人身份证人像面" prop="acctIdCardImageId1">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.SettlementForm)
                        .forShowAcctIdcardImageId1 as UploadFile[]
                    "
                    :upload-method="uploadAcctIdcardImageId1"
                    name="iFile"
                    :limit="1"
                    @change="handleAcctIdcardImageId1Change"
                    accept="image"
                    @success="handleAcctIdcardImageId1Success"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/10/477deac72e994d209074cc1cf94211a0.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 非法人反面身份证 -->
              <wd-cell-group
                v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                title="非法人身份证国徽面"
                border
              >
                <wd-cell required title="请上传非法人身份证国徽面" prop="acctIdCardImageId2">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.SettlementForm)
                        .forShowAcctIdcardImageId2 as UploadFile[]
                    "
                    :upload-method="uploadAcctIdcardImageId2"
                    name="iFile"
                    :limit="1"
                    @change="handleAcctIdcardImageId2Change"
                    accept="image"
                    @success="handleAcctIdcardImageId2Success"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/10/c34243d6ab9249f9a4035e2b8b1bd8eb.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 资金清算授权委托书 -->
              <wd-cell-group
                v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                title="资金清算授权委托书"
                border
              >
                <wd-cell required title="请上传资金清算授权委托书" prop="delegatedAuthorizationId">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.SettlementForm)
                        .forShowDelegatedAuthorizationId as UploadFile[]
                    "
                    :upload-method="uploadDelegatedAuthorizationId"
                    name="iFile"
                    :limit="1"
                    @change="handleDelegatedAuthorizationIdChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/246743c964d4415baf8d96633848e8b1.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 银行卡正面 -->
              <wd-cell-group
                v-if="
                  ((model as Api.ShopApply.SettlementForm).acctTypeCode === 58 &&
                    (model as Api.ShopApply.SettlementForm).acctIdType === 0) ||
                  (model as Api.ShopApply.SettlementForm).acctIdType === 1
                "
                title="银行卡正面"
                border
              >
                <wd-cell required title="请上传含银行卡号面" prop="bankCardImageId1">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.SettlementForm)
                        .forShowbankCardImageId1 as UploadFile[]
                    "
                    :upload-method="uploadBankCardImageId1"
                    name="iFile"
                    :limit="1"
                    @success="handleCardImageIdSuccess"
                    @change="handleBankCardImageId1Change"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/770859c18bc94a8fbf5effd9dfd28994.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <!-- 银行卡反面 -->
              <wd-cell-group
                v-if="
                  ((model as Api.ShopApply.SettlementForm).acctTypeCode === 58 &&
                    (model as Api.ShopApply.SettlementForm).acctIdType === 0) ||
                  (model as Api.ShopApply.SettlementForm).acctIdType === 1
                "
                title="银行卡反面"
                border
              >
                <wd-cell required title="请上传银行卡反面" prop="bankCardImageId2">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.SettlementForm)
                        .forShowBankCardImageId2 as UploadFile[]
                    "
                    :upload-method="uploadBankCardImageId2"
                    name="iFile"
                    :limit="1"
                    @success="handleCardImageId2Success"
                    @change="handleBankCardImageId2Change"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/2d5af51c34e9465e8354d66035278a94.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>

              <!-- 银行开户许可 -->
              <wd-cell-group
                v-if="
                  (model as Api.ShopApply.SettlementForm).acctTypeCode === 57 &&
                  (model as Api.ShopApply.SettlementForm).acctIdType === 0
                "
                title="银行开户许可证"
                border
              >
                <wd-cell required title="请上传银行开户许可证" prop="bankAccountLicenseId">
                  <wd-upload
                    :file-list="
                      (model as Api.ShopApply.SettlementForm)
                        .forShowbankAccountLicenseId as UploadFile[]
                    "
                    :upload-method="uploadBankAccountLicenseId"
                    name="iFile"
                    :limit="1"
                    @change="handleBankAccountLicenseIdChange"
                    accept="image"
                    :action="VITE_UPLOAD_BASEURL"
                  >
                    <wd-img
                      src="https://file.shanqianqi.com/image/2025/06/11/246743c964d4415baf8d96633848e8b1.png"
                      width="200rpx"
                      height="200rpx"
                      mode="aspectFill"
                    ></wd-img>
                  </wd-upload>
                </wd-cell>
              </wd-cell-group>
              <wd-cell-group title="" border>
                <wd-input
                  label="开户名"
                  label-width="100px"
                  prop="acctName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.SettlementForm).acctName"
                  placeholder="请输入持卡人姓名"
                />

                <wd-input
                  label="银行卡号"
                  label-width="100px"
                  prop="acctNo"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.SettlementForm).acctNo"
                  placeholder="请输入银行卡号"
                />
                <wd-cell title="开户银行" title-width="100px" required prop="openningBankName">
                  <wd-col-picker
                    v-if="bankColumns.length"
                    v-model="(model as Api.ShopApply.SettlementForm).forShowBankInfo"
                    :columns="bankColumns"
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    custom-value-class="!text-left"
                    value-key="code"
                    label-key="name"
                    :column-change="handleBankColumnChange"
                    @confirm="handleBankConfirm"
                  ></wd-col-picker>
                </wd-cell>
                <!-- <wd-input
                  label="开户银行"
                  label-width="100px"
                  prop="openningBankName"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.SettlementForm).openningBankName"
                  placeholder="请输入开户银行"
                /> -->
                <wd-input
                  v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                  label="非法人身份证号"
                  label-width="100px"
                  prop="acctIdCard"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.SettlementForm).acctIdCard"
                  placeholder="请输入非法人身份证号"
                />
                <wd-cell
                  v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                  title="非法人身份证有效期"
                  title-width="100px"
                  required
                  prop="isLong"
                >
                  <wd-radio-group
                    custom-class="!flex"
                    v-model="(model as Api.ShopApply.SettlementForm).isLong"
                    shape="dot"
                    @change="hanldeIsLongChange"
                    inline
                  >
                    <wd-radio :value="0">非永久</wd-radio>
                    <wd-radio :value="1">永久</wd-radio>
                  </wd-radio-group>
                </wd-cell>
                <wd-cell
                  v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                  title="非法人身份证开始时间"
                  title-width="100px"
                  required
                  prop="accountIdDtStart"
                >
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    placeholder="请选择开始时间"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @confirm="handleAccountIdDtStartChange"
                    v-model="(model as Api.ShopApply.SettlementForm).forShowAccountIdDtStart"
                    type="date"
                  />
                </wd-cell>
                <wd-cell
                  v-if="
                    (model as Api.ShopApply.SettlementForm).isLong === 0 &&
                    (model as Api.ShopApply.SettlementForm).acctIdType === 1
                  "
                  title="非法人身份证结束时间"
                  title-width="100px"
                  required
                  prop="accountIdDtEnd"
                >
                  <wd-datetime-picker
                    custom-class="!flex  !border-none"
                    custom-cell-class="!p-unset"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @confirm="handleAccountIdDtEndChange"
                    placeholder="请选择结束时间"
                    v-model="(model as Api.ShopApply.SettlementForm).forShowAccountIdDtEnd"
                    type="date"
                  />
                </wd-cell>
                <wd-input
                  v-if="(model as Api.ShopApply.SettlementForm).acctIdType === 1"
                  label="手机号"
                  type="tel"
                  inputmode="tel"
                  label-width="100px"
                  :maxlength="11"
                  prop="merContactMobile"
                  required
                  clearable
                  v-model="(model as Api.ShopApply.SettlementForm).merContactMobile"
                  placeholder="签约短信手机号"
                />
              </wd-cell-group>
            </div>
            <div class="py10px text-#333333 text-32rpx font-bold">其他补充照片</div>
            <!-- 其他补充照片 -->
            <wd-cell-group title="" border>
              <wd-upload
                :file-list="
                  (model as Api.ShopApply.SettlementForm).forShowotherImageIds as UploadFile[]
                "
                :upload-method="uploadOtherImageIds"
                name="iFile"
                @change="handleOtherImageIdsChange"
                accept="image"
                :action="VITE_UPLOAD_BASEURL"
              >
                <wd-img
                  src="https://file.shanqianqi.com/image/2025/06/11/246743c964d4415baf8d96633848e8b1.png"
                  width="200rpx"
                  height="200rpx"
                  mode="aspectFill"
                ></wd-img>
              </wd-upload>
            </wd-cell-group>
          </template>
        </wd-form>
      </template>
    </div>

    <div class="px-26rpx box-border grid grid-cols-1">
      <div @click="handleMerchartClick">
        <wd-checkbox
          v-if="step === 0"
          v-model="merchartCheck"
          :disabled="!merchartRead"
          shape="square"
        >
          <div class="flex items-center">
            阅读并同意
            <div class="h-fit flex items-center" @click.stop>
              <wd-button
                @click="goMerchart"
                type="text"
                custom-class="!p-unset !m-unset !line-height-unset"
              >
                《商家入驻协议》
              </wd-button>
            </div>
          </div>
        </wd-checkbox>
      </div>

      <div class="box-border w-full mt-10px flex items-center gap-x-10px">
        <wd-button v-if="step !== 0" custom-class="!flex-1" @click="preStep" type="success">
          上一步
        </wd-button>
        <wd-button custom-class="!flex-1" @click="nextStep">
          {{ step === 4 ? '提交信息' : '下一步' }}
        </wd-button>
      </div>
    </div>

    <wd-popup
      v-model="helpPop"
      custom-style="border-radius:32rpx;width:80%"
      @close="helpPop = !helpPop"
    >
      <div class="p-20rpx flex flex-col items-center gap-y-20rpx">
        <wd-img :src="qrCodeImg" width="100%" enable-preview mode="widthFix"></wd-img>
        <span class="text-30rpx text-#333333 font-bold text-center">帮忙进件二维码</span>
      </div>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import type { FormRules } from 'wot-design-uni/components/wd-form/types'
import { getEnvBaseUploadUrl, createUploadHandler, convertCategoryArrayToPickerMap } from '@/utils'
import type { UploadFile } from 'wot-design-uni/components/wd-upload/types'
import {
  userSupplierCategory,
  fetchRegionList,
  userSupplierApply,
  fetchAddress,
  picRecognize,
  getBankRegistration,
  getRegistration,
  getBankList,
  userShopApplyDetail,
  getImgById,
} from '@/service'
import dayjs from 'dayjs'
import { useLocationStore } from '@/store'
import { useShopApplyEditor } from '@/hooks/useShopApplyEditor'
import { convertBackendDataToFormMap } from '@/utils'

const {
  // 全部信息
  form,

  merchant,
  shop,
  settlement,

  // 更新方法
  updateAccount,
  updatePersonal,
  updateMerchant,
  updateShop,
  updateSettlement,

  // 重置
  resetAll,
} = useShopApplyEditor()

const now = dayjs()

const minDate = ref(now.subtract(30, 'year').valueOf())

const maxDate = ref(now.add(30, 'year').valueOf())

const recUserId = ref('')
const type = ref('')
onLoad(async (e) => {
  if (e?.type) {
    type.value = e?.type
  }
  if (e?.userId) {
    recUserId.value = e?.userId
  }

  await runDetail()
  await handleGetBank()
  await handleGetBusiness()

  //编辑回显
  if (type.value === 'edit') {
    merchartCheck.value = true
    merchartRead.value = true

    // 营业执照所在地回显
    if (applyDetail.value?.provinceCode) {
      businessCode.value = Number(applyDetail.value?.provinceCode)
      await handleGetBusiness()
    }
    if (applyDetail.value?.cityCode) {
      businessCode.value = Number(applyDetail.value?.cityCode)
      await handleGetBusiness()
    }

    // 经营类别
    if (applyDetail.value?.categoryId) {
      const resolvedPath = resolveCategoryPath(Number(applyDetail.value?.categoryId))
      if (resolvedPath.length > 0) {
        formMap.value[2].forShowcategoryId = resolvedPath

        const parentId = resolvedPath[0]
        const index = categoryMap.value['0']?.findIndex((item) => item.value == parentId)
        if (index !== -1) {
          selectedCategoryIndex.value = index
        }
      }
    }

    // 门店位置回显
    if (applyDetail.value?.storeParam?.provinceId) {
      selectedProvinceIndex.value =
        area.value[0]?.findIndex(
          (item: any) => item?.value === applyDetail.value?.storeParam?.provinceId,
        ) ?? 0

      if (applyDetail.value?.storeParam?.cityId) {
        selectedCityIndex.value =
          area.value[1][selectedProvinceIndex.value]?.findIndex(
            (item: any) => item?.value === applyDetail.value?.storeParam?.cityId,
          ) ?? 0
      }
    }

    // 开户行回显
    if (applyDetail.value?.settleProvinceCode) {
      bankCode.value = Number(applyDetail.value?.settleProvinceCode)
      await handleGetBank()
    }
    if (applyDetail.value?.settleCityCode) {
      bankCode.value = Number(applyDetail.value?.settleCityCode)
      const { data } = await getBankList(bankCode.value)
      bankColumns.value.push(data)
    }

    // 赋值
    formMap.value = await convertBackendDataToFormMap(
      applyDetail.value as Api.ShopApply.ShopApplyDetail,
    )

    // 门店经纬度

    if (applyDetail.value?.storeParam?.longitude && applyDetail.value?.storeParam?.latitude) {
      const { data } = await fetchAddress({
        lon: Number(applyDetail.value?.storeParam?.longitude),
        lat: Number(applyDetail.value?.storeParam?.latitude),
        ver: 1,
      })
      const poi = JSON.parse((data as unknown as string) ?? '')?.result?.addressComponent?.poi ?? ''
      formMap.value[3].showLocation = poi
    }
  }
  //正常回显
  else {
    if (settlement.value?.otherImageIds) {
      const imageIds = settlement.value?.otherImageIds.split(',')
      const imagePromises = imageIds.map(async (id) => {
        const { data } = await getImgById({ imageId: Number(id) })
        return { url: data }
      })
      const forShowotherImageIds = await Promise.all(imagePromises)

      updateSettlement({ forShowotherImageIds })
    }

    formMap.value = form
    // 营业执照所在地回显
    if (merchant.value?.provinceCode) {
      businessCode.value = Number(merchant.value?.provinceCode)
      await handleGetBusiness()
    }
    if (merchant.value?.cityCode) {
      businessCode.value = Number(merchant.value?.cityCode)
      await handleGetBusiness()
    }

    // 经营类别
    if (merchant.value?.categoryId) {
      const resolvedPath = resolveCategoryPath(Number(merchant.value?.categoryId))
      if (resolvedPath.length > 0) {
        formMap.value[2].forShowcategoryId = resolvedPath

        const parentId = resolvedPath[0]
        const index = categoryMap.value['0']?.findIndex((item) => item.value == parentId)
        if (index !== -1) {
          selectedCategoryIndex.value = index
        }
      }
    }

    // 门店位置回显
    if (shop.value?.provinceId) {
      selectedProvinceIndex.value =
        area.value[0]?.findIndex((item: any) => item?.value === shop.value?.provinceId) ?? 0

      if (shop.value?.cityId) {
        selectedCityIndex.value =
          area.value[1][selectedProvinceIndex.value]?.findIndex(
            (item: any) => item?.value === shop.value?.cityId,
          ) ?? 0
      }
    }

    // 开户行回显
    if (settlement.value?.settleProvinceCode) {
      bankCode.value = Number(settlement.value?.settleProvinceCode)
      await handleGetBank()
    }
    if (settlement.value?.settleCityCode) {
      bankCode.value = Number(settlement.value?.settleCityCode)
      const { data } = await getBankList(bankCode.value)
      bankColumns.value.push(data)
    }
  }
})

const locationStore = useLocationStore()

const { safeAreaInsets } = uni.getSystemInfoSync()

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`

const steps = ref([
  {
    label: '账号信息',
    icon: 'https://file.shanqianqi.com/image/2025/07/02/4c41be4133ed423681da00896ba8200d.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/07/02/23b70b74011240189d50e2ee36cdc000.png',
  },
  {
    label: '法人信息',
    icon: 'https://file.shanqianqi.com/image/2025/07/02/3f5934cd889b4996a851250b8de85617.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/07/02/87dada65ae554f40a8f72a5b9cc647ef.png',
  },
  {
    label: '商户信息',
    icon: 'https://file.shanqianqi.com/image/2025/07/02/346957f263e147a7be2e1866010bc041.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/07/02/88dd665a90334f378252b70d77780f4b.png',
  },
  {
    label: '门店信息',
    icon: 'https://file.shanqianqi.com/image/2025/07/02/4987b557267b4c969b816ebec0fb38ad.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/07/02/55a8c3f56a284e2787c54345f989aa81.png',
  },
  {
    label: '结算信息',
    icon: 'https://file.shanqianqi.com/image/2025/07/02/333e5f1085444bcdb4b600ff79827bba.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/07/02/8558f52883c641fb897cad8780ee9ed9.png',
  },
])

const step = ref<number>(0)
const handleStep = (index) => {
  step.value = index
}

// 存储多个 form 的 ref
const formRefs = ref<Record<number, any>>({})

const setFormRef = (index: number, el: any) => {
  if (el) formRefs.value[index] = el
}

const formMap = ref<Api.ShopApply.FormMapType>([
  //账号信息
  { mobile: '', userName: '', password: '' },
  //法人信息
  {
    identityImag1: 0,
    forShowidentityImag1: [],
    identityImag2: 0,
    forShowidentityImag2: [],
    larName: '',
    larIdCard: '',
    idValidity: 0,
    larIdCardStDt: '',
    forShowlarIdcardStDt: 0,
    forShowlarIdcardExpDt: 0,
    larIdCardExpDt: '',
    merContactMobile: '',
    merContactEmail: '',
  },
  //商户信息
  {
    businessId: 0,
    forShowbusinessId: [],
    merBlisName: '',
    merBlis: '',
    isPerpetual: 0,
    merBlisStDt: '',
    forShowMerBlisStDt: 0,
    merBlisExpDt: '',
    forShowMerBlisExpDt: 0,
    supplierCommissionRate: '',
    categoryId: '',
    forShowcategoryId: [],
    forShowBussinessInfo: [],
    provinceCode: '',
    cityCode: '',
    countyCode: '',
    storeName: '',
    merBusiContent: '',
  },
  //门店信息
  {
    storeName: '',
    provinceId: 0,
    cityId: 0,
    regionId: 0,
    forShowarea: [],
    area: '',
    address: '',
    shopHoursSTime: '',
    shopHoursETime: '',
    summary: '',
    logoImageId: 0,
    forShowlogoImageId: [],
    cashRegisterId: 0,
    forShowCashRegisterId: [],
    interiorShotId: 0,
    forShowInteriorShotId: [],
    latitude: '',
    longitude: '',
    showLocation: '请选择位置',
  },
  // 结算信息
  {
    acctIdType: 0,
    acctTypeCode: 58,
    bankCardImageId1: 0,
    forShowbankCardImageId1: [],
    bankCardImageId2: 0,
    forShowBankCardImageId2: [],
    acctIdCardImageId1: 0,
    forShowAcctIdcardImageId1: [],
    acctIdCardImageId2: 0,
    forShowAcctIdcardImageId2: [],
    bankAccountLicenseId: 0,
    forShowbankAccountLicenseId: [],
    delegatedAuthorizationId: 0,
    forShowDelegatedAuthorizationId: [],
    acctName: '',
    acctNo: '',
    forShowBankInfo: [],
    settleProvinceCode: '',
    settleProvinceName: '',
    settleCityCode: '',
    settleCityName: '',
    openningBankCode: '',
    openningBankName: '',
    clearingBankCode: '',
    acctIdCard: '',
    merContactMobile: '',
    otherImageIds: '',
    forShowotherImageIds: [],
    isLong: 0,
    accountIdDtStart: '',
    forShowAccountIdDtEnd: 0,
    accountIdDtEnd: '',
    forShowAccountIdDtStart: 0,
  },
])

const formRules = ref<[FormRules, FormRules, FormRules, FormRules, FormRules]>([
  // 账号信息
  {
    mobile: [
      {
        required: true,
        message: '请输入手机号',
        validator: (value) => {
          if (!value) {
            return Promise.reject('请输入手机号')
          } else if (!/^1[3-9]\d{9}$/.test(value)) {
            return Promise.reject('请正确输入手机号')
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
    userName: [
      {
        required: true,
        message: '请输入登录账号',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入登录账号')
          }
        },
      },
    ],
    password: [
      {
        required: true,
        message: '请输入登录密码',
        validator: (value) => {
          if (!value) {
            return Promise.reject('请输入登录密码')
          } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(value)) {
            return Promise.reject('密码格式不正确')
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
  },
  // 法人信息
  {
    identityImag1: [
      {
        required: true,
        message: '请上传身份证人面像',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传身份证人面像')
          }
        },
      },
    ],
    identityImag2: [
      {
        required: true,
        message: '请上传身份证国微像',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传身份证国微像')
          }
        },
      },
    ],
    larName: [
      {
        required: true,
        message: '请输入姓名',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入姓名')
          }
        },
      },
    ],
    merContactMobile: [
      {
        required: true,
        message: '请输入商户联系人手机号',
        validator: (value) => {
          if (!value) {
            return Promise.reject('请输入商户联系人手机号')
          } else if (!/^1[3-9]\d{9}$/.test(value)) {
            return Promise.reject('请正确输入手机号')
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
    larIdCard: [
      {
        required: true,
        message: '请输入身份证号',
        validator: (value) => {
          if (!value) {
            return Promise.reject('请输入身份证号')
          } else if (
            !/^[1-9]\d{5}(18|19|20|21)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(
              value,
            )
          ) {
            return Promise.reject('请正确身份证号')
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
    larIdCardStDt: [
      {
        required: true,
        message: '请选择开始时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择开始时间')
          }
        },
      },
    ],
    larIdCardExpDt: [
      {
        required: true,
        message: '请选择结束时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择结束时间')
          }
        },
      },
    ],
    merContactEmail: [
      {
        required: true,
        message: '请输入邮箱',
        validator: (value) => {
          if (!value) {
            return Promise.reject('请输入邮箱')
          } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
            return Promise.reject('请正确输入邮箱')
          } else {
            return Promise.resolve()
          }
        },
      },
    ],
  },
  // 商户信息
  {
    businessId: [
      {
        required: true,
        message: '请上传营业执照',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传营业执照')
          }
        },
      },
    ],
    merBlisName: [
      {
        required: true,
        message: '请输入营业执照名称',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入营业执照名称')
          }
        },
      },
    ],
    storeName: [
      {
        required: true,
        message: '请输入商户名称',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入商户名称')
          }
        },
      },
    ],
    merBusiContent: [
      {
        required: true,
        message: '请输入商户经营内容',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入商户经营内容')
          }
        },
      },
    ],
    merBlis: [
      {
        required: true,
        message: '请输入营业执照号',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入营业执照号')
          }
        },
      },
    ],
    provinceCode: [
      {
        required: true,
        message: '请选择营业执照所在地',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择营业执照所在地')
          }
        },
      },
    ],
    merBlisStDt: [
      {
        required: true,
        message: '请选择开始时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择开始时间')
          }
        },
      },
    ],
    merBlisExpDt: [
      {
        required: true,
        message: '请选择结束时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择结束时间')
          }
        },
      },
    ],
    supplierCommissionRate: [
      {
        required: true,
        message: '请输入服务费比例',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入服务费比例(5%-20%)')
          }
        },
      },
    ],
    categoryId: [
      {
        required: true,
        message: '请选择主营类别',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择主营类别')
          }
        },
      },
    ],
  },
  // 门店信息
  {
    storeName: [
      {
        required: true,
        message: '请输入门店名称',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入门店名称')
          }
        },
      },
    ],
    provinceId: [
      {
        required: true,
        message: '请选择所在城市',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择所在城市')
          }
        },
      },
    ],
    address: [
      {
        required: true,
        message: '请输入详细地址',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入详细地址')
          }
        },
      },
    ],
    longitude: [
      {
        required: true,
        message: '请选择位置',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择位置')
          }
        },
      },
    ],
    shopHoursSTime: [
      {
        required: true,
        message: '请选择营业开始时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择营业开始时间')
          }
        },
      },
    ],
    shopHoursETime: [
      {
        required: true,
        message: '请选择营业结束时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择营业结束时间')
          }
        },
      },
    ],
    summary: [
      {
        required: true,
        message: '请输入经营内容',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入经营内容')
          }
        },
      },
    ],
    logoImageId: [
      {
        required: true,
        message: '请上传门头照',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传门头照')
          }
        },
      },
    ],
    cashRegisterId: [
      {
        required: true,
        message: '请上传收银台照片',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传收银台照片')
          }
        },
      },
    ],
    interiorShotId: [
      {
        required: true,
        message: '请上传内景照',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传内景照')
          }
        },
      },
    ],
  },
  // 结算信息
  {
    bankCardImageId1: [
      {
        required: true,
        message: '请上传含银行卡号面',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传含银行卡号面')
          }
        },
      },
    ],
    bankCardImageId2: [
      {
        required: true,
        message: '请上传银行卡反面',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传银行卡反面')
          }
        },
      },
    ],
    acctIdCardImageId1: [
      {
        required: true,
        message: '请上传非法人身份证人像面',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传非法人身份证人像面')
          }
        },
      },
    ],
    acctIdCardImageId2: [
      {
        required: true,
        message: '请上传银行卡反面',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传银行卡反面')
          }
        },
      },
    ],
    bankAccountLicenseId: [
      {
        required: true,
        message: '请上传银行开户许可证',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请上传银行开户许可证')
          }
        },
      },
    ],
    acctName: [
      {
        required: true,
        message: '请输入持卡人姓名',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入持卡人姓名')
          }
        },
      },
    ],
    acctNo: [
      {
        required: true,
        message: '请输入银行卡号',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入银行卡号')
          }
        },
      },
    ],
    openningBankName: [
      {
        required: true,
        message: '请输入开户银行',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入开户银行')
          }
        },
      },
    ],
    acctIdCard: [
      {
        required: true,
        message: '请输入非法人身份证号',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入非法人身份证号')
          }
        },
      },
    ],
    accountIdDtStart: [
      {
        required: true,
        message: '请选择非法人身份证开始时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择非法人身份证开始时间')
          }
        },
      },
    ],
    accountIdDtEnd: [
      {
        required: true,
        message: '请选择非法人身份证结束时间',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请选择非法人身份证结束时间')
          }
        },
      },
    ],
    merContactMobile: [
      {
        required: true,
        message: '请输入签约短信手机号',
        validator: (value) => {
          if (value) {
            return Promise.resolve()
          } else {
            return Promise.reject('请输入签约短信手机号')
          }
        },
      },
    ],
  },
])

/*
账号信息
*/
const handleMobileBlur = ({ value }) => {
  formMap.value[0].userName = value
}

/*
法人信息
*/
// 法人身份证人面像
const uploadIdCardFront = createUploadHandler('image')
const handleIdCardFrontImgChange = async ({ fileList }) => {
  formMap.value[1].forShowidentityImag1 = fileList
  formMap.value[1].identityImag1 = fileList[0]?.response?.fileId
}
const handleIdCardFrontImgSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'IdCard' })
      formMap.value[1].larName = data?.subImages[0]?.kvInfo?.data?.name
      formMap.value[1].larIdCard = data?.subImages[0]?.kvInfo?.data?.idNumber
    } catch (error) {
      uni.showToast({ title: '自动识别失败,请正确上传', icon: 'none' })
    }
  }
}

// 法人身份证国徽像
const uploadIdCardBack = createUploadHandler('image')
const handleIdCardBackImgChange = async ({ fileList }) => {
  formMap.value[1].forShowidentityImag2 = fileList
  formMap.value[1].identityImag2 = fileList[0]?.response?.fileId
}
const handleIdCardBackImgSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'IdCard' })
      const sT = data?.subImages[0]?.kvInfo?.data?.validPeriod?.split('-')?.[0]
      const eT = data?.subImages[0]?.kvInfo?.data?.validPeriod?.split('-')?.[1] ?? ''
      formMap.value[1].larIdCardStDt = dayjs(sT, 'YYYY.MM.DD').format('YYYY-MM-DD')
      formMap.value[1].forShowlarIdcardStDt = dayjs(sT, 'YYYY.MM.DD').valueOf()

      if (!eT) {
        formMap.value[1].larIdCardExpDt = '9999-12-31'
        formMap.value[1].idValidity = 1
        return
      }

      formMap.value[1].larIdCardExpDt = dayjs(eT).format('YYYY-MM-DD')
      formMap.value[1].forShowlarIdcardExpDt = dayjs(eT).valueOf()
    } catch (error) {
      uni.showToast({ title: '自动识别失败,请正确上传', icon: 'none' })
    }
  }
}

const handleValidityChange = ({ value }) => {
  if (value === 1) formMap.value[1].larIdCardExpDt = '9999-12-31'
}

// 身份证有效期开始时间
const handleLarIdcardStDtChange = ({ value }) => {
  formMap.value[1].larIdCardStDt = dayjs(value).format('YYYY-MM-DD')
}

// 身份证有效期结束时间
const handleLarIdcardExpDtChange = ({ value }) => {
  formMap.value[1].larIdCardExpDt = dayjs(value).format('YYYY-MM-DD')
}

/*
商户信息
*/
// 营业执照
const uploadIdBusinessId = createUploadHandler('image')
const handleBusinessIdChange = async ({ fileList }) => {
  formMap.value[2].forShowbusinessId = fileList
  formMap.value[2].businessId = fileList[0]?.response?.fileId
}
const handleBusinessIdSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'BusinessLicense' })
      const info = data?.subImages[0]?.kvInfo?.data
      formMap.value[2].merBlisName = info?.companyName
      formMap.value[2].merBusiContent = info?.businessScope
      formMap.value[2].merBlis = info?.creditCode
      formMap.value[2].merBlisStDt = dayjs(info?.validFromDate, 'YYYYMMDD').format('YYYY-MM-DD')
      formMap.value[2].forShowMerBlisStDt = dayjs(info?.validFromDate, 'YYYYMMDD').valueOf()
      formMap.value[2].forShowMerBlisStDt = dayjs(info?.validFromDate, 'YYYYMMDD').valueOf()
    } catch (error) {
      uni.showToast({ title: '自动识别失败,请正确上传', icon: 'none' })
    }
  }
}

const handlePerpetualChange = ({ value }) => {
  if (value === 1) formMap.value[2].merBlisExpDt = '9999-12-31'
}

// 营业执照省市区
const businessCode = ref(1)

const businessColumns = ref([])

const handleGetBusiness = async () => {
  const { data } = await getRegistration(businessCode.value)
  businessColumns.value.push(data)
}
// 帮助函数：确定当前选中 code 属于哪一列
function getBussinessSelectedLevel(code) {
  for (let i = 0; i < businessColumns.value.length; i++) {
    if (businessColumns.value[i].some((item) => item.code === code)) {
      return i
    }
  }
  return 0 // fallback
}

const handleBusinessColumnChange = async ({ selectedItem, resolve, finish }) => {
  const level = getBussinessSelectedLevel(selectedItem.code)
  businessCode.value = selectedItem.code

  // 获取下一级（如：选中“市”后拿“区”）
  const { data } = await getRegistration(businessCode.value)

  if (data && data.length > 0) {
    // 有子项：清理后续层级 + 追加
    businessColumns.value.splice(level + 1)
    businessColumns.value.push(data)
    resolve(businessColumns.value)
  } else {
    finish()
  }
}

const handleBusinessConfirm = async ({ value }) => {
  formMap.value[2].provinceCode = value?.[0] ?? ''
  formMap.value[2].cityCode = value?.[1] ?? ''
  formMap.value[2].countyCode = value?.[2] ?? ''
}

// 营业执照有效期开始时间
const handleMerBlisStDtChange = ({ value }) => {
  formMap.value[2].merBlisStDt = dayjs(value).format('YYYY-MM-DD')
}

// 营业执照有效期结束时间
const handleMerBlisExpDtChange = ({ value }) => {
  formMap.value[2].merBlisExpDt = dayjs(value).format('YYYY-MM-DD')
}

// 主营分类
const { data: category } = useRequest(() => userSupplierCategory(), { immediate: true })
const categoryMap = computed<Record<string, { label: string; value: number }[]>>(() =>
  convertCategoryArrayToPickerMap(category.value),
)

function resolveCategoryPath(categoryId: number): number[] {
  for (const parent of category.value || []) {
    if (parent.categoryId === categoryId) {
      // 是一级分类
      return [categoryId, 0]
    }

    const children = parent.children || []
    const child = children.find((c) => c.categoryId === categoryId)
    if (child) {
      // 是子分类，返回 [父ID, 子ID]
      return [parent.categoryId, child.categoryId]
    }
  }

  // 没找到
  return []
}

const selectedCategoryIndex = ref(0)

const categoryColumns = computed(() => {
  const parentList = categoryMap.value['0'] || []
  const parentItem = parentList[selectedCategoryIndex.value]
  const subList = parentItem
    ? categoryMap.value[parentItem.value] || []
    : [{ label: '', value: '' }]

  return [parentList, subList]
})

const onChangeCategory = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]

  if (columnIndex === 0) {
    const parentList = categoryMap.value['0'] || []
    const parentIndex = parentList.findIndex((c) => c.value === item.value)
    if (parentIndex !== -1) {
      selectedCategoryIndex.value = parentIndex
    }

    const key = item.value.toString()
    const nextCol = categoryMap.value?.[key] || []

    if (nextCol.length > 0) {
      pickerView.setColumnData(1, nextCol)
    } else {
      pickerView.setColumnData(1, [{ label: '', value: '' }])
    }
  }

  resolve()
}
const displayFormat = (items) => {
  const labels = items.map((i) => i?.label).filter((label) => !!label)

  return labels.join(' > ')
}
const handleCateConfirm = ({ value }) => {
  formMap.value[2].categoryId = value?.[1] || value?.[0]
}

const hanldeCommissionRateBlur = ({ value }) => {
  if (value === '' || value === null || value === undefined) return

  const numValue = Number(value)

  if (isNaN(numValue) || numValue < 3 || numValue > 20) {
    formMap.value[2].supplierCommissionRate = ''
  }
}

/*
门店信息
 */

// 省市区
const { data: area } = useRequest(() => fetchRegionList(), { immediate: true })

const selectedProvinceIndex = ref(0)
const selectedCityIndex = ref(0)

const columns = computed(() => [
  area.value[0],
  area.value[1][selectedProvinceIndex.value] || [],
  area.value[2][selectedProvinceIndex.value]?.[selectedCityIndex.value] || [],
])

const onChangeDistrict = (pickerView, value, columnIndex, resolve) => {
  const provinceIndex = area.value[0]?.findIndex((item) => item.value === value[0].value)

  if (columnIndex === 0) {
    selectedProvinceIndex.value = provinceIndex
    selectedCityIndex.value = 0

    const cities = area.value[1][provinceIndex] || []
    pickerView.setColumnData(1, cities)

    const districts = area.value[2][provinceIndex]?.[0] || []
    pickerView.setColumnData(2, districts)
  } else if (columnIndex === 1) {
    const cityList = area.value[1][provinceIndex] || []
    const cityIndex = cityList.findIndex((item) => item.value === value[1].value)

    selectedCityIndex.value = cityIndex

    const districts = area.value[2][provinceIndex]?.[cityIndex] || []
    pickerView.setColumnData(2, districts)
  }

  resolve()
}

const handleConfirm = ({ value }) => {
  const [provinceId, cityId, regionId] = value

  // 找省索引和label
  const provinceIndex = area.value[0].findIndex((p) => p.value === provinceId)
  const provinceLabel = area.value[0][provinceIndex]?.label || ''

  // 找市索引和label
  const cityIndex = area.value[1][provinceIndex].findIndex((c) => c.value === cityId)
  const cityLabel = area.value[1][provinceIndex][cityIndex]?.label || ''

  // 找区label
  const regionList = area.value[2][provinceIndex]?.[cityIndex] || []
  const regionLabel = regionList.find((r) => r.value === regionId)?.label || ''

  // 设置表单字段
  formMap.value[3].provinceId = provinceId
  formMap.value[3].cityId = cityId
  formMap.value[3].regionId = regionId
  formMap.value[3].area = `${provinceLabel}/${cityLabel}/${regionLabel}`
}

// 经纬度
const handleFetchLocation = () => {
  uni.getLocation({
    type: 'wgs84',
    altitude: false,
    success: async (result) => {
      try {
        const { data } = await fetchAddress({
          lon: result?.longitude,
          lat: result?.latitude,
          ver: 1,
        })

        const poi =
          JSON.parse((data as unknown as string) ?? '')?.result?.addressComponent?.poi ?? ''
        formMap.value[3].longitude = `${result?.longitude ?? ''}`
        formMap.value[3].latitude = `${result?.latitude ?? ''}`
        formMap.value[3].showLocation = poi
      } catch (error) {
        console.log('🚀 ~ success: ~ error:', error)
      }
    },
    fail: (e) => {
      console.log('🚀 ~ e:', e)
      console.log('fail')
    },
    complete: () => {},
  })
}
if (locationStore.locationInfo?.location) {
  formMap.value[3].longitude = `${locationStore.locationInfo?.location.lon ?? ''}`
  formMap.value[3].latitude = `${locationStore.locationInfo?.location.lat ?? ''}`
  formMap.value[3].showLocation = locationStore.locationInfo?.poi
} else {
  handleFetchLocation()
}
// 选择位置
const hanldeLocationChoose = () => {
  uni.chooseLocation({
    success: (res) => {
      console.log('🚀 ~ hanldeLocationChoose ~ res:', res)
      formMap.value[3].longitude = `${res?.longitude ?? ''}`
      formMap.value[3].latitude = `${res?.latitude ?? ''}`
      formMap.value[3].showLocation = res?.name
    },
    fail: (err) => {
      console.log(err)
    },
  })
}

// 门头照
const uploadLogoImageId = createUploadHandler('image')
const handleLogoImageIdChange = ({ fileList }) => {
  formMap.value[3].forShowlogoImageId = fileList
  formMap.value[3].logoImageId = fileList[0]?.response?.fileId
}

// 收银台
const uploadCashRegisterId = createUploadHandler('image')
const handleCashRegisterIdChange = ({ fileList }) => {
  formMap.value[3].forShowCashRegisterId = fileList
  formMap.value[3].cashRegisterId = fileList[0]?.response?.fileId
}

// 内景照
const uploadInteriorShotId = createUploadHandler('image')
const handleInteriorShotIdChange = ({ fileList }) => {
  formMap.value[3].forShowInteriorShotId = fileList
  formMap.value[3].interiorShotId = fileList[0]?.response?.fileId
}

/*
结算信息
*/

// 结算对象类型切换
const handleAcctIdTypeChange = ({ value }) => {
  if (value === 1) {
    formMap.value[4].acctTypeCode = 58
  }
}

// 资金清算授权委托书
const uploadDelegatedAuthorizationId = createUploadHandler('image')
const handleDelegatedAuthorizationIdChange = ({ fileList }) => {
  formMap.value[4].forShowDelegatedAuthorizationId = fileList
  formMap.value[4].delegatedAuthorizationId = fileList[0]?.response?.fileId
}

// 银行卡正面
const uploadBankCardImageId1 = createUploadHandler('image')
const handleBankCardImageId1Change = async ({ fileList }) => {
  formMap.value[4].forShowbankCardImageId1 = fileList
  formMap.value[4].bankCardImageId1 = fileList[0]?.response?.fileId
}
const handleCardImageIdSuccess = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'BankCard' })
      const info = data?.subImages[0]?.kvInfo?.data
      formMap.value[4].acctNo = info?.cardNumber
    } catch (error) {}
  }
}

// 银行卡反面
const uploadBankCardImageId2 = createUploadHandler('image')
const handleBankCardImageId2Change = ({ fileList }) => {
  formMap.value[4].forShowBankCardImageId2 = fileList
  formMap.value[4].bankCardImageId2 = fileList[0]?.response?.fileId
}
const handleCardImageId2Success = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'BankCard' })
      const info = data?.subImages[0]?.kvInfo?.data
      formMap.value[4].acctNo = info?.cardNumber
    } catch (error) {}
  }
}

// 开户行省市区
const bankCode = ref(1)

const bankColumns = ref([])

const handleGetBank = async () => {
  const { data } = await getBankRegistration(bankCode.value)
  bankColumns.value.push(data)
}

const handleBankColumnChange = async ({ selectedItem, resolve, finish }) => {
  const level = getSelectedLevel(selectedItem.code)
  bankCode.value = selectedItem.code

  // 获取下一级（如：选中“市”后拿“区”）
  const { data } = await getBankRegistration(bankCode.value)

  if (data && data.length > 0) {
    // 有子项：清理后续层级 + 追加
    bankColumns.value.splice(level + 1)
    bankColumns.value.push(data)
    resolve(bankColumns.value)
  } else {
    // 没有下一级：尝试获取支行列表
    const { data } = await getBankList(bankCode.value)

    if (data && data.length > 0) {
      // 有支行数据，说明还有一层可选
      bankColumns.value.splice(level + 1)
      bankColumns.value.push(data)
      resolve(bankColumns.value)
    } else {
      // 支行也没有了，直接完成
      finish()
    }
  }
}

// 帮助函数：确定当前选中 code 属于哪一列
function getSelectedLevel(code) {
  for (let i = 0; i < bankColumns.value.length; i++) {
    if (bankColumns.value[i].some((item) => item.code === code)) {
      return i
    }
  }
  return 0 // fallback
}

const handleBankConfirm = async ({ value, selectedItems }) => {
  formMap.value[4].settleProvinceCode = selectedItems[0]?.code
  formMap.value[4].settleProvinceName = selectedItems[0]?.name
  formMap.value[4].settleCityCode = selectedItems[1]?.code
  formMap.value[4].settleCityName = selectedItems[1]?.name
  formMap.value[4].openningBankName = selectedItems[2]?.branchBankName
  formMap.value[4].openningBankCode = selectedItems[2]?.branchBankNo
  formMap.value[4].clearingBankCode = selectedItems[2]?.clearNo

  formMap.value[4].forShowBankInfo = ['', '', selectedItems[2]?.branchBankNo]
}

// 非法人身份证人像面
const uploadAcctIdcardImageId1 = createUploadHandler('image')
const handleAcctIdcardImageId1Change = ({ fileList }) => {
  formMap.value[4].forShowAcctIdcardImageId1 = fileList
  formMap.value[4].acctIdCardImageId1 = fileList[0]?.response?.fileId
}
const handleAcctIdcardImageId1Success = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'IdCard' })
      formMap.value[4].acctName = data?.subImages[0]?.kvInfo?.data?.name
    } catch (error) {
      uni.showToast({ title: '自动识别失败,请正确上传', icon: 'none' })
    }
  }
}

// 非法人身份证国徽面
const uploadAcctIdcardImageId2 = createUploadHandler('image')
const handleAcctIdcardImageId2Change = ({ fileList }) => {
  formMap.value[4].forShowAcctIdcardImageId2 = fileList
  formMap.value[4].acctIdCardImageId2 = fileList[0]?.response?.fileId
}
const handleAcctIdcardImageId2Success = async ({ fileList }) => {
  const url = fileList[0]?.response?.filePath
  if (url) {
    try {
      const { data } = await picRecognize({ url, type: 'IdCard' })
      const sT = data?.subImages[0]?.kvInfo?.data?.validPeriod?.split('-')?.[0]
      const eT = data?.subImages[0]?.kvInfo?.data?.validPeriod?.split('-')?.[1] ?? ''

      formMap.value[4].accountIdDtStart = dayjs(sT, 'YYYY.MM.DD').format('YYYY-MM-DD')
      formMap.value[4].forShowAccountIdDtStart = dayjs(sT, 'YYYY.MM.DD').valueOf()

      if (!eT) {
        formMap.value[4].accountIdDtEnd = '9999-12-31'
        formMap.value[4].isLong = 1
        return
      }

      formMap.value[4].accountIdDtEnd = dayjs(eT).format('YYYY-MM-DD')
      formMap.value[4].forShowAccountIdDtEnd = dayjs(eT).valueOf()
    } catch (error) {
      uni.showToast({ title: '自动识别失败,请正确上传', icon: 'none' })
    }
  }
}

// 非法人身份证有效期
const hanldeIsLongChange = ({ value }) => {
  if (value === 1) formMap.value[4].accountIdDtEnd = '9999-12-31'
}

// 非法人身份证有效期开始时间
const handleAccountIdDtStartChange = ({ value }) => {
  formMap.value[4].accountIdDtStart = dayjs(value).format('YYYY-MM-DD')
}

// 非法人身份证有效期结束时间
const handleAccountIdDtEndChange = ({ value }) => {
  formMap.value[4].accountIdDtEnd = dayjs(value).format('YYYY-MM-DD')
}

// 银行开户许可证
const uploadBankAccountLicenseId = createUploadHandler('image')
const handleBankAccountLicenseIdChange = ({ fileList }) => {
  formMap.value[4].forShowbankAccountLicenseId = fileList
  formMap.value[4].bankAccountLicenseId = fileList[0]?.response?.fileId
}

// 其他补充照片
const uploadOtherImageIds = createUploadHandler('image')
const handleOtherImageIdsChange = ({ fileList }) => {
  formMap.value[4].forShowotherImageIds = fileList
  formMap.value[4].otherImageIds = fileList?.map((item) => item?.response?.fileId)?.join(',') ?? ''
}

const preStep = () => {
  step.value--
}

onShow(() => {
  if (uni.getStorageSync('hasReadMerchartAgreement')) {
    merchartRead.value = true
    // uni.removeStorageSync('hasReadMerchartAgreement')
  }
})

const merchartCheck = ref(false)
const merchartRead = ref(false)
const goMerchart = () => {
  uni.navigateTo({
    url: `/pages-sub/webview/ue?type=merchart`,
  })
}

const handleMerchartClick = () => {
  if (!merchartRead.value) {
    uni.showToast({ title: '请阅读商户入驻协议', icon: 'none' })
  }
}

const { data: applyDetail, run: runDetail } = useRequest(() => userShopApplyDetail())
const applyStatue = computed(() => !!applyDetail?.value?.supplierApplyId)

const helpPop = ref(false)

const qrCodeImg = computed(() => applyDetail?.value?.qrCode)

watch(
  () => applyDetail.value,
  ({ mobile, userName, password }) => {
    if (mobile) formMap.value[0].mobile = mobile
    if (userName) formMap.value[0].userName = userName
    if (password) formMap.value[0].password = password
  },
)

const redirected = ref(false)

watch(
  () => applyDetail.value?.resultUrl,
  (v) => {
    if (v && type.value !== 'edit') {
      redirected.value = true
      uni.redirectTo({ url: `/pages-sub/webview/webview?url=${applyDetail.value?.resultUrl}` })
    }
  },
)

const updateFunMap = {
  0: updateAccount,
  1: updatePersonal,
  2: updateMerchant,
  3: updateShop,
  4: updateSettlement,
}

const checkAllFieldsFilled = () => {
  for (let stepIndex = 0; stepIndex < formRules.value.length; stepIndex++) {
    if (stepIndex === 4) continue

    const stepRules = formRules.value[stepIndex]
    const stepForm = formMap.value[stepIndex]

    for (const field in stepRules) {
      const value = stepForm[field]

      const isEmpty =
        value === '' ||
        value === null ||
        value === undefined ||
        (Array.isArray(value) && value.length === 0)

      if (isEmpty) {
        console.warn(`第 ${stepIndex + 1} 步的字段 ${field} 未填写`)
        uni.showToast({
          title: `请完整填写第 ${stepIndex + 1} 步信息`,
          icon: 'none',
        })
        step.value = stepIndex
        return false
      }
    }
  }
  return true
}
let nextStepTimer: number | null = null

const nextStep = async () => {
  if (nextStepTimer) return

  nextStepTimer = setTimeout(() => {
    nextStepTimer = null
  }, 1000)

  const currentForm = formRefs.value[step.value]
  if (!currentForm) return

  const { valid } = await currentForm.validate()
  if (!valid) return

  if (step.value === 0) {
    if (!merchartCheck.value) {
      uni.showToast({ title: '请阅读商户入驻协议', icon: 'none' })
      return
    }

    if (!applyStatue.value) {
      try {
        const mergedForm = Object.assign({}, ...formMap.value.slice(0, 1))
        updateFunMap[0]?.(formMap.value[0])
        await userSupplierApply(mergedForm)
      } catch (error) {
        uni.showModal({
          title: error?.data?.msg,
          showCancel: true,
        })
        return
      }
    }
  }

  const isLastStep = step.value >= steps.value.length - 1

  updateFunMap[step.value]?.(formMap.value[step.value])

  if (!isLastStep) {
    step.value++
    return
  }

  const allFilled = checkAllFieldsFilled()
  if (!allFilled) return

  try {
    uni.showLoading({ title: '提交中', mask: true })

    const storeParam = formMap.value[3]
    const otherParams = Object.assign({}, ...[0, 1, 2, 4].map((i) => formMap.value[i]))

    const finalParams = {
      ...otherParams,
      storeParam,
    }

    if (recUserId.value) {
      finalParams.userId = recUserId.value
    }

    await userSupplierApply(finalParams)

    uni.hideLoading()
    resetAll()
    await runDetail()

    uni.showToast({ title: '申请成功', icon: 'success', duration: 1000 })

    if (!redirected.value) {
      setTimeout(() => uni.redirectTo({ url: '/pages-my/my-shop/shop-apply-status/index' }), 1200)
    }
  } catch (error) {
    uni.hideLoading()
    uni.showModal({
      title: error?.data?.msg,
      showCancel: true,
    })
  }
}
</script>

<style lang="scss" scoped>
:deep(.wd-picker__cell) {
  padding-left: unset !important;
  padding-right: unset !important;
  background: unset !important;
}
:deep(.wd-picker__label) {
  margin-right: unset !important;
}
:deep(.wd-picker__value) {
  color: #999999 !important;
}

:deep(.wd-col-picker__cell) {
  padding-left: unset !important;
  padding-right: unset !important;
  background: unset !important;
}
:deep(.wd-col-picker__label) {
  margin-right: unset !important;
}
:deep(.wd-col-picker__value) {
  color: #999999 !important;
}
:deep(.wd-picker__cell::after) {
  display: none !important;
}
:deep(.wd-col-picker__cell) {
  padding: unset !important;
}
:deep(.wd-col-picker__cell::after) {
  display: none !important;
}
:deep(.wd-picker__cell) {
  padding: unset !important;
}
</style>
