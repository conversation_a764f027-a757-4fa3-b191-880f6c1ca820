import { defineStore } from 'pinia'
import { fetchLoginSetting } from '@/service'

export const useLoginSettingStore = defineStore(
  'login-setting',
  () => {
    const loginSetting = ref<Api.User.LoginSettingData>()
    // 记录是否第一次获取语音权限成功
    const isAuthorizeRecord = ref<boolean>(false)

    const getLoginSetting = async () => {
      const { data } = await fetchLoginSetting({ appId: import.meta.env.VITE_APP_TITLE })

      setLoginSetting(data)
      initAuthorizeRecord()
    }

    const setLoginSetting = (info: Api.User.LoginSettingData) => {
      loginSetting.value = info
    }

    const clearLoginSetting = () => {
      loginSetting.value = undefined
    }

    const setUserAuthorizeRecord = (isAuthorize: boolean) => {
      isAuthorizeRecord.value = isAuthorize
    }

    const initAuthorizeRecord = () => {
      isAuthorizeRecord.value = false
    }

    return {
      loginSetting,
      isAuthorizeRecord, // 添加这一行
      getLoginSetting,
      setLoginSetting,
      clearLoginSetting,
      setUserAuthorizeRecord,
      initAuthorizeRecord,
    }
  },
  { persist: true },
)
