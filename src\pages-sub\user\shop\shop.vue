<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '申请入驻',
  },
}
</route>

<template>
  <view></view>
</template>

<script setup lang="ts">
import { userShopApplyDetail } from '@/service'
import { useUserSettingStore } from '@/store'
const userSettingStore = useUserSettingStore()

const loading = ref(true)

// 生命周期 - 页面加载
onShow(() => {
  getData()
})

// 获取数据
const getData = async () => {
  loading.value = true
  uni.showLoading({ title: '加载中...' })

  await userSettingStore.fetchUserSetting()
  const status = userSettingStore.userSetting?.supplierStatus

  if (status === 2) {
    uni.redirectTo({ url: '/pages-my/my-shop/index' })
    return
  }

  if (status === 3) {
    uni.hideLoading()
    uni.showModal({
      content: '商户禁用,请联系客服处理',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      },
    })
    return
  }

  const { data } = await userShopApplyDetail()

  if (!data?.supplierApplyId || (!data?.ecNo && !data?.resultUrl)) {
    uni.redirectTo({ url: '/pages-my/my-shop/shop-apply/index' })
    return
  }

  if (!data?.ecNo && data?.resultUrl) {
    uni.redirectTo({ url: `/pages-sub/webview/webview?url=${data?.resultUrl}` })
    return
  }

  if (
    !data?.lakalaLedgerBind ||
    !data?.lakalaLedgerMer ||
    data?.lakalaLedgerBind?.auditStatus !== 1 ||
    data?.lakalaLedgerMer?.auditStatus !== 1
  ) {
    uni.redirectTo({ url: '/pages-my/my-shop/shop-apply-status/index' })
    return
  }
}
</script>
<style scoped>
:deep(.wd-picker__cell) {
  padding-left: unset !important;
  padding-right: unset !important;
  background: unset !important;
}
:deep(.wd-picker__label) {
  margin-right: unset !important;
}
:deep(.wd-picker__value) {
  color: #999999 !important;
}

:deep(.wd-col-picker__cell) {
  padding-left: unset !important;
  padding-right: unset !important;
  background: unset !important;
}
:deep(.wd-col-picker__label) {
  margin-right: unset !important;
}
:deep(.wd-col-picker__value) {
  color: #999999 !important;
}
:deep(.wd-picker__cell::after) {
  display: none !important;
}
:deep(.wd-col-picker__cell) {
  padding: unset !important;
}
:deep(.wd-col-picker__cell::after) {
  display: none !important;
}
:deep(.wd-picker__cell) {
  padding: unset !important;
}
</style>
