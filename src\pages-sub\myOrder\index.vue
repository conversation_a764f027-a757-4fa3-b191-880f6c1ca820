<!-- <route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的订单',
  },
}
</route> -->

<template>
  <view class="overflow-hidden pb-2 box-border h-full">
    <z-paging
      ref="paging"
      v-model="state.listData"
      :default-page-size="10"
      :fixed="false"
      @query="queryList"
    >
      <template #top>
        <div class="flex flex-col w-full">
          <wd-tabs @click="handleSourceTabChange" v-model="state.sourceActive" auto-line-width>
            <block v-for="(ele, index) in state.ordrerTypeTabs" :key="index">
              <wd-tab :title="`${ele.name}`" :name="ele.name"></wd-tab>
            </block>
          </wd-tabs>
          <!-- <wd-tabs
            @click="handleTabChange"
            :slidable="state.ordrerTabs.length > 3 ? 'always' : 'auto'"
            v-model="state.stateActive"
            auto-line-width
          >
            <block v-for="item in state.ordrerTabs" :key="item.key">
              <wd-tab :title="`${item.name}`" :name="item.key"></wd-tab>
            </block>
          </wd-tabs> -->
          <wd-radio-group
            custom-class="flex-row bg-#F2F2F2 flex-row w-full overflow-x-auto whitespace-nowrap p20rpx box-border"
            v-model="state.stateActive"
            shape="button"
            @change="handleTabChange"
          >
            <wd-radio v-for="item in state.ordrerTabs" :key="item.key" :value="item.key">
              {{ item.name }}
            </wd-radio>
          </wd-radio-group>
        </div>
      </template>
      <view class="order-list px20rpx pt20rpx grid grid-cols-1 box-border">
        <view v-for="(item, index) in state.listData" :key="item.orderId">
          <div class="item">
            <!-- 订单头部 -->
            <view class="d-b-c pb40rpx">
              <view
                class="item-dianpu flex items-center"
                @click="ToShop(item.shopSupplierId, item.storeId, item?.storeType)"
              >
                <view class="flex items-center gap-x-15rpx">
                  <div class="i-carbon-store text-32rpx text-black"></div>
                  <text class="text-32rpx text-black" v-if="item.shopSupplierId > 0">
                    {{ item.supplierName }} {{ item?.storeName ? `(${item?.storeName})` : '' }}
                  </text>
                </view>
                <view>
                  <!-- 商城店铺不让进 -->
                  <text
                    v-if="item?.storeType && item?.storeType != 20 && item?.storeId"
                    class="i-carbon-chevron-right"
                  ></text>
                </view>
              </view>
              <!-- 商圈订单（待评价不显示） -->
              <view
                v-if="
                  !(
                    (item.orderStatus == 30 && item.storeType == 30) ||
                    (item.orderStatus == 30 && item.storeType == 40)
                  )
                "
                class="state"
              >
                <text class="text-28rpx text-red">{{ item.orderStatusText }}</text>
              </view>
            </view>

            <!-- 订单信息 -->
            <view
              class="order-head d-b-c"
              @click.stop="goToOrderDetail(item.orderId, item?.storeType)"
            >
              <view class="text-25rpx text-#333333">
                <wd-tag
                  color="#f6220c"
                  bg-color="#ffe7e4"
                  custom-style="padding: 10rpx 12rpx; font-size:22rpx;"
                  custom-class="rounded-4rpx mr10rpx"
                >
                  {{ item.orderSourceText }}
                </wd-tag>
                <text class="">订单号：{{ item.orderNo }}</text>
              </view>
            </view>

            <!-- 商品列表 -->
            <view
              class="flex justify-between flex-wrap relative py20rpx"
              v-if="item.product.length > 1"
              @click="goToOrderDetail(item.orderId, item?.storeType)"
            >
              <scroll-view :scroll-x="true">
                <view class="pr100rpx flex justify-start items-center">
                  <view class="cover mr10rpx" v-for="(img, num) in item.product" :key="num">
                    <image
                      class="w-150rpx h-150rpx rounded-8rpx"
                      :src="img.productImage"
                      mode="aspectFit"
                    />
                  </view>
                </view>
              </scroll-view>
              <view class="total-count pl20rpx">
                <view class="left-shadow"></view>
                <view
                  class="text-#FF7D26 flex justify-center items-end text-22rpx pl20rpx box-border"
                >
                  ¥
                  <text class="text-40rpx">{{ item.payPrice }}</text>
                </view>
                <view class="text-gray text-22rpx">共{{ item.totalNum }}件</view>
              </view>
            </view>

            <!-- 单个商品 -->
            <view
              class="flex items-center justify-center py20rpx"
              v-else
              @click="goToOrderDetail(item.orderId, item?.storeType)"
            >
              <view class="cover" v-for="(img, num) in item.product" :key="num">
                <image
                  :src="img.productImage"
                  class="w-150rpx h-150rpx rounded-8rpx"
                  mode="aspectFit"
                />
              </view>
              <view class="flex-1 text-#333333 text-25rpx px15rpx">
                {{ item.product[0]?.productName }}
              </view>
              <view class="">
                <view class="left-shadow"></view>
                <view class="text-#FF7D26">
                  ¥
                  <text class="text-40rpx theme-price" v-if="item.orderSource === 80">
                    {{ (item.payPrice * 1 + item.advance.payPrice * 1).toFixed(2) }}
                  </text>
                  <text class="" v-else>{{ item.payPrice }}</text>
                </view>
                <!-- 不是线下订单，线下订单不显示 -->
                <view class="text-gray text-22rpx" v-if="item.orderSource !== 100">
                  共{{ item.totalNum }}件
                </view>
              </view>
            </view>

            <!-- 订单操作按钮 -->
            <div class="flex justify-between items-center w-full">
              <wd-popover mode="menu" :content="menu" @menuclick="menulinkCick">
                <div class="text-24rpx px30rpx text-#999999">更多</div>
              </wd-popover>
              <view class="order-bts" v-if="item.orderSource == 80">
                <!-- 预售订单逻辑 -->
                <template v-if="item.orderSource === 80">
                  <!-- 定金/尾款状态 -->
                  <view class="d-b-c line-h-50 f24 gray9" v-if="item.advance">
                    <view>定金</view>
                    <view>
                      <text>{{ item.advance.payStatus === 20 ? '已支付' : '待支付' }}</text>
                      ￥{{ item.advance.payPrice }}
                    </view>
                  </view>
                  <view class="d-b-c line-h-50 f24 gray9" v-if="item.advance">
                    <view>尾款</view>
                    <view>
                      <text>
                        {{
                          item.advance.payStatus === 20 && item.payStatus === 20
                            ? '已支付'
                            : '待支付'
                        }}
                      </text>
                      ￥{{ item.payPrice }}
                    </view>
                  </view>
                </template>
              </view>
              <view class="flex items-center justify-end gap-20rpx" v-else>
                <button
                  v-if="item.orderStatus == 21"
                  @click="retractCancelOrder(item)"
                  style="border: 1px solid #ff7d26"
                  class="text-#FF7D26 m0 rounded-30rpx text-28rpx font-700"
                >
                  撤回取消
                </button>
                <button
                  v-if="item.orderStatus == 20"
                  @click="deleteCancelOrder(item)"
                  style="border: 1px solid #ff7d26"
                  class="text-#FF7D26 m0 rounded-30rpx text-28rpx font-700"
                >
                  删除订单
                </button>
                <block v-if="item.orderStatus == 10">
                  <!-- 未支付取消订单 -->
                  <button
                    v-if="
                      (item.payStatus == 10 && item.storeType === 30) ||
                      (item.payStatus == 10 && item.storeType === 40)
                    "
                    @click="getShowCancelPopup(item)"
                    style="border: 1px solid #ff7d26"
                    class="text-#FF7D26 m0 rounded-30rpx text-28rpx font-700"
                  >
                    取消
                  </button>

                  <button
                    v-if="
                      (item.payStatus == 10 && item.storeType == 10) ||
                      (item.payStatus == 10 && item.storeType == 20)
                    "
                    @click="cancelOrder(item)"
                    style="border: 1px solid #ff7d26"
                    class="text-#FF7D26 m0 rounded-30rpx text-28rpx font-700"
                  >
                    取消
                  </button>
                  <!-- 已支付取消订单 -->
                  <block
                    v-if="
                      (item.payStatus == 20 &&
                        item.deliveryStatus == 10 &&
                        item.storeType === 40) ||
                      (item.storeType === 30 && item.payStatus == 20 && item.deliveryStatus == 10)
                    "
                  >
                    <button
                      @click="getShowCancelPopup(item)"
                      class="m0 rounded-30rpx text-28rpx font-700"
                    >
                      申请退款
                    </button>
                  </block>
                  <block
                    v-if="
                      (item.storeType == 20 && item.payStatus == 20 && item.deliveryStatus == 10) ||
                      (item.storeType == 10 && item.payStatus == 20 && item.deliveryStatus == 10)
                    "
                  >
                    <button @click="cancelOrder(item)" class="m0 rounded-30rpx text-28rpx font-700">
                      申请退款
                    </button>
                  </block>
                  <!-- 订单核销码 -->
                  <template v-if="item.payStatus == 20 && item.deliveryStatus == 10">
                    <block v-if="item.deliveryType == 20">
                      <button
                        v-if="item.orderSource == 30 && item.assembleStatus == 20"
                        @click="onQRCode(item.orderId)"
                        class="m0 rounded-40rpx text-28rpx font-700"
                      >
                        核销码
                      </button>
                      <button
                        v-if="item.orderSource != 30"
                        @click="onQRCode(item.orderId)"
                        class="m0 rounded-40rpx text-28rpx font-700"
                      >
                        核销码
                      </button>
                    </block>
                  </template>
                  <!-- 订单付款 -->
                  <block v-if="item.payStatus == 10">
                    <button
                      class="m0 bg-#FF7D26 rounded-40rpx text-white text-28rpx font-700"
                      @click="onPayOrder(item)"
                    >
                      付款
                    </button>
                  </block>
                  <!-- 确认收货 -->
                  <block v-if="item.deliveryStatus == 20 && item.receiptStatus == 10">
                    <button
                      v-if="item.payType == 20 && item.paySource == 'wx' && state.isSendWx"
                      style="border: 1px solid #ff7d26"
                      class="text-#FF7D26 m0 rounded-30rpx text-28rpx font-700"
                      @click="wxOrder(item)"
                    >
                      小程序确认收货
                    </button>
                    <button
                      v-else
                      style="border: 1px solid #ff7d26"
                      class="text-#FF7D26 m0 rounded-30rpx text-28rpx font-700"
                      @click="orderReceipt(item.orderId)"
                    >
                      确认收货
                    </button>
                  </block>
                </block>

                <!-- 查看物流 -->
                <button
                  class="rounded-30rpx text-#ff5704 border-2rpx border-solid border-#ff5704 text-28rpx m0"
                  v-if="
                    (item.orderStatus == 10 || item.orderStatus == 30) &&
                    (item.deliveryType == 40 || item.deliveryType == 10) &&
                    item.deliveryStatus != 10
                  "
                  @click="
                    gotoPage(
                      `/pages-my/orederExpressDetail/orederExpressDetail?orderId=${item.orderId}&deliveryType=40`,
                    )
                  "
                >
                  查看物流
                </button>

                <!-- 已经结算订单显示  -->
                <button
                  class="text-#FF7D26 border-2rpx border-solid border-#ff5704 m0 rounded-30rpx text-28rpx font-700"
                  v-if="item.orderStatus == 30 && item.isComment == 0"
                  @click="handleredPacketOpen(item?.orderId)"
                >
                  释放红包
                </button>
                <!-- 订单评价 商圈订单（评价不显示）  -->
                <button
                  class="m0 bg-#FF7D26 rounded-40rpx text-28rpx text-white font-700"
                  v-if="
                    item.orderStatus == 30 &&
                    item.isComment == 0 &&
                    item?.storeType != 40 &&
                    item?.storeType != 30
                  "
                  @click="gotoEvaluate(item.orderId)"
                >
                  评价
                </button>
              </view>
            </div>
          </div>
        </view>
      </view>
      <!-- 核销二维码弹窗 -->
      <wd-popup
        v-model="state.isCodeImg"
        custom-style="border-radius:32rpx;width:600rpx;height:600rpx"
        @close="handleHideCodePopup"
      >
        <view class="w-full h-full">
          <wd-img custom-class="w-full h-full" mode="widthFix" :src="state.codeImg"></wd-img>
        </view>
      </wd-popup>
    </z-paging>
    <!-- 商圈订单取消需要写退款原因 -->
    <wd-popup
      v-model="state.showCancelPopup"
      position="center"
      :closeable="true"
      custom-style="width: 600rpx; border-radius: 32rpx; padding: 40rpx;"
      @close="handleClose"
    >
      <view class="flex flex-col gap-4">
        <!-- 弹窗标题 -->
        <view class="text-center text-lg font-bold">填写退款原因</view>

        <!-- 退款原因输入框 -->
        <wd-textarea
          v-model="state.cancelText"
          placeholder="请输入退款原因（选填）"
          :maxlength="200"
          rows="4"
          custom-class="w-full p-2 border border-gray-300 rounded-md"
          custom-style="font-size: 14px;"
        />

        <!-- 操作按钮 -->
        <view class="flex justify-between gap-4 mt-4">
          <button
            @click="handleClose"
            class="flex-1 p-20rpx bg-gray-200 text-gray-700 rounded-30rpx text-sm"
          >
            取消
          </button>
          <button
            @click="cancelReasonOrder(cancelObj)"
            class="flex-1 p-20rpx bg-orange-500 text-white rounded-30rpx text-sm"
          >
            确定
          </button>
        </view>
      </view>
    </wd-popup>
    <wd-popup
      v-if="state.showredPacketPopup"
      v-model="state.showredPacketPopup"
      position="center"
      :closeable="false"
      custom-style="width: 600rpx;
      height: 800rpx;
      border-radius: 32rpx; padding: 0;"
      @close="handleredPacketClose"
    >
      <releaseRedEnvelopeDetails
        :orderId="state.redPacketOrderId"
        @close="handleredPacketClose"
      ></releaseRedEnvelopeDetails>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import {
  CancelUserOrder,
  CancelReasonUserOrder,
  DeleteUserOrder,
  RetractUserOrder,
  fetchUserOrderList,
  ReceiptUserOrder,
  fetchUserOrderCode,
} from '@/service'
import releaseRedEnvelopeDetails from '@/components/releaseRedEnvelopeDetails/releaseRedEnvelopeDetails.vue'
// 引入 onLoad 生命周期钩子
// 响应式状态
const state = reactive({
  redPacketOrderId: '',
  showredPacketPopup: false,
  stateActive: 'all',
  ordrerTabs: [
    // {
    //   key: 'all',
    //   name: '全部',
    // },
    // {
    //   key: 'waitcheck',
    //   name: '待核销',
    // },
    // {
    //   key: 'havecheck',
    //   name: '待评价',
    // },

    {
      key: 'all',
      name: '全部订单',
    },
    {
      key: 'payment',
      name: '待付款',
    },
    {
      key: 'delivery',
      name: '待发货',
    },
    {
      key: 'received',
      name: '待收货/待使用',
    },
    {
      key: 'comment',
      name: '待评价',
    },
  ],
  sourceActive: '全部订单',
  ordrerTypeTabs: [
    {
      // 线上传空，线下传100
      key: 0,
      name: '全部订单',
    },
    {
      // 线上传空，线下传100
      key: 10,
      name: '商圈订单',
    },
    {
      key: 20,
      name: '商城订单',
    },
  ],
  topRefresh: false,
  listData: [] as OrderItem[],
  page: 1,
  listRows: 10,
  lastPage: 0,
  noMore: false,
  isCodeImg: false,
  codeImg: 'https://picsum.photos/346/460',
  mchId: '',
  isSendWx: false,
  showCancelPopup: false,
  cancelText: '',
})
const menu = ref<Array<Record<string, any>>>([
  {
    content: '申请开票',
    eventType: 'openInvoice', // 事件类型标识
  },
  // {
  //   content: '删除订单',
  //     eventType: 'deleteOrder', // 事件类型标识
  // },
])
//防抖处理
let signInTimeout: ReturnType<typeof setTimeout> | null = null
let tabTimer: ReturnType<typeof setTimeout> | null = null
let cancelObj = ref(null)

// 页面加载时触发
onLoad((e) => {
  if (typeof e.dataType !== 'undefined') {
    state.stateActive = e.dataType
  }
  if (e.sourceActive) {
    state.sourceActive = e.sourceActive
    console.log('  e.sourceActive', e.sourceActive)
  }
  // handleSourceTabChange()
})

// 生命周期 - 初始化
onMounted(() => {})
const paging = ref()
onShow(() => {
  paging?.value?.reload()
})
//menu点击
const menulinkCick = (e) => {
  const { item } = e
  const { eventType } = item

  // 根据事件类型执行不同操作
  switch (eventType) {
    case 'openInvoice':
      handleOpenInvoice()
      break
    // case 'deleteOrder':
    //   handleDeleteOrder();
    //   break;
    default:
      console.log('未定义的操作类型', eventType)
  }
}
// 申请开票处理函数
const handleOpenInvoice = () => {
  console.log('触发申请开票逻辑')
  // 打开开票弹窗或页面跳转
  gotoPage(`/pages-sub/orderInvoice/invoiceCenter`)
}

const queryList = async (pageNo: number, pageSize: number) => {
  uni.showLoading({
    title: '加载中',
  })
  try {
    const { data } = await fetchUserOrderList({
      pageIndex: pageNo,
      type: state.stateActive,
      pageSize: pageSize,
      orderType: state.ordrerTypeTabs[sourceTabsIndex.value]?.key,
      appId: import.meta.env.VITE_APPID,
    })
    const records = data.list.records

    paging.value.complete(records)
    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
    paging.value.complete(false)
  }
}
//取消订单弹窗
const handleClose = () => {
  state.showCancelPopup = false
}
const sourceTabsIndex = computed(() =>
  state.ordrerTypeTabs.findIndex((item) => item.name === state.sourceActive),
)
// 订单来源tab
const handleSourceTabChange = () => {
  if (signInTimeout) clearTimeout(signInTimeout)
  signInTimeout = setTimeout(async () => {
    //线下
    const currentKey = state.ordrerTypeTabs[sourceTabsIndex.value]?.key
    if (currentKey === 0) {
      state.ordrerTabs = [
        {
          key: 'all',
          name: '全部订单',
        },
        {
          key: 'payment',
          name: '待付款',
        },
        {
          key: 'delivery',
          name: '待发货',
        },
        {
          key: 'received',
          name: '待收货/待使用',
        },
        {
          key: 'comment',
          name: '待评价',
        },
      ]
    } else if (currentKey === 10) {
      state.ordrerTabs = [
        {
          key: 'all',
          name: '全部',
        },
        {
          key: 'payment',
          name: '待付款',
        },
        {
          key: 'waitcheck',
          name: '待核销',
        },
        // {
        //   key: 'havecheck',
        //   name: '待评价',
        // },
      ]
    } else if (currentKey === 20) {
      state.ordrerTabs = [
        {
          key: 'all',
          name: '全部订单',
        },
        {
          key: 'payment',
          name: '待付款',
        },
        {
          key: 'delivery',
          name: '待发货',
        },
        {
          key: 'received',
          name: '待收货',
        },
        {
          key: 'comment',
          name: '待评价',
        },
      ]
    }
    state.stateActive = state.ordrerTabs[0].key
    paging.value?.reload()
  }, 300)
}
//打开红包弹窗
const handleredPacketOpen = (orderId: any) => {
  state.redPacketOrderId = orderId

  state.showredPacketPopup = true
}
//关闭红包弹窗
const handleredPacketClose = () => {
  state.redPacketOrderId = ''
  state.showredPacketPopup = false
}
// 标签切换处理
const handleTabChange = () => {
  if (tabTimer) clearTimeout(tabTimer)
  tabTimer = setTimeout(async () => {
    paging.value?.reload()
  }, 300)
}
const getShowCancelPopup = (e) => {
  state.showCancelPopup = true
  cancelObj.value = e
}
// 撤回取消订单
const retractCancelOrder = (item: OrderItem) => {
  uni.showModal({
    title: '提示',
    content: '您确定要撤回取消订单吗？',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }
      uni.showLoading({ title: '正在处理' })

      // 发送 API 请求以撤回取消订单
      RetractUserOrder({
        appId: import.meta.env.VITE_APPID,
        orderId: item.orderId,
      })
        .then((res) => {
          uni.hideLoading()
          uni.showToast({ title: res.msg || '操作成功', icon: 'success' })
          paging.value?.reload() // 刷新订单列表
        })
        .catch((error) => {
          uni.hideLoading()
          console.error('撤回取消订单失败:', error)
          uni.showToast({ title: error.data.msg || '操作失败', duration: 2000, icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
    fail: (err) => {
      console.error('显示弹窗失败:', err)
    },
  })
}

// 删除订单
const deleteCancelOrder = (item: OrderItem) => {
  uni.showModal({
    title: '提示',
    content: '您确定要删除订单吗？',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }

      uni.showLoading({ title: '正在处理' })

      // 发送 API 请求以撤回取消订单
      DeleteUserOrder({
        appId: import.meta.env.VITE_APPID,
        orderId: item.orderId,
      })
        .then((res) => {
          uni.hideLoading()
          uni.showToast({ title: res.msg || '删除成功', duration: 2000, icon: 'success' })
          paging.value?.reload() // 刷新订单列表
        })
        .catch((error) => {
          uni.hideLoading()
          uni.showToast({ title: error.data.msg || '删除失败', duration: 2000, icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
    fail: (err) => {
      console.error('显示弹窗失败:', err)
    },
  })
}
//商圈订单申请退款
const cancelReasonOrder = async (item) => {
  let e
  if (item.storeType == 40 || 30) {
    e = cancelObj.value
  } else {
    e = item
  }
  let content = '您确定要取消吗?'
  if (e.isMore === 1) {
    content = '取消订单后，促销优惠将一并取消，是否继续？'
  }

  const result = await uni.showModal({
    title: '提示',
    content: content,
  })

  if (result.confirm) {
    uni.showLoading({
      title: '正在处理',
    })

    try {
      if (state.cancelText) {
        await CancelReasonUserOrder({
          orderId: e.orderId,
          cancelReason: state.cancelText,
        })
      }

      const res = await CancelUserOrder({
        orderId: item.orderId,
      })

      uni.hideLoading()
      state.showCancelPopup = false
      uni.showToast({
        title: res.msg || '操作成功',
        duration: 2000,
        icon: 'success',
      })
      paging.value?.reload()
    } catch (error) {
      uni.hideLoading()
      console.error('取消订单失败:', error)
      state.showCancelPopup = false
      uni.showToast({
        title: '操作失败',
        duration: 2000,
        icon: 'none',
      })
    }
  }
}
//申请取消订单(商城)
const cancelOrder = async (item) => {
  let content = '您确定要取消吗?'
  if (item.isMore === 1) {
    content = '取消订单后，促销优惠将一并取消，是否继续？'
  }

  const result = await uni.showModal({
    title: '提示',
    content: content,
  })

  if (result.confirm) {
    uni.showLoading({
      title: '正在处理',
    })

    try {
      const res = await CancelUserOrder({
        orderId: item.orderId,
        appId: import.meta.env.VITE_APPID,
      })
      uni.hideLoading()
      state.showCancelPopup = false
      uni.showToast({
        title: res.msg || '操作成功',
        duration: 2000,
        icon: 'success',
      })
      paging.value?.reload()
    } catch (error) {
      uni.hideLoading()
      console.error('取消订单失败:', error)
      state.showCancelPopup = false
      uni.showToast({
        title: '操作失败',
        duration: 2000,
        icon: 'none',
      })
    }
  }
}
const gotoProductDetail = (productId: number, storeId: any) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${productId}&storeId=${storeId}` })
}
// 核销二维码模态框
const handleHideCodePopup = () => {
  state.isCodeImg = false
}
//订单核销
const onQRCode = async (orderId: string) => {
  state.isCodeImg = true
  let res = await fetchUserOrderCode({
    orderId: orderId,
    source: 'wx',
  })
  state.codeImg = res.data
  //   state.codeImg = `https://api.shanshang-inc.com/api/order/code?orderId=${orderId}`
}
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
//订单付款
const onPayOrder = (item: any) => {
  uni.navigateTo({
    url: `/pages-sub/goods/pay-order/index?orderId=${item?.orderId}&storeId=${item?.storeId}`,
  })
}
//小程序确认收货
const wxOrder = (item: OrderItem) => {
  // if (wx.openBusinessView) {
  //   wx.openBusinessView({
  //     businessType: "weappOrderConfirm",
  //     extraData: {
  //       merchant_id: state.mchId,
  //       merchant_trade_no: item.tradeNo,
  //       transaction_id: item.transactionId,
  //     },
  //     success() {
  ReceiptUserOrder({
    appId: import.meta.env.VITE_APPID,
    orderId: item.orderId,
  })
    .then((res) => {
      uni.hideLoading()
      uni.showToast({ title: res.msg || '操作成功', duration: 2000, icon: 'success' })
      paging.value?.reload() // 刷新订单列表
    })
    .catch((error) => {
      uni.hideLoading()
      uni.showToast({ title: error.data.msg || '操作失败', duration: 2000, icon: 'none' })
    })
    .finally(() => {
      uni.hideLoading()
    })
}
// 跳转评价
const gotoEvaluate = (orderId: string) => {
  uni.navigateTo({ url: `/pages-sub/evaluate/evaluate?orderId=${orderId}` })
}

// 跳转到店铺
const ToShop = (shopSupplierId: number, storeId: any, storeType: any) => {
  //storeType不等于20才能进店铺
  if (storeType != 20) {
    uni.navigateTo({
      url: `/pages-sub/home/<USER>/supplier/index?shopId=${shopSupplierId}&storeId=${storeId}`,
    })
  }
}
// 跳转订单详情
const goToOrderDetail = (orderId: string, storeType: any) => {
  uni.navigateTo({
    url: `/pages-sub/myOrder/myOrderDetail?orderId=${orderId}&storeType=${storeType}`,
  })
}

// 确认收货
const orderReceipt = (orderId: string) => {
  uni.showModal({
    title: '提示',
    content: '你确定要收货吗？',
    success: (res) => {
      if (!res.confirm) {
        return // 用户取消操作，退出函数
      }
      // 发送 API 请求以撤回取消订单
      ReceiptUserOrder({
        appId: import.meta.env.VITE_APPID,
        orderId: orderId,
      })
        .then((res) => {
          uni.hideLoading()
          uni.showToast({ title: res.msg || '操作成功', duration: 2000, icon: 'success' })
          paging.value?.reload() // 刷新订单列表
        })
        .catch((error) => {
          uni.hideLoading()
          uni.showToast({ title: error.data.msg || '操作失败', duration: 2000, icon: 'none' })
        })
        .finally(() => {
          uni.hideLoading()
        })
    },
  })
}

// 类型声明（实际项目中建议单独文件管理）
interface OrderItem {
  storeType?: any
  storeName?: any
  storeId: number
  deliveryStatus: number
  deliveryType: number
  assembleStatus: number
  receiptStatus: number
  payType: number
  paySource: string
  isMore: number
  isComment: number
  shooperId: number
  orderId: string
  orderStatus: number
  payStatus: number
  shopSupplierId: number
  supplierName: string
  orderSourceText: string
  orderStatusText: string
  orderNo: string
  product: ProductItem[]
  payPrice: number
  totalNum: number
  orderSource: number
  isSendWx: boolean
  advance?: {
    payStatus: number
    payPrice: number
    endTime: string
  }
}

interface ProductItem {
  productImage: string
  productId: number
  productName?: string
  billSourceId: number
}
</script>

<style lang="scss" scoped>
:deep(.flex-row) {
  &::-webkit-scrollbar {
    display: none; // 隐藏滚动条
  }

  // -ms-overflow-style: none; // IE 和 Edge
  // scrollbar-width: none; // Firefox
}
:deep(.wd-radio__label) {
  font-size: 22rpx !important;
  font-weight: 600;
  padding: 10rpx !important;
}
.total-count {
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  padding-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.order-list {
  // padding: 20rpx;
  background: #f2f2f2;

  .item {
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 20rpx;

    .d-b-c {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
