import { reactive, ref } from 'vue'
import { useLoginSettingStore } from '../store'
const loginSettingStore = useLoginSettingStore()

export function useVoiceInput() {
  let voiceTimeout: ReturnType<typeof setTimeout> | null = null
  let countdownTimer: ReturnType<typeof setInterval> | null = null
  // 初始化语音识别插件
  const plugin = requirePlugin('WechatSI')
  const manager = plugin.getRecordRecognitionManager()

  // 记录光标位置
  const cursorPosition = ref(0)

  // 语音输入状态
  const voiceState = reactive({
    isRecording: false, // 是否正在录音
    recordLoading: false, // 录音加载状态
    remainingTime: 15, // 倒计时
  })

  const startCountdown = () => {
    voiceState.remainingTime = 15
    countdownTimer = setInterval(() => {
      voiceState.remainingTime--
      if (voiceState.remainingTime <= 0) {
        clearInterval(countdownTimer!)
        countdownTimer = null
      }
    }, 1000)
  }

  const clearTimers = () => {
    if (voiceTimeout) {
      clearTimeout(voiceTimeout)
      voiceTimeout = null
    }
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }

  // 处理文本框失焦事件，保存光标位置
  const handleTextareaBlur = (e: any) => {
    cursorPosition.value = e.target.cursor || 0
  }

  // 初始化语音识别事件监听
  const initVoiceListeners = (
    updateText: (res: {
      tempFilePath: string
      duration: number
      fileSize: number
      result: string
    }) => void,
  ) => {
    // 录音结束事件
    manager.onStop = (res: {
      tempFilePath: string
      duration: number
      fileSize: number
      result: string
    }) => {
      clearTimers()
      voiceState.isRecording = false
      voiceState.recordLoading = false
      if (res.result) {
        updateText(res)
      }
    }

    // 录音错误事件
    manager.onError = (res) => {
      clearTimers()
      voiceState.isRecording = false
      voiceState.recordLoading = false
      console.error('语音识别错误:', res)
    }

    // 录音开始事件
    manager.onStart = () => {
      voiceState.recordLoading = false
    }
  }
  // 引导用户去设置页
  const showPermissionGuide = () => {
    uni.getSetting({
      success(res) {
        console.log('🚀 ~ success ~ res:', res)
        if (!res.authSetting['scope.record']) {
          // 用户未授权，弹窗引导用户授权
          uni.showModal({
            title: '麦克风权限申请',
            content: '需要获取麦克风权限以便您使用语音功能，请允许开启麦克风权限。',
            confirmText: '去授权',
            cancelText: '暂不授权',
            success(modalRes) {
              if (modalRes.confirm) {
                uni.openSetting({
                  success(settingRes) {
                    if (settingRes.authSetting['scope.record']) {
                      // 用户重新授权了

                      loginSettingStore.initAuthorizeRecord()
                      if (voiceState.isRecording) {
                        // 停止录音
                        voiceState.isRecording = false
                        voiceState.recordLoading = false
                        manager.stop()
                        clearTimers()
                      } else {
                        // 开始录音
                        voiceState.isRecording = true
                        voiceState.recordLoading = true
                        voiceState.remainingTime = 15

                        manager.start({
                          duration: 15000,
                          lang: 'zh_CN',
                        })

                        // 倒计时与自动停止
                        startCountdown()
                        voiceTimeout = setTimeout(() => {
                          if (voiceState.isRecording) {
                            manager.stop()
                            voiceState.isRecording = false
                            voiceState.recordLoading = false
                            clearTimers()
                          }
                        }, 15000)
                      }
                    }
                  },
                })
              }
            },
          })
        }
      },
      fail(err) {
        console.log('🚀 ~ fail ~ err:', err)
      },
    })
  }
  // 处理语音输入按钮按下点击
  const handleVoiceInputTouchStart = (vibrate?: () => void) => {
    // 如果已拒绝过权限，直接引导去设置页
    if (loginSettingStore?.isAuthorizeRecord) {
      showPermissionGuide()
      return
    }

    // 首次申请权限
    vibrate?.()
    uni.authorize({
      scope: 'scope.record',
      success(res) {
        if (voiceState.isRecording) {
          // 停止录音
          voiceState.isRecording = false
          voiceState.recordLoading = false
          manager.stop()
          clearTimers()
        }
      },
      fail(err) {
        loginSettingStore.setUserAuthorizeRecord(true)
      },
    })
  }
  // 处理语音输入按钮松开点击
  const handleVoiceInputTouchEnd = (vibrate?: () => void) => {
    vibrate?.()

    // 开始录音
    voiceState.isRecording = true
    voiceState.recordLoading = true
    voiceState.remainingTime = 15

    manager.start({
      duration: 15000,
      lang: 'zh_CN',
    })

    // 倒计时与自动停止
    startCountdown()
    voiceTimeout = setTimeout(() => {
      if (voiceState.isRecording) {
        manager.stop()
        voiceState.isRecording = false
        voiceState.recordLoading = false
        clearTimers()
      }
    }, 15000)
  }
  // 在文本指定位置插入新内容
  const insertTextAtCursor = (currentText: string, newText: string): string => {
    const position = cursorPosition.value
    return (
      currentText.slice(0, position) +
      (position > 0 && !currentText.endsWith(' ') ? ' ' : '') +
      newText +
      currentText.slice(position)
    )
  }

  // 更新光标位置
  const updateCursorPosition = (currentText: string, insertedText: string) => {
    cursorPosition.value =
      cursorPosition.value +
      insertedText.length +
      (cursorPosition.value > 0 && !currentText.endsWith(' ') ? 1 : 0)
  }

  return {
    voiceState,
    cursorPosition,
    handleTextareaBlur,
    handleVoiceInputTouchStart,
    handleVoiceInputTouchEnd,
    initVoiceListeners,
    insertTextAtCursor,
    updateCursorPosition,
  }
}
