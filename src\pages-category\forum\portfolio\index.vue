<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="s_page w-100% h-100%">
    <div class="s_bg">
      <img
        src="https://file.shanqianqi.com/image/2025/06/24/6ef8031173ff43f39f2b20e15ffd2a2e.png"
        alt=""
      />
      <div class="s_news">
        <div class="s_pos flex justify-center items-center flex-col mr-25%">
          <img
            :src="
              userStore?.userInfo?.avatarUrl
                ? userStore?.userInfo?.avatarUrl
                : 'https://file.shanqianqi.com/image/2025/06/06/5e92b4f0175641d5ac8c93adb2de8175.png'
            "
            alt=""
            class="w-160rpx h-160rpx rounded-16rpx"
            @click="handleUserProfile"
          />
          <span class="mt-20rpx text-36rpx">{{ userStore?.userInfo?.nickName }}</span>
          <div class="mt-10rpx">
            <span class="text-#333333 text-28rpx" @click="attention">
              关注 {{ defaultPage.dataList.follower }}
            </span>
            <span class="inline-block h-30rpx w-2rpx bg-#333333 ml-25rpx mr-25rpx"></span>
            <span class="text-#333333 text-28rpx" @click="myfan">
              粉丝 {{ defaultPage.dataList.followee }}
            </span>
          </div>
        </div>
        <div class="p-40rpx 40rpx">
          <div class="s_choose flex justify-around items-center mt-20%">
            <!-- <div class="flex flex-col justify-center items-center" @click="mycomplain">
              <img
                src="https://file.shanqianqi.com/image/2025/06/21/a9d4cc99ca2b4883ae771bc4bad52971.png"
                alt=""
                class="w-60rpx h-60rpx"
              />
              <span class="text-#333333 text-30rpx mt-20rpx">我的吐槽</span>
              <span class="text-#999999 text-26rpx">({{ defaultPage.dataList.post }})</span>
            </div> -->
            <div class="flex flex-col justify-center items-center" @click="myreply">
              <img
                src="https://file.shanqianqi.com/image/2025/06/24/5b4addedd32f47d1b65243d1223f4ce1.png"
                alt=""
                class="w-60rpx h-60rpx"
              />
              <span class="text-#333333 text-30rpx mt-20rpx">我的回复</span>
              <span class="text-#999999 text-26rpx">({{ defaultPage.dataList.comment }})</span>
            </div>
            <div class="flex flex-col justify-center items-center" @click="myfabu">
              <img
                src="https://file.shanqianqi.com/image/2025/06/24/2494f4aec27842fd9f1e0dd4840dd633.png"
                alt=""
                class="w-60rpx h-60rpx"
              />
              <span class="text-#333333 text-30rpx mt-20rpx">我的发布</span>
              <span class="text-#999999 text-26rpx">({{ defaultPage.dataList.post }})</span>
            </div>
            <!-- <div class="flex flex-col justify-center items-center flex-1" @click="favorite">
              <img
                src="https://file.shanqianqi.com/image/2025/06/28/629d338187fc4b4681b5231feb005b19.png"
                alt=""
                class="w-60rpx h-60rpx"
              />
              <span class="text-#333333 text-30rpx mt-20rpx">获赞/被收藏</span>
              <span class="text-#999999 text-26rpx">({{ defaultPage.dataList.likes }})</span>
            </div> -->
          </div>
        </div>
        <div class="pr-40rpx pl-40rpx">
          <div class="s_choose">
            <div class="flex flex-col justify-center items-center text-34rpx">功能中心</div>
            <div class="flex justify-around items-center mt-30rpx">
              <div class="flex flex-col justify-center items-center" @click="mylove">
                <img
                  src="https://file.shanqianqi.com/image/2025/06/24/7e77dc4c666b40f68cfdf9950221c8c8.png"
                  alt=""
                  class="w-60rpx h-60rpx"
                />
                <span class="text-#333333 text-30rpx mt-20rpx">我的收藏</span>
              </div>
              <div class="flex flex-col justify-center items-center" @click="mylike">
                <img
                  src="https://file.shanqianqi.com/image/2025/06/24/1c9c0144eb0c4252bfe97ca7ff7a6d10.png"
                  alt=""
                  class="w-60rpx h-60rpx"
                />
                <span class="text-#333333 text-30rpx mt-20rpx">我的点赞</span>
              </div>
              <div class="flex flex-col justify-center items-center" @click="myhistory">
                <img
                  src="https://file.shanqianqi.com/image/2025/06/24/7cb13afb131943d88c57af82f1a6d1fc.png"
                  alt=""
                  class="w-60rpx h-60rpx"
                />
                <span class="text-#333333 text-30rpx mt-20rpx">浏览历史</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </view>
  <Tabbar></Tabbar>
</template>

<script lang="ts" setup>
import Tabbar from '@/components/ForumTabBar/index.vue'

import { useUserStore } from '@/store'
import { doLogin } from '@/utils'
import { centerCenter } from '@/service'
onShow(() => {
  mycenter()
})
// onLoad(async () => {
//   mycenter()
// })

const defaultPage = reactive({
  dataList: {},
})
const userStore = useUserStore()

//跳转设置
const handleUserProfile = () => {
  if (userStore?.isLogined) {
    uni.navigateTo({ url: '/pages-sub/user/user-detail/index' })
  } else {
    doLogin()
    // uni.navigateTo({ url: '/pages-sub/login/login' })
  }
}

//个人中心
const mycenter = async () => {
  const { data } = await centerCenter({})
  console.log('datadata', data.center)
  defaultPage.dataList = data.center
  console.log('defaultPage.dataList.follower', defaultPage.dataList.likes)
}
//跳转我的关注
const attention = () => {
  uni.navigateTo({
    url: '/pages-category/forum/attention/index',
  })
}
//跳转论坛首页
const goFanhui = () => {
  uni.navigateTo({
    url: '/pages-category/forum/index',
  })
}

//跳转我的粉丝
const myfan = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myFan/index',
  })
}

//跳转我的收藏
const mylove = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myLove/index',
  })
}

//跳转我的点赞
const mylike = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myLike/index',
  })
}

//跳转我的浏览历史
const myhistory = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myHistory/index',
  })
}

//跳转我的吐槽
const mycomplain = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myComplain/index',
  })
}

//跳转我的回复
const myreply = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myReply/index',
  })
}

//跳转我的发布
const myfabu = () => {
  uni.navigateTo({
    url: '/pages-category/forum/myFabu/index',
  })
}

//跳转获赞/被收藏
const favorite = () => {
  uni.navigateTo({
    url: '/pages-category/forum/favoriteAndlike/index',
  })
}
</script>

<style lang="scss" scoped>
.s_page {
  overflow: hidden;
}
.s_bg {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  img {
    width: 100%;
    height: 100%;
  }
  .s_news {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 10%;
    left: 0;
  }
  .s_choose {
    padding: 48rpx 0;
    border: 2px #a5c7f7 solid;
    background: #ffffff4d;
    backdrop-filter: blur(20rpx);
    border-radius: 20rpx;
  }
}
</style>
