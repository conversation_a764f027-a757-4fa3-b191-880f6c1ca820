declare namespace Api {
  namespace Home {
    interface UnderLineProductListByCategoryIdItem {
      addSource: number
      agentMoneyType: number
      aloneGradeEquity: string
      aloneGradeType: number
      appId: number
      auditRemark: string
      auditStatus: number
      categoryId: number
      content: string
      contentPosterId: number
      contentVideoId: number
      createTime: string
      customFormStr: string
      deductStockType: number
      deliveryId: number
      extraRedPocketPrice: string
      firstMoney: string
      gradeIds: string
      highPrice: string
      isAgent: number
      isAloneGrade: number
      isDelete: number
      isEnableGrade: number
      isIndAgent: number
      isPicture: number
      isPointsDiscount: number
      isPointsGift: number
      isPreview: number
      isVirtual: number
      limitNum: number
      linePrice: string
      maxPointsDiscount: number
      posterId: number
      previewTime: null | string
      productId: number
      productImage: string
      productName: string
      productNo: string
      productPrice: string
      productSearchVoList: unknown
      productSort: number
      productStatus: number
      productStock: number
      productSupplierScoreRate: unknown
      productUserScoreRate: unknown
      salesActual: number
      salesInitial: number
      secondMoney: string
      sellingPoint: string
      shopSupplierId: number
      specType: number
      storeType: null | number
      supplierCommissionRate: string
      supplierPrice: string
      tableId: number
      thirdMoney: string
      updateTime: string
      useShanBeanRate: string
      videoId: number
      videoLink: null | string
      videoType: number
      viewTimes: number
      virtualAuto: number
      virtualContent: string
    }
    interface UnderLineProductListByCategoryIdData {
      countId: null | number
      current: number
      hitCount: boolean
      maxLimit: null | number
      optimizeCountSql: boolean
      orders: unknown[]
      pages: number
      records: UnderLineProductListByCategoryIdItem[]
      searchCount: boolean
      size: number
      total: number
    }

    interface SupplierCategoryListItem {
      appId: number
      categoryId: number
      children: null | SupplierCategoryListItem[]
      createTime: string
      depositMoney: string
      isVisible: string
      name: string
      parent: unknown
      parentId: 11
      picPath: string
      sort: number
      turnValue: string
      updateTime: null | string
    }

    interface CascaderOption {
      checked: unknown
      childIndex: unknown
      children: CascaderOption[]
      id: number
      index: unknown
      level: number
      name: string
      pid: number
    }
    interface HomeStoreListItem {
      shopsupplierId?: any
      address: string
      appId: number
      area: string
      cityId: number
      createTime: string
      geohash: string
      distance: number
      businessStartTime?: string
      businessEndTime?: string
      supplierCommissionRate: string
      isCheck: number
      isDelete: number
      keyword: null | string
      latitude: string
      linkman: string
      logoFilePath: string
      logoImageId: number
      longitude: string
      pageIndex: number
      pageSize: number
      phone: string
      productSales: number
      provinceId: number
      regionId: number
      serverScore: string
      shopHours: unknown
      shopSupplierId: number
      sort: number
      startIndex: number
      status: number
      storeId: number
      storeName: string
      storeType: number
      summary: string
      supplierName: string
      updateTime: null | string
      productListVo: productItem[]
    }
    interface HomeStoreData {
      lastPage: null | number
      pageIndex: number
      pageSize: number
      total: number
      records: HomeStoreListItem[]
    }
    interface AddressBody {
      lon: number
      lat: number
      ver: number
    }

    interface AddressData {
      msg: string
      result: {
        addressComponent: {
          address: string
          address_distance: number
          address_position: string
          city: string
          city_code: string
          county: string
          county_code: string
          nation: string
          poi: string
          poi_distance: number
          poi_position: string
          province: string
          province_code: string
          road: string
          road_distance: number
          town: string
          town_code: number
        }
        formatted_address: string
        location: {
          lat: number
          lon: number
        }
      }
      status: string
    }
    interface GoodsListBody {
      pageIndex: number
      search?: string
      sortType?: string
      sortPrice: number
      pageSize: number
      appId: number
      type?: string
      shopSupplierId?: string
      categoryId?: number
      visitType?: number
      userId?: number
    }
    interface GoodItem {
      isShowBeanValue?: string
      beanDeductionValue?: string
      extraRedPocketPrice?: any
      productId: number
      productName: string
      productPrice: string
      linePrice: string
      productNo: string
      productStock: number
      sellingPoint: string
      categoryId: number
      specType: number
      productSales: number
      isPointsGift: number
      isPointsDiscount: number
      maxPointsDiscount: number
      isEnableGrade: number
      isAloneGrade: number
      isVirtual: number
      aloneGradeEquity: Record<string, any>
      aloneGradeType: number
      viewTimes: number
      limitNum: number
      gradeIds: string
      isUserGrade: boolean
      addSource: number
      productImage: string
      shopSupplierId: number
      supplierPrice: string
      supplier: {
        address: string
        name: string
        shopSupplierId: number
        logoId: number
      }
      seckill: unknow
      isPreview: number
      previewTime: unknow
      previewTimeStamp: number
      isActivity: boolean
    }
    interface commentList {
      avatarUrl: string
      nickName: string
      content: string
      createTime: string
      score: number
      image: any[]
    }
    interface GoodsListData {
      total: number
      records: GoodItem[]
      pageIndex: number
      pageSize: number
      lastPage: number
    }

    interface GoodDetail {
      serviceType: number
      cartTotalNum: number
      commentData: unknown[]
      serviceOpen: boolean
      commentDataCount: number
      discount: {
        productReduce: []
        productCoupon: []
        givePoints: number
        showDiscount: boolean
      }
      share: {
        signPackage: unknow
        shareParams: Record<string, any>
      }
      detail: {
        isShowBeanValue?: string
        beanDeductionValue?: string
        scoreDeductionValue?: string
        productId: number
        productName: string
        extraRedPocketPrice?: any
        productPrice: string
        linePrice: string
        highPrice: string
        productNo: string
        productStock: number
        videoId: number
        posterId: number
        videoType: number
        videoLink: string
        sellingPoint: string
        categoryId: number
        specType: number
        deductStockType: number
        content: string
        salesInitial: number
        salesActual: number
        productSort: number
        deliveryId: number
        isPointsGift: number
        isPointsDiscount: number
        maxPointsDiscount: number
        isEnableGrade: number
        isAloneGrade: number
        isAgent: number
        aloneGradeEquity: string
        aloneGradeType: number
        productStatus: number
        viewTimes: number
        isVirtual: number
        limitNum: number
        gradeIds: string
        virtualAuto: number
        virtualContent: string
        isPicture: number
        contentVideoId: number
        contentPosterId: number
        tableId: number
        shopSupplierId: number
        supplierPrice: string
        auditStatus: number
        auditRemark: string
        isIndAgent: number
        agentMoneyType: number
        isPreview: number
        addSource: number
        previewTime: unknow
        firstMoney: string
        secondMoney: string
        thirdMoney: string
        customFormStr: string
        isDelete: number
        appId: number
        createTime: string
        updateTime: string
        image: {
          imageId: number
          filePath: string
        }[]
        contentImage: unknow
        skuList: {
          productSkuId: number
          productId: number
          specSkuId: string
          imageId: number
          productNo: string
          productPrice: string
          linePrice: string
          lowPrice: string
          stockNum: number
          productSales: number
          productWeight: number
          appId: number
          createTime: string
          updateTime: unknow
          imagePath: string
          productAttr: unknow
        }[]
        productSku: {
          productSkuId: number
          productId: number
          specSkuId: string
          imageId: number
          productNo: string
          productPrice: string
          linePrice: string
          lowPrice: string
          stockNum: number
          productSales: number
          productWeight: number
          appId: number
          createTime: string
          updateTime: unknow
          imagePath: string
          productAttr: unknow
        }
        specList: unknow
        deliveryRule: unknow
        commentData: unknow
        productSales: number
        isUserGrade: boolean
        previewTimeStamp: number
        supplier: {
          shopSupplierId: number
          name: string
          favCount: number
          logoId: number
          categoryId: number
          serverScore: string
          productSales: number
          logoFilePath: string
          categoryName: string
          storeType: number
          supplierUserId: number
          serviceUserId: number
          nickName: unknow
        }
        server: []
        videoFilePath: string
        posterFilePath: string
        seckill: unknow
        advance: unknow
        aloneGradeEquityJson: unknow
        sku: unknow
        customForm: unknow
        contentPoster: unknow
        contentVideo: unknow
      }
      specData: unknow
      storeOpen: number
      isFav: boolean
      serviceUser: {
        serviceUserId: number
        userId: unknow
        userName: unknow
        salt: unknow
        password: unknow
        nickName: unknow
        headPortrait: unknow
        shopSupplierId: unknow
        type: unknow
        sort: unknow
        statu: unknow
        isDelete: unknow
        appId: unknow
        createTime: unknow
        updateTime: unknow
      }
      supplier: {
        categoryId: number
        categoryName: string
        favCount: number
        logoFilePath: string
        logoId: number
        name: string
        nickName: string | null
        productSales: number
        serverScore: string
        serviceUserId: number
        shopSupplierId: number
        storeType: number
        supplierUserId: number
      }
      userVisit: {
        appId: number
        content: null
        createTime: string
        productId: number
        shopSupplierId: number
        storeId: number
        updateTime: null
        userId: number
        visitId: number
        visitType: number
        visitcode: null
      }
    }

    interface GoodCategoryItem {
      appId: number
      categoryId: number
      children: GoodCategoryItem[]
      createTime: string
      imageId: number
      imagePath: string
      name: string
      parent: unknown
      parentId: number
      sort: number
      updateTime: string
    }
    interface GoodCategory {
      list: GoodCategoryItem[]
      template: {
        categoryStyle: number
        shareTitle: string
        windStyle: number
      }
    }
    interface SupplierCategoryItem {
      appId: number
      categoryId: any
      children: SupplierCategoryItem[]
      createTime: string
      depositMoney: string
      name: string
      parent: null | number
      parentId: number | string
      picPath: string
      updateTime: number
    }

    interface ShopCategoryItem {
      categoryId: number
      depositMoney: string
      name: string
      picPath: string
    }
    interface ShopCategory {
      list: ShopCategoryItem[]
      smsOpen: boolean
    }

    interface CartListProductItem {
      swipeAction?: any
      aloneGradeEquity: string
      aloneGradeType: number
      appId: number
      cartId: number
      createTime: string
      isAloneGrade: number
      isDelete: number
      isEnableGrade: number
      isUserGrade: boolean
      joinPrice: string
      productAttr: string
      productId: number
      productImage: string
      productName: string
      productPrice: string
      productStatus: number
      shopSupplierId: number
      specSkuId: string
      specType: number
      stockNum: number
      totalNum: number
      updateTime: unknown
      userId: number
      checked?: boolean
    }

    interface CartListItem {
      productList: CartListProductItem[]
      shopSupplierId: number
      supplier: {
        name: string
        shopSupplierId: number
      }
      checked?: boolean
      cartId?: string
      checkList?: string[]
    }
    interface CartData {
      storeOpen: number
      productList: CartListItem[]
    }

    interface PageItems {
      data:
        | {
            color?: string
            imgName?: string
            imgUrl?: string
            isVisible?: string
            linkUrl?: string
            name?: string
            text?: string
          }[]
        | RecItem[]
      defaultData: unknown
      group: string
      icon: string
      name: string
      params: { dataNum: number }
      style: {
        background: string
        layout: number
        paddingBottom: number
        paddingLeft: number
        paddingTop: number
      }
      type: string
    }
    interface AppSetting {
      page: {
        items: PageItems[]
        page: {
          category: { color: string; open: number }
          name: string
          params: {
            icon: string
            name: string
            shareImg: string
            shareTitle: string
            title: string
            titleType: string
            toplogo: string
          }
          style: { titleTextColor: string; titleBackgroundColor: string }
          type: string
        }
      }
      service: {
        appId: unknown
        createTime: unknown
        headPortrait: unknown
        isDelete: unknown
        nickName: unknown
        password: unknown
        salt: unknown
        serviceUserId: number
        shopSupplierId: unknown
        sort: unknown
        statu: unknown
        type: unknown
        updateTime: unknown
        userId: unknown
        userName: unknown
      }
      setting: {
        collection: {
          status: string
        }
        homepush: {
          des: string
          filePath: string
          imageId: number
          isOpen: boolean
          link: { name: string; id: number; type: string; url: string }
          remark: string
          title: string
          type: string
        }
        officia: {
          status: string
        }
      }
    }
  }
  namespace Order {
    interface StoreListBody {
      longitude: number
      latitude: number
      shopSupplierId: number
      url?: string
      token: string
      appId: number
    }
    interface StoreListItem {
      address: string
      appId: number
      area: unknown
      city: string
      cityId: number
      coordinate: unknown
      createTime: string
      distinct: number
      geohash: string
      isCheck: number
      isCheckText: string
      isDelete: number
      latitude: string
      linkman: string
      logoImageId: number
      logoImagePath: string
      longitude: string
      phone: string
      province: string
      provinceId: number
      region: string
      regionId: number
      shopHours: string
      shopSupplierId: number
      showDistinct: string
      sort: number
      status: number
      statusText: string
      storeId: number
      storeName: string
      summary: string
      updateTime: string
    }
    interface PreBuyBody {
      appId: number
      couponId?: number
      delivery: number
      isUsePoints: number
      paySource: string
      productId: string
      productNum: string
      roomId?: number
      specSkuId?: string
      storeId: number | string
      supplier?: {
        couponId?: number
        delivery?: number
        remark?: string
        storeId?: number
      }
    }

    interface PreBuyData {
      points?: number
      redPacket?: string
      settledRule: {
        isCoupon: boolean
        isUsePoints: boolean
        forcePoints: boolean
        isUserGrade: boolean
        isAgent: boolean
      }
      orderData: {
        delivery: unknown
        address: {
          addressId: number
          district: string
          name: string
          phone: string
          detail: string
          provinceId: number
          cityId: number
          regionId: number
          userId: number
          region: {
            province: string
            city: string
            region: string
          }
        }
        existAddress: boolean
        expressPrice: unknown
        intraRegion: unknown
        extractStore: unknown
        isAllowPoints: boolean
        isUsePoints: boolean
        lastExtract: unknown
        deliverySetting: unknown
        orderPrice: unknown
        orderPayPrice: string
        orderTotalPrice: string
        pointsBonus: unknown
        pointsMoney: string
        pointsNum: unknown
        couponId: unknown
        couponMoney: any
        reduce: unknown
        productReduceMoney: any
        couponIdSys: unknown
        couponMoneySys: any
        couponList: unknown[]
        roomId: number
        money: unknown
        reduceMoney: unknown
        balancePayment: unknown
        advanceStartTime: unknown
        advanceEndTime: unknown
      }
      supplierList: {
        shopSupplierId: number
        productList: {
          productId: number
          productName: string
          productPrice: string
          linePrice: string
          highPrice: string
          productNo: string
          productStock: number
          videoId: number
          posterId: number
          videoType: number
          videoLink: string
          sellingPoint: string
          categoryId: number
          specType: number
          deductStockType: number
          content: string
          salesInitial: number
          salesActual: number
          productSort: number
          deliveryId: number
          isPointsGift: number
          isPointsDiscount: number
          maxPointsDiscount: number
          isEnableGrade: number
          isAloneGrade: number
          isAgent: number
          aloneGradeEquity: string
          aloneGradeType: number
          productStatus: number
          viewTimes: number
          isVirtual: number
          limitNum: number
          gradeIds: string
          virtualAuto: number
          virtualContent: string
          isPicture: number
          contentVideoId: number
          contentPosterId: number
          tableId: number
          shopSupplierId: number
          supplierPrice: string
          auditStatus: number
          auditRemark: string
          isIndAgent: number
          agentMoneyType: number
          isPreview: number
          addSource: number
          previewTime: unknown
          firstMoney: string
          secondMoney: string
          thirdMoney: string
          customFormStr: string
          isDelete: number
          appId: number
          createTime: string
          updateTime: string
          productSku: {
            productSkuId: number
            productId: number
            specSkuId: string
            imageId: number
            productNo: string
            productPrice: string
            linePrice: string
            lowPrice: string
            stockNum: number
            productSales: number
            productWeight: number
            appId: number
            createTime: string
            updateTime: unknown
            imagePath: string
            productAttr: string
          }
          totalNum: number
          specSkuId: string
          totalPrice: string
          totalPayPrice: string
          productImage: string
          pointsBonus: number
          maxPointsNum: number
          pointsNum: number
          pointsMoney: string
          expressPrice: unknown
          deliveryRuleList: unknown
          delivery: unknown
          couponMoney: string
          isUserGrade: boolean
          gradeRatio: string
          gradeProductPrice: string
          gradeTotalMoney: string
          fullReduceMoney: unknown
          productReduceMoney: string
          pointSku: unknown
          seckillSku: unknown
          advanceSku: unknown
          assembleSku: unknown
          bargainSku: unknown
          billSourceId: unknown
          activityId: unknown
          productSourceId: unknown
          skuSourceId: unknown
          couponMoneySys: string
          supplierMoney: unknown
          sysMoney: string
          customForm: unknown
        }[]
        supplier: {
          shopSupplierId: number
          name: string
        }
        orderData: {
          delivery: number
          address: {
            addressId: number
            district: string
            name: string
            phone: string
            detail: string
            provinceId: number
            cityId: number
            regionId: number
            userId: number
            region: {
              province: string
              city: string
              region: string
            }
          }
          existAddress: boolean
          expressPrice: string
          intraRegion: boolean
          extractStore: {
            storeId: number
            storeName: string
            logoImageId: number
            linkman: string
            phone: string
            shopHours: string
            provinceId: number
            cityId: number
            regionId: number
            address: string
            longitude: string
            latitude: string
            geohash: string
            summary: string
            sort: number
            isCheck: number
            status: number
            isDelete: number
            appId: number
            createTime: string
            updateTime: string
            shopSupplierId: number
            coordinate: unknown
            logoImagePath: string
            province: string
            city: string
            region: string
            distinct: unknown
            showDistinct: unknown
            statusText: string
            isCheckText: string
          }
          isAllowPoints: boolean
          isUsePoints: boolean
          lastExtract: unknown
          deliverySetting: [10, 20]
          orderPrice: string
          orderPayPrice: string
          orderTotalPrice: string
          pointsBonus: number
          pointsMoney: string
          pointsNum: number
          couponId: number
          couponMoney: string
          couponList: unknown[]
          reduce: any
          productReduceMoney: string
          storeId: number
          couponIdSys: unknown
          couponMoneySys: unknown
          supplierMoney: string
          sysMoney: string
        }
      }[]
      storeOpen: number
      templateArr: []
    }

    interface OrderBuy {
      appId: number
      couponId?: unknown
      customForm?: unknown
      isUsePoints: number
      productId?: string
      productNum?: string
      cartIds?: string
      roomId?: number
      specSkuId?: string
      supplier: Record<
        number,
        { couponId?: number; delivery: number; remark: string; storeId: number }
      >
    }

    interface CommentList {
      total: {
        all: number
        negative: number
        praise: number
        review: number
      }
      comments: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: unknown[]
        total: number
      }
    }

    interface RecItem {
      addSource: number
      aloneGradeEquity: string
      aloneGradeType: number
      categoryId: number
      gradeIds: string
      isActivity: boolean
      isAloneGrade: number
      isEnableGrade: number
      isPointsDiscount: number
      isPointsGift: number
      isPreview: number
      isUserGrade: boolean
      isVirtual: number
      limitNum: number
      linePrice: string
      maxPointsDiscount: number
      previewTime: unknown
      previewTimeStamp: number
      productId: number
      productImage: string
      productName: string
      productNo: string
      productPrice: string
      productSales: number
      productStock: number
      seckill: unknown
      sellingPoint: string
      shopSupplierId: number
      specType: number
      supplier: { name: string; shopSupplierId: number; logoId: number }
      supplierPrice: string
      viewTimes: number
    }
    interface RecData {
      isRecommend: boolean
      recommendName: string
      list: RecItem[]
    }
  }

  namespace Supplier {
    interface storeListItem {
      address: string
      announcerId: null | number | string
      appId: number
      area: string
      cityId: number
      createTime: string
      geohash: string
      isCheck: number
      isDelete: number
      latitude: string
      linkman: string
      logoImageId: number
      longitude: string
      phone: string
      provinceId: number
      regionId: number
      shopHours: string
      shopSupplierId: number
      sort: number
      status: number
      storeId: number
      storeName: string
      summary: string
      updateTime: null | string
    }
    interface SupplierData {
      storelist: storeListItem[]
      isRecycle: number
    }
    interface ProductItem {
      addSource: number
      aloneGradeEquity: string
      aloneGradeType: number
      categoryId: number
      gradeIds: string
      isActivity: unknown
      isAloneGrade: number
      isEnableGrade: number
      isPointsDiscount: number
      isPointsGift: number
      isPreview: number
      isUserGrade: unknown
      isVirtual: number
      limitNum: number
      linePrice: string
      maxPointsDiscount: number
      previewTime: unknown
      previewTimeStamp: unknown
      productId: number
      productImage: string
      productName: string
      productNo: string
      productPrice: string
      productSales: unknown
      productStock: number
      seckill: unknown
      sellingPoint: string
      shopSupplierId: number
      specType: number
      supplier: unknown
      supplierPrice: string
      viewTimes: number
    }
    interface SupplierItem {
      categoryId: number
      storeName?: any
      storeId?: any
      categoryName: string
      favCount: number
      logoFilePath: string
      logoId: number
      name: string
      productList: ProductItem[]
      productSales: number
      score: unknown
      serverScore: string
      shopSupplierId: number
    }
    interface SupplierData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: SupplierItem[]
      total: number
    }

    interface SupplierDataDetail {
      storeId?: any
      storeName?: string
      area: string
      address: string
      appId: number
      backgroundPath: string
      businessFilePath: string
      businessId: number
      cashMoney: string
      categoryId: number
      categoryName: string
      createTime: string
      depositMoney: string
      describeScore: string
      description: unknown
      expressScore: string
      favCount: number
      freezeMoney: string
      giftMoney: number
      isDelete: number
      isFull: number
      isOpenErp: boolean
      isRecycle: number
      isfollow: number
      jjyAppkey: string
      jjyPrivatekey: unknown
      jjyPublickey: unknown
      jjySecret: string
      jjyUrl: string
      linkName: string
      linkPhone: string
      logoFilePath: string
      logoId: number
      money: string
      name: string
      nickName: unknown
      openService: number
      productList: unknown
      productSales: number
      realName: string
      score: string
      serverScore: string
      serviceUserId: number
      shopSupplierId: number
      status: number
      storeType: number
      supplierUserId: number
      totalGift: number
      totalMoney: string
      updateTime: unknown
      userId: number
    }
    interface SupplierHome {
      adList: unknown[]
      couponList: unknown[]
      supplier?: any
      detail: SupplierDataDetail
      store: any
      service: unknown
      serviceOpen: boolean
      serviceType: number
      userVisit: {
        appId: number
        content: null
        createTime: string
        productId: number
        shopSupplierId: number
        storeId: number
        updateTime: null
        userId: number
        visitId: number
        visitType: number
        visitcode: null
      }
    }
  }

  namespace Store {
    interface StoreBody {
      appId: number
      storeName?: string
      pageIndex: number
      pageSize: number
      sortType?: string
      categoryId?: number
      productName?: string
      lon: number
      lat: number
      ver: '1'
    }

    interface productItem {
      productEnumPrice?: any
      beanDeductionValue?: string
      isShowBeanValue?: string
      productId: number
      productName: string
      productPrice: string
      linePrice: string
      highPrice: string
      productImage: string
      productNo: string
      productStock: number
      videoId: number
      posterId: number
      videoType: number
      videoLink: string
      sellingPoint: string
      categoryId: number
      specType: number
      deductStockType: number
      content: string
      salesInitial: number
      salesActual: number
      productSort: number
      deliveryId: number
      isPointsGift: number
      isPointsDiscount: number
      maxPointsDiscount: number
      isEnableGrade: number
      isAloneGrade: number
      isAgent: number
      aloneGradeEquity: string
      aloneGradeType: number
      productStatus: number
      viewTimes: number
      isVirtual: number
      limitNum: number
      gradeIds: string
      virtualAuto: number
      virtualContent: string
      isPicture: number
      contentVideoId: number
      contentPosterId: number
      tableId: number
      shopSupplierId: number
      supplierPrice: string
      auditStatus: number
      auditRemark: string
      isIndAgent: number
      agentMoneyType: number
      isPreview: number
      addSource: number
      previewTime: unknown
      productSales: number
      firstMoney: string
      secondMoney: string
      thirdMoney: string
      customFormStr: string
      isDelete: number
      appId: number
      useShanBeanRate: string
      productSupplierScoreRate: string
      productUserScoreRate: string
      supplierCommissionRate: string
      extraRedPocketPrice: string
      createTime: string
      updateTime: string
    }

    interface StoreListItem {
      pageIndex: number
      startIndex: number
      pageSize: number
      logoFilePath: string
      keyword: unknown
      storeId: number
      storeName: string
      logoImageId: number
      linkman: string
      phone: string
      shopHours: unknown
      serverScore: string
      provinceId: number
      supplierName: string
      cityId: number
      regionId: number
      area: string
      address: string
      longitude: string
      latitude: string
      geohash: string
      summary: string
      sort: number
      isCheck: number
      status: number
      isDelete: number
      appId: number
      createTime: string
      updateTime: string
      shopSupplierId: number
      productList: productItem[]
    }

    interface StoreListData {
      total: number
      records: StoreListItem[]
      pageIndex: number
      pageSize: number
      lastPage: number
    }

    interface SelfListData {
      total: number
      records: productItem[]
      pageIndex: number
      pageSize: number
      lastPage: number
    }
  }

  namespace User {
    interface orderCapitalItem {
      appId: number
      createTime: string
      description: string
      flowType: number
      id: number
      money: string
      orderId: null | number
      updateTime: null | string
      userId: number
      nickName: string
      storeName: string
    }
    interface orderCapitalData {
      identity: {
        isCity: boolean
        isExecutive: boolean
        isProvince: boolean
        isRegion: boolean
      }
      page: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: orderCapitalItem[]
        total: number
      }
    }
    interface TeamPerformanceItem {
      completePerformance: string
      nickName: string
      performanceRatio: string
      position: string
      userId: number
    }
    interface TeamPerformanceData {
      total: number
      records: TeamPerformanceItem[]
      pageIndex: number
      pageSize: number
      lastPage: number
    }
    interface MyteamUserItem {
      userId: number
      avatarUrl: string
      nickName: string
      createTime: string
      isSupplier: boolean
      payMoney: string
    }
    interface MyTeamUserData {
      list: {
        records: MyteamUserItem[]
        total: number
        pages: number
        size: number
        countId: null | number
        current: number
        hitCount: boolean
        maxLimit: null | number
        optimizeCountSql: boolean
        orders: any[]
        searchCount: boolean
      }
      totalPayMoney: string
    }
    interface MyteamShopItem {
      shopSupplierId: number
      name: string
      logoId: number
      avatarUrl: string
      createTime: boolean
      salesMoney: string
      supplierCommissionRate: string
      sysMoney: string
    }
    interface MyTeamShopData {
      list: {
        records: MyteamShopItem[]
        total: number
        pageIndex: number
        pageSize: number
        lastPage: number
      }
      performance: {
        totalMoney: string
        totalSysMoney: string
      }
    }
    interface evaluate {
      appId: number
      orderProductId: number | string
      productId: number | string
      score: number | string
      imageList: { fileId: string; filePath: string }[]
      expressScore: number | string
      serverScore: number | string
      describeScore: number | string
      userId: number
      content: string
    }
    interface CollectionItem {
      favoriteId: number
      linePrice: string
      pid: number
      logoPath: string
      productId: number
      productImage: string
      productName: string
      productPrice: string
      productSales: number
      shopSupplierId: number
      type: number
      userId: number
    }

    interface CollectionData {
      lastPage: number
      pageIndex: number
      pageSize: number
      total: number
      records: CollectionItem[] | SupplierItem[]
    }
    interface AddressItem {
      addressId: number
      name: string
      phone: string
      region: Region
      detail: string
    }
    interface UserAddressData {
      defaultId: number
      list: AddressItem[]
    }

    interface WxLoginInfo {
      loginUserVo: {
        mobile: string
        userId: number
      }
      token: string
    }
    interface Agent {
      avatarUrl: string | unknown
      firstNum: number
      freezeMoney: string
      gradeId: number
      mobile: string
      grade: string
      isSign: number
      signUrlQrCode: string
      signUrl: string
      signUrlExpireTime: string
      money: string
      nickName: string | unknown
      realName: string
      refereeId: number
      refereeNickname: string
      secondNum: number
      thirdNum: number
      totalMoney: string
      userId: number
      nextGradeAgentMoney: string
      nextGradeAgentStore: number
    }
    interface InfoWords {
      index: {
        applyNow: string
        cash: string
        freezeMoney: string
        money: string
        notAgent: string
        referee: string
        title: string
        totalMoney: string
      }
      order: {
        all: string
        settled: string
        title: string
        unsettled: string
      }
      cashApply: {
        capital: string
        minMoney: string
        money: string
        moneyPlaceholder: string
        submit: string
        title: string
      }
      cashList: {
        all: string
        apply10: string
        apply20: string
        apply30: string
        apply40: string
        title: string
      }
      team: {
        first: string
        second: string
        third: string
        title: string
        totalTeam: string
      }
      qrcode: {
        title: string
      }
    }

    interface CashListItem {
      applyStatus: number
      applyStatusText: string
      createTime: string
      id: number
      mchId: unknown
      money: string
      outBillNo: string
      packageInfo: unknown
      wxAppId: unknown
    }

    interface CashListData {
      list: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: CashListItem[]
        total: number
      }
      words: InfoWords
    }

    interface OrderListItem {
      orderMaster: {
        orderNo: string
      }
      isSettled: number
      avatarUrl: string
      nickName: string
      firstUserId: number
      firstMoney: string
      secondUserId: number
      secondMoney: string
      thirdUserId: number
      thirdMoney: string
      orderPrice: string
      revenueSource: string
      createTime: string
      id: number
    }

    interface CashOrderData {
      list: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: OrderListItem[]
        total: number
      }
      words: InfoWords
    }
    interface TeamListItem {
      agent: {
        avatarUrl: string | null
        firstNum: number
        freezeMoney: string
        gradeId: number
        mobile: string
        money: string
        nickName: string | null
        realName: string
        refereeId: number
        refereeNickname: string | null
        secondNum: number
        thirdNum: number
        totalMoney: string
        userId: number
      }
      agentId: number
      createTime: string | null
      id: number
      level: number
      user: {
        avatarUrl: string
        expendMoney: string
        nickName: string
        realName: string
        userId: number
      }
      userId: number
      payMoney: string
      position: string
      payPercent: string
    }

    interface UserAgentInfo {
      appId: number
      bankCard: string | null
      bankName: string | null
      createTime: string
      firstNum: number
      freezeMoney: string
      gradeId: number
      identity: string | null
      isDelete: number
      mobile: string
      money: string
      realName: string
      refereeId: number
      secondNum: number
      thirdNum: number
      totalMoney: string
      updateTime: string | null
      userId: number
    }

    interface UserInviteShopItem {
      createTime: string
      logo: string
      name: string
      shopSupplierId: number
    }

    interface CashSupplierData {
      list: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: UserInviteShopItem[]
        total: number
      }
      totalSaleMoney: string
    }
    interface CashData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: TeamListItem[]
      total: number
    }
    interface CashTeamData {
      list: CashData
      words: InfoWords
      totalPayMoney: string
      agent: UserAgentInfo
      setting: {
        isOpen: number
        level: number
        selfBuy: number
      }
    }

    interface ApplyData {
      agentUser: UserAgentInfo
      background: {
        apply: string
        cashApply: string
        index: string
      }
      settlement: {
        minMoney: string
        payType: number[]
        settleDays: number
        wechatPayAuto: number
      }
      templateArr: string[]
      words: InfoWords
    }

    interface UserAgent {
      agent: Agent
      refereeUserNickName: string
      performance: {
        countMoney: string
        countSupplier: number
      }
      user: {
        avatarUrl: string
        expendMoney: string
        nickName: string
        realName: string
        userId: number
      }
      words: InfoWords
      apply: stringstate.dataType
      background: string
      isAgent: boolean
      isApplying: boolean
    }
    interface BalanceLogEntry {
      logId: number
      userId: number
      scene: number
      sceneText: string
      money: string // 或 number，根据需求调整
      description: string
      remark: string | null
      createTime: string // 或 Date，根据需求调整
    }
    interface UserBalanceLogIndex {
      cashOpen: boolean
      balance: string
      balanceOpen: boolean
      list: BalanceLogEntry[]
    }
    interface UserBalanceLogList {
      total: number
      lastPage: number
      pageIndex: number
      pageSize: number
      records: BalanceLogEntry[]
    }
    interface UserBalancePlanSettings {
      isOpen: boolean
      isPlan: number
      minMoney: string
      describe: string
    }
    interface UserBalancePlanLists {
      planId: number
      money: number
      planName: string
      giveMoney: number
      giveShanBean: string | null
      realMoney: number
      sort: number
      isDelete: number
    }
    interface UserBalancePlanIndex {
      settings: UserBalancePlanSettings
      list: UserBalancePlanLists[]
    }
    interface SubmitUserBalancePlan {
      orderId: number
    }

    interface UserOrderProducts {
      orderProductId: number
      productId: number
      productName: string
      imageId: number
      isAgent: number
      deductStockType: number
      specType: number
      specSkuId: string
      productSkuId: number
      productAttr: string
      productNo: string
      productPrice: string
      linePrice: string
      productWeight: number
      isUserGrade: number
      gradeRatio: string
      gradeProductPrice: string
      gradeTotalMoney: string
      couponMoney: string
      pointsMoney: string
      pointsNum: number
      pointsBonus: number
      totalNum: number
      totalPrice: string
      totalPayPrice: string
      fullreduceMoney: string
      productReduceMoney: string
      isComment: number
      orderId: number
      userId: number
      productSourceId: number
      skuSourceId: number
      billSourceId: number
      virtualContent: string
      tableId: number
      tableRecordId: number
      productImage: string
      refund: boolean
      refundStatus: Record<string, unknown>[]
      refundState: Record<string, unknown>[]
      tableData: Record<string, unknown>[]
      firstMoney: string
      secondMoney: string
      thirdMoney: string
    }
    interface UserOrderRecords {
      createTime: string
      stateText: string
      orderId: number
      orderNo: string
      tradeNo: string
      totalPrice: string
      orderPrice: string
      couponId: number
      couponMoney: string
      pointsMoney: string
      pointsNum: number
      payPrice: string
      updatePrice: string
      updatePriceSymbol: string
      fullreduceMoney: string | null
      fullreduceRemark: string | null
      productReduceMoney: string
      buyerRemark: string
      payType: number
      payTypeText: string
      paySource: string
      payStatus: number
      payStatusText: string
      payTime: string
      payEndTime: string
      deliveryType: number
      deliveryTypeText: string
      expressPrice: string
      expressId: number
      expressCompany: string
      expressNo: string
      deliveryStatus: number
      deliveryStatusText: string
      deliveryTime: string | null
      receiptStatus: number
      receiptStatusText: string
      receiptTime: string | null
      orderStatus: number
      orderStatusText: string
      pointsBonus: number
      isSettled: number
      transactionId: string
      isComment: number
      orderSource: number
      orderSourceText: string
      userId: number
      isRefund: number
      assembleStatus: number
      activityId: number
      isAgent: number
      cancelRemark: string
      virtualAuto: number
      virtualContent: string
      balance: string
      onlineMoney: string
      refundMoney: string
      isDelete: number
      totalNum: number
      supplierName: string
      shopSupplierId: number
      mchId: string
      advance: unknown
      isMore: number
      product: UserOrderProducts[]
    }
    interface UserOrderArray {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: UserOrderRecords[]
      total: number
    }
    interface UserOrderLists {
      isSendWx: string
      list: UserOrderArray
    }
    interface UserOrderDetailExpressList {
      expressId: number
      expressName: string
    }
    interface UserOrderDetailExtractStore {
      storeName: string
      phone: string
      address: string
      province: string
      city: string
      region: string
    }
    interface UserOrderDetailDetailListProducts {
      orderProductId: number
      productId: number
      productName: string
      imageId: number
      isAgent: number
      deductStockType: number
      specType: number
      specSkuId: string
      productSkuId: number
      productAttr: string
      productNo: string
      productPrice: string
      linePrice: string
      productWeight: number
      isUserGrade: number
      gradeRatio: number
      gradeProductPrice: string
      gradeTotalMoney: string
      couponMoney: string
      pointsMoney: string
      pointsNum: number
      pointsNum: number
      pointsBonus: number
      totalNum: number
      totalPrice: string
      totalPayPrice: string
      fullreduceMoney: string
      productReduceMoney: string
      isComment: number
      orderId: number
      userId: number
      productSourceId: number
      skuSourceId: number
      billSourceId: number
      virtualContent: string
      tableId: number
      tableRecordId: number
      productImage: string
      refund: boolean
      refundStatus: any
      refundState: any
      tableData: any
      firstMoney: string
      secondMoney: string
      thirdMoney: string
    }
    interface UserOrderDetailAddress {
      orderAddressId: number
      name: string
      phone: string
      provinceId: number
      cityId: number
      regionId: number
      detail: string
      orderId: number
      userId: number
      region: {
        province: string
        city: string
        region: string
      }
    }
    interface UserOrderDetailDetailList {
      storeType?: number
      storeLongitude?: string
      storeLatitude?: string
      storeName?: string
      storeArea?: string
      storeAddress?: string
      cancelReason?: string
      storeId?: any
      payShanBean: string
      payBalance: string
      payBank: string
      payRedPocket: string
      payPrice: string
      paySource: any
      orderId: any
      orderNo: string
      totalPrice: string
      orderPrice: string
      couponId: number
      extractStoreId: number
      couponMoney: any
      couponIdSys: number
      couponMoneySys: any
      pointsMoney: number
      pointsNum: number
      payStatus: number
      payPrice: string
      updatePrice: string
      updatePriceSymbol: string
      fullreduceMoney: any
      fullreduceRemark: string
      productReduceMoney: any
      buyerRemark: string
      payType: number
      payTypeText: string
      payTime: string | null
      payEndTime: string | null
      deliveryType: number
      deliveryTypeText: string
      deliveryStatus: number
      deliveryStatusText: string
      deliveryTime: string | null
      receiptStatus: number
      receiptStatusText: string
      receiptTime: string | null
      orderStatus: number
      orderStatusText: string
      pointsBonus: number
      isSettled: number
      transactionId: string
      tradeNo: string
      userId: number
      isRefund: number
      assembleStatus: number
      activityId: number
      isAgent: number
      cancelRemark: string
      virtualAuto: number
      virtualContent: string
      balance: string
      onlineMoney: string
      refundMoney: string
      isDelete: number
      address: UserOrderDetailAddress
      stateText: string
      payEndTimeText: string
      expressPrice: string
      createTime: string
      isAllowRefund: boolean
      express: express | null
      expressNo: string
      extractStore: UserOrderDetailExtractStore | null
      showTable: boolean
      supplierName: string
      shopSupplierId: number
      supplierUserId: number
      orderSource: number
      mchId: string
      serviceUserId: number
      nickName: string | null
      advance: any
      isMore: number
      orderDeliverList: any
      customForm: any
      product: UserOrderDetailDetailListProducts[]
    }
    interface express {
      expressId: any
      expressName: string
    }
    interface UserOrderDetail {
      pointsName: string
      isSendWx: boolean
      serviceType: number
      serviceOpen: boolean
      expressList: UserOrderDetailExpressList[]
      detail: UserOrderDetailDetailList
    }
    interface UserPointsCredits {
      lastPage: number
      pageIndex: number
      pageSize: number
      total: number
      records: UserPointsRecords[]
    }
    interface UserPointsRecords {
      appId: number
      createTime: string
      flowType: number
      logId: number
      orderDescription: string
      orderId: number
      remake: string | null
      userId: number
      value: string
    }
    interface UserPointsList {
      totalCredits: string
      credits: UserPointsCredits
    }
    interface UserGoodBean {
      remark: string
      logId: number
      userId: number
      value: number
      description: string
      createTime: string
    }
    interface UserGoodBeanCredits {
      lastPage: number
      pageIndex: number
      pageSize: number
      total: number
      records: UserGoodBean[]
    }
    interface UserGoodBeanIndex {
      isOpen: boolean
      isTransBalance: boolean
      discountRatio: number
      points: number
      list: UserGoodBeanCredits
    }

    interface UserRedPacketRecords {
      redPacketId: number
      userId: number
      orderId: number
      orderTitle: string
      appId: number
      amount: string
      remainingAmount: string
      isFinished: string
      source: string | null
      createTime: string
      updateTime: string
      validityPeriod: string
    }
    interface UserRedPacket {
      lastPage: number
      pageIndex: number
      pageSize: number
      total: number
      records: UserRedPacketRecords[]
    }
    interface UserRedPacketList {
      totalRemainingAmount: string
      totalAmount: string
      redPacket: UserRedPacket
    }
    interface UserRedPacket {
      lastPage: any
      pageIndex: any
      pageSize: any
      records: UserRedPacketRecords[]
      total: any
    }
    interface UserRedPacketRecords {
      amount: string
      appId: number
      createTime: string
      installment: number
      isFinished: any
      isSupplier: string
      orderId: number
      orderNo: string
      orderTitle: string
      planId: number
      redPacketId: string
      remainingAmount: string
      source: string
      updateTime: string
      userId: number
      validityPeriod: string
    }
    interface UserRedPacketLogRecords {
      statementId: number
      orderId: number
      orderTitle: string
      userId: number
      redPacketId: number
      amount: string
      amountUsed: string
      remainingAmount: string
      remarks: string | null
      appId: number
      createTime: string
    }
    interface UserRedPacketLogList {
      lastPage: number
      pageIndex: number
      pageSize: number
      total: number
      records: UserRedPacketLogRecords[]
    }

    interface FormDataItem {
      orderProductId: number | string
      productId: number | string
      score: number | string
      imageList: { filePath: string }[]
      expressScore: number | string
      serverScore: number | string
      describeScore: number | string
      orderId: number | string
      userId: number
      content: string | null
    }

    interface UserCommentToOrder {
      score: number | null
      content: string | null
      orderId: number
      productId: number
      orderProductId: number
      productName: string
      productPrice: number
      nickName: string
      productImage: string
      userId: number
      imageList: { filePath: string }[]
    }
    interface UserInfo {
      addressId: number
      appId: number
      appUser: string
      appopenId: string
      avatarUrl: string
      balance: string
      cashMoney: string
      city: string
      country: string
      createTime: string
      expendMoney: string
      freezeMoney: string
      gender: number
      giftMoney: number
      gradeId: number
      isDelete: number
      mobile: string
      mpopenId: string
      nickName: string
      openId: string
      password: string
      paymentPassword: string
      payMoney: string
      points: number
      province: string
      realName: string
      refereeId: number
      regSource: string
      salt: string
      totalInvite: number
      totalPoints: number
      unionId: string
      updateTime: unknown
      userId: number
      userType: number
      redPacket?: string
      credits?: string
      withdrawableEarnings: string
      zfbUserId: unknown
    }

    interface UserCenterConfig {
      coupon: number
      getPhone: boolean
      invitation: {
        image: string
        invitationId: boolean
        isOpen: boolean
      }
      isWx: boolean
      menus: {
        appId: number
        createTime: string
        icon: string
        isShow: number
        menuId: number
        name: string
        path: string
        pathName: string
        sort: number
        sysTag: string
        updateTime: unknown
      }[]
      msgCount: number
      orderCount: {
        comment: number
        delivery: number
        payment: number
        received: number
      }
      page: {
        items: {
          group: string
          icon: string
          name: string
          style: {
            background: string
            bgcolor: string
            padding: number
            paddingBottom: number
            paddingLeft: number
            paddingTop: number
            type: number
          }
          type: string
        }[]
        page: {
          category: {
            color: string
            open: number
          }
          name: string
          params: {
            icon: 'icon-biaoti'
            name: string
            shareImg: string
            shareTitle: string
            title: string
            titleType: string
            topLogo: string
            toplogo: string
          }
          style: {
            titleBackgroundColor: string
            titleTextColor: string
          }
          type: string
        }
      }
      service: {
        appId: unknown
        createTime: unknown
        headPortrait: unknown
        isDelete: unknown
        nickName: unknown
        password: unknown
        salt: unknown
        serviceUserId: number
        shopSupplierId: unknown
        sort: unknown
        statu: unknown
        type: unknown
        updateTime: unknown
        userId: unknown
        userName: unknown
      }
      serviceUserId: number
      setting: {
        agentOpen: number
        balanceOpen: number
        pointsName: string
        supplierImage: string
      }
      sign: {
        content: string
        coupon: unknown[]
        everSign: number
        increaseReward: number
        isCoupon: boolean
        isIncrease: boolean
        isOpen: boolean
        noIncrease: number
        rewardData: unknown[]
        signType: number
      }
      supplierStatus: number
      supplierUser: {
        shopSupplierId: unknown
        supplierUserId: unknown
        userId: unknown
        userName: unknown
      }
      user: UserInfo
      userGrade: string
    }

    interface SignData {
      arr: unknown[]
      continuousDays: number
      couponNum: string
      days: number
      indexToday: number
      isSign: number
      list: number[]
      maxDays: number
      period: string
      point: Record<string, any>
      points: string
      signList: {
        appId: number
        coupon: unknown[]
        couponNum: number
        createTime: string
        days: number
        points: number
        prize: string
        signDate: string
        signDay: number
        updateTime: unknown
        userId: number
        userSignId: number
      }[]
      today: string
      userPoint: number
      week: {
        coupon: unknown[]
        date: string
        isSign: number
        nextMont: number
        rank: number
      }[]
    }

    interface LoginSettingData {
      addAudit: number
      avatarUrl: string
      commissionRate: string
      deliveryType: number[]
      editAudit: number
      enableMpOauth: boolean
      h5SmsOpen: boolean
      isGetLog: boolean
      isSendWx: boolean
      kuaiDi100: unknown
      loadImage: string
      loginDesc: string
      loginLogo: string
      logoUrl: string
      mpBinding: boolean
      mpOauthUrl: string
      mpState: number
      name: string
      operateType: number
      policy: { service: string; privacy: string }
      smsOpen: number
      storeOpen: number
      supplierCash: number
      supplierImage: string
      txKey: string
      userName: string
      wxBinding: boolean
      wxOpen: boolean
      wxPhone: boolean
      isAudit: boolean
    }

    interface UserAddressData {
      detail: {
        addressId: number
        cityId: number
        detail: string
        district: string
        name: string
        phone: string
        provinceId: number
        region: { province: string; city: string; region: string }
        regionId: number
        userId: number
      }
      regionData: { label: string; value: number }[][]
    }

    interface IndexData {
      coupon: number
      getPhone: boolean
      invitation: { isOpen: number; image: string; invitationId: number }
      isWx: boolean
      menus: {
        appId: number
        createTime: string
        icon: string
        isShow: number
        menuId: number
        name: string
        path: string
        pathName: string
        sort: number
        sysTag: string
        updateTime: null | string
      }[]
      msgCount: number
      orderCount: {
        comment: number
        delivery: number
        payment: number
        received: number
      }
      service: {
        appId: null | number
        createTime: null | string
        headPortrait: unknown
        isDelete: unknown
        nickName: string
        password: unknown
        salt: unknown
        serviceUserId: 2
        shopSupplierId: unknown
        sort: unknown
        statu: unknown
        type: unknown
        updateTime: unknown
        userId: unknown
        userName: unknown
      }
      serviceUserId: number
      setting: {
        agentOpen: number
        balanceOpen: number
        pointsName: string
        supplierImage: string
      }
      sign: {
        content: string
        coupon: string[]
        everSign: number
        increaseReward: number
        isCoupon: boolean
        isIncrease: boolean
        isOpen: boolean
        noIncrease: number
        rewardData: {
          coupon: string[]
          day: number
          isCoupon: boolean
          isPoint: boolean
          point: number
        }[]
        signType: number
      }
      supplierStatus: number
      supplierUser: {
        shopSupplierId: unknown
        supplierUserId: unknown
        userId: unknown
        userName: unknown
      }
      user: UserInfo
      userGrade: string
    }

    interface ShopCategoryItem {
      appId: number
      categoryId: number
      children: ShopCategoryItem[]
      createTime: string
      depositMoney: string
      name: string
      parent: unknown
      parentId: number
      picPath: string
      updateTime: unknown
    }

    interface UserTradeData {
      isOpen: number
      todayCount: number
      totalCount: number
      supplier: {
        address: string
        appId: number
        backgroundPath: string
        businessFilePath: string
        businessId: number
        cashMoney: string
        categoryId: number
        categoryName: string
        categoryPid: unknown
        cityId: unknown
        createTime: string
        depositMoney: string
        describeScore: string
        description: unknown
        expressScore: string
        favCount: number
        freezeMoney: string
        giftMoney: number
        isDelete: number
        isFull: number
        isOpenErp: boolean
        isRecycle: number
        isfollow: number
        jjyAppkey: string
        jjyPrivatekey: unknown
        jjyPublickey: unknown
        jjySecret: string
        jjyUrl: string
        linkName: string
        linkPhone: string
        logoFilePath: string
        logoId: number
        money: string
        name: string
        nickName: unknown
        openService: number
        productList: unknown
        productSales: number
        provinceId: unknown
        realName: string
        regionId: unknown
        score: string
        serverScore: string
        serviceUserId: number
        shopSupplierId: number
        status: number
        storeType: number
        supplierCommissionRate: string
        supplierLakalaAppTerminalNum: unknown
        supplierLakalaOfflineTerminalNum: unknown
        supplierProductIsCheck: string
        supplierShanBeanRate: string
        supplierUserId: number
        totalGift: number
        totalMoney: string
        updateTime: unknown
        userId: number
      }
    }

    interface UserStoreDataRecordsItem {
      orderNo: string
      payMoney: string
      supplierMoney: string
      settledId: number
    }
    interface UserStoreData {
      order: {
        orderPerPriceT: string
        orderPerPriceY: string
        orderRefundMoneyT: string
        orderRefundMoneyY: string
        orderRefundTotalT: string
        orderRefundTotalY: string
        orderTotalPriceT: string
        orderTotalPriceY: string
        orderTotalT: number
        orderTotalY: number
        orderUserTotalT: number
        orderUserTotalY: number
      }
      ordersettle: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: UserStoreDataRecordsItem[]
        total: number
      }
      visit: {
        favProductT: number
        favProductY: number
        favStoreT: number
        favStoreY: number
        visitTotalT: number
        visitTotalY: number
        visitUserT: number
        visitUserY: number
      }
    }

    interface UserStoreProductDataItem {
      productId: number
      productName: string
      productPrice: string
      linePrice: string
      productNo: string
      productStock: number
      sellingPoint: string
      categoryId: number
      specType: number
      productSales: number
      isPointsGift: number
      isPointsDiscount: number
      maxPointsDiscount: number
      isEnableGrade: number
      isAloneGrade: number
      isVirtual: number
      aloneGradeEquity: string
      aloneGradeType: number
      viewTimes: number
      limitNum: number
      gradeIds: string
      isUserGrade: boolean
      addSource: number
      productImage: string
      shopSupplierId: number
      supplierPrice: string
      supplier: {
        address: string
        name: string
        shopSupplierId: number
        logoId: number
      }
      seckill: unknow
      isPreview: number
      previewTime: unknow
      previewTimeStamp: number
      isActivity: boolean
    }
    interface UserStoreProductData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: UserStoreProductDataItem[]
      total: number
    }

    interface AddProductSetting {
      agentSetting: {
        firstMoney: number
        secondMoney: number
        thirdMoney: number
      }
      basicSetting: {
        isOpen: number
        level: number
        selfBuy: number
      }
      category: unknown[]
      delivery: {
        appId: number
        createTime: string
        deliveryId: number
        method: number
        name: string
        shopSupplierId: number
        sort: number
        updateTime: null | string
      }[]
      gradeList: {
        appId: number
        createTime: string
        equity: number
        givePoints: number
        gradeId: number
        isDefault: number
        isDelete: number
        name: string
        openInvite: number
        openMoney: number
        openPoints: number
        remark: string
        updateTime: null | string
        upgradeInvite: number
        upgradeMoney: number
        upgradePoints: number
        weight: number
      }[]
      supplier: Api.Supplier.SupplierDataDetail
      isSpecLocked: boolean
      specData: unknown
      tableList: unknown[]
    }

    interface SpecItem {
      itemId: number
      specValue: string
    }

    interface SpecAttr {
      groupId: number
      groupName: string
      specItems: SpecItem[]
      tempValue: string
    }

    interface SpecRow {
      itemId: number
      specValue: string
    }

    interface SpecSku {
      productSkuId: number
      specSkuId: string
      rows: SpecRow[]
      productNo: string
      productPrice: string
      linePrice: string
      stockNum: string
      productWeight: string
    }
    interface AddProductSettingBody {
      productName: string
      image: (Record<string, string> | string)[]
      videoId: number
      posterId: number
      categoryPid: string
      categoryId?: string
      productStatus: 10 | 20
      sellingPoint: string
      isVirtual: 0 | 1
      deliveryId: number | string
      isDeliveryFree: 1 | 0
      isPicture: 1 | 0
      productSort: number
      limitNum: number
      virtualAuto: 1 | 0
      virtualContent: string
      deductStockType: 10 | 20
      specType: 10 | 20
      sku: {
        productNo: string
        productPrice: string
        linePrice: string
        stockNum: string
        productWeight: string
      }
      specMany: {
        specAttr: SpecAttr[]
        specList: SpecSku[]
      }
      contentImage: (Record<string, string> | string)[]
      isPointsGift: 0 | 1
      isPointsDiscount: 0 | 1
      isAgent: 0 | 1
      maxPointsDiscount: string
    }

    interface InviteItem {
      userId: number
      nickName: string
      mobile: string
      realName: string
      avatarUrl: string
      inviteCount: number
    }
  }
  namespace aggent {
    interface ApplyBody {
      name: string
      mobile?: string
      forShowidentityImag1: any[]
      identityFrontImage: number
      forShowidentityImag2: any[]
      identityBackImage: number
      idValidity: number
      idCardStartDate: string
      forShowIdCardStDt: number
      idCardExpiredDate: string
      forShowIdCardExpDt: number
      forShowbankCardImageId1: any[]
      bankCardImage: number
      name: string
      identity: string
      bankCard: string
      forShowBankInfo: string[]
      settleProvinceCode?: string
      settleProvinceName?: string
      settleCityCode?: string
      settleCityName?: string
      openningBankCode?: string
      bankName: string
      clearingBankCode?: string
    }
    interface ApplyWords {
      wordTitle: string
      license: string
      submit: string
      gotoMall: string
      waitAudit: string
      title: string
    }

    interface QrcodeWords {
      title: string
    }

    interface IndexWords {
      money: string
      freezeMoney: string
      totalMoney: string
      notAgent: string
      applyNow: string
      referee: string
      title: string
      cash: string
    }

    interface CashListWords {
      all: string
      apply40: string
      apply30: string
      apply20: string
      apply10: string
      title: string
    }

    interface TeamWords {
      third: string
      totalTeam: string
      title: string
      first: string
      second: string
    }

    interface CashApplyWords {
      capital: string
      money: string
      submit: string
      moneyPlaceholder: string
      minMoney: string
      title: string
    }

    interface OrderWords {
      all: string
      settled: string
      unsettled: string
      title: string
    }

    interface Words {
      apply: ApplyWords
      qrcode: QrcodeWords
      index: IndexWords
      cashList: CashListWords
      team: TeamWords
      cashApply: CashApplyWords
      order: OrderWords
    }

    interface User {
      userId: number
      nickName: unknown | string
      avatarUrl: unknown | string
      realName: string
      mobile: string
      money: string
      freezeMoney: string
      totalMoney: string
      refereeId: number
      firstNum: number
      secondNum: number
      thirdNum: number
      gradeId: number
      refereeNickname: unknown | string
    }

    interface AggentApplyStatus {
      license: License
      isApplying: boolean
      isAgent: boolean
      apply: string
      background: string
      words: Words
      refereeName: string
      user: User
      templateArr: string[] // 若实际数据有具体结构，可替换为具体类型
      // name: string
      // mobile: string
      // identity:string
      // bankCard:string
    }
  }
  namespace refund {
    interface applyRefundFormData {
      type: any
      orderProductId: any
      images: any[]
      content: string
    }
    interface RefundExpress {
      expressId: number
      expressName: string
    }
    interface RefundorderProduct {
      orderProductId: number
      productId: number
      productName: string
      imageId: number
      isAgent: number
      deductStockType: number
      specType: number
      specSkuId: string
      productSkuId: number
      productAttr: string
      productNo: string
      productPrice: string
      linePrice: string
      productWeight: number
      isUserGrade: number
      gradeRatio: number
      gradeProductPrice: string
      gradeTotalMoney: string
      couponMoney: string
      pointsMoney: string
      pointsNum: number
      pointsBonus: number
      totalNum: number
      totalPrice: string
      totalPayPrice: string
      fullreduceMoney: string
      productReduceMoney: string
      isComment: number
      orderId: number
      userId: number
      productSourceId: number
      skuSourceId: number
      billSourceId: number
      virtualContent: string
      tableId: number
      tableRecordId: number
      productImage: string
      refund: any
      refundStatus: any
      refundState: any
      tableData: any
      firstMoney: string
      secondMoney: string
      thirdMoney: string
      supplierCommissionRate: any
    }
    interface RefundImages {
      fileId: number
      filePath: string
    }
    interface RerefundDetail {
      orderRefundId: number
      orderId: number
      orderProductId: number
      userId: number
      type: number
      applyDesc: string
      isAgree: number
      refuseDesc: string
      refundMoney: string
      isUserSend: number
      sendTime: any
      expressId: string
      expressNo: string
      isReceipt: number
      status: number
      deliverTime: any
      sendExpressId: string
      sendExpressNo: string
      isPlateSend: number
      orderProduct: RefundorderProduct
      plateDesc: string
      createTime: string
      stateText: string
      productImage: any
      images: RefundImages[]
      orderM: {
        orderId: number
        orderNo: string
        totalPrice: string
        orderPrice: string
        couponId: number
        extractStoreId: number
        couponMoney: string
        couponIdSys: number
        couponMoneySys: string
        pointsMoney: string
        pointsNum: number
        payPrice: string
        updatePrice: string
        updatePriceSymbol: string
        fullreduceMoney: string
        fullreduceRemark: string
        productReduceMoney: string
        buyerRemark: string
        payType: number
        payTypeText: string
        paySource: string
        payStatus: number
        payStatusText: string
        payTime: string
        payEndTime: string
        deliveryType: number
        deliveryTypeText: string
        deliveryStatus: number
        deliveryStatusText: string
        deliveryTime: string
        receiptStatus: number
        receiptStatusText: string
        receiptTime: string
        orderStatus: number
        orderStatusText: string
        pointsBonus: number
        isSettled: number
        transactionId: string
        tradeNo: string
        userId: number
        isRefund: number
        assembleStatus: number
        activityId: number
        isAgent: number
        cancelRemark: string
        virtualAuto: number
        virtualContent: string
        balance: string
        onlineMoney: string
        refundMoney: string
        isDelete: number
        product: any
        address: any
        stateText: number
        payEndTimeText: any
        expressPrice: string
        createTime: string
        isAllowRefund: true
        express: any
        expressNo: string
        extractStore: {
          storeName: string
          phone: string
          address: string
          province: string
          city: string
          region: string
        }
        storeId?: number
        showTable: any
        supplierName: string
        shopSupplierId: number
        supplierUserId: number
        orderSource: number
        mchId: any
        serviceUserId: any
        nickName: any
        advance: any
        isMore: 0
        orderDeliverList: any
        customForm: any
      }
      address: any
      expressList: RerefundExpress[]
      typeText: string
      expressName: any
      sendExpressName: any
    }
  }

  namespace Search {
    interface HotSearchItem {
      appId: number
      createTime: string
      hotLabelName: string
      hotLabelSort: number
      hotLabelType: string
      hotProductLabelId: number
      isDelete: number
      updateTime: null | string
    }
    interface LinkSearchItem {
      labelId: number
      labelType: string
      labelName: string
      labelSort: number
      isDelete: number
      appId: number
      createTime: null | string
      updateTime: null | string
    }
  }

  namespace ShopApply {
    interface KvInfo {
      data: {
        address: string
        birthDate: string
        ethnicity: string
        idNumber: string
        name: string
        sex: string
        issueAuthority: string
        validPeriod: string
        RegistrationDate: string
        businessAddress: string
        businessScope: string
        companyForm: string
        companyName: string
        companyType: string
        creditCode: string
        issueDate: string
        legalPerson: string
        registeredCapital: string
        title: string
        validFromDate: string
        validToDate: string
        bankName: string
        cardNumber: string
        cardType: string
      }
      kvCount: number
      kvDetails: {
        address: {
          keyConfidence: number
          keyName: 'address'
          value: string
          valueAngle: number
          valueConfidence: number
        }
        birthDate: {
          keyConfidence: number
          keyName: 'birthDate'
          value: string
          valueAngle: number
          valueConfidence: number
        }
        ethnicity: {
          keyConfidence: number
          keyName: 'ethnicity'
          value: string
          valueAngle: number
          valueConfidence: number
        }
        idNumber: {
          keyConfidence: number
          keyName: 'idNumber'
          value: string
          valueAngle: number
          valueConfidence: number
        }
        name: {
          keyConfidence: number
          keyName: 'name'
          value: string
          valueAngle: number
          valueConfidence: number
        }
        sex: {
          keyConfidence: number
          keyName: 'sex'
          value: string
          valueAngle: number
          valueConfidence: number
        }
      }
    }
    interface PicRec {
      height: number
      subImageCount: number
      subImages: {
        qualityInfo: { isCopy: boolean }
        subImageId: number
        kvInfo: KvInfo
        angle: number
        type: string
      }[]
      width: number
    }
    interface AccountForm {
      mobile: string
      userName: string
      password: string
    }

    interface PersonalForm {
      identityImag1: number
      forShowidentityImag1: any[]
      identityImag2: number
      forShowidentityImag2: any[]
      larName: string
      larIdCard: string
      idValidity: 0 | 1
      larIdCardStDt: string
      forShowlarIdcardStDt: number
      larIdCardExpDt: string
      forShowlarIdcardExpDt: number
      merContactMobile: string
      merContactEmail: string
    }

    interface MerchantForm {
      businessId: number
      forShowbusinessId: any[]
      merBlisName: string
      merBlis: string
      isPerpetual: 0 | 1
      merBlisStDt: string
      forShowMerBlisStDt: number
      merBlisExpDt: string
      forShowMerBlisExpDt: number
      supplierCommissionRate: string
      categoryId: string
      forShowcategoryId: number[]
      forShowBussinessInfo: string[]
      provinceCode: string
      cityCode: string
      countyCode: string
      merBusiContent: string
      storeName: string
    }
    interface ShopForm {
      storeName: string
      provinceId: number
      area: string
      forShowarea: number[]
      cityId: number
      regionId: number
      address: string
      shopHoursSTime: string
      shopHoursETime: string
      summary: string
      logoImageId: number
      forShowlogoImageId: any[]
      cashRegisterId: number
      forShowCashRegisterId: any[]
      interiorShotId: number
      forShowInteriorShotId: any[]
      latitude: string
      longitude: string
      showLocation: string
    }

    interface SettlementForm {
      acctIdType: 0 | 1
      acctTypeCode: 57 | 58
      bankCardImageId1: number
      forShowbankCardImageId1: any[]
      bankCardImageId2: number
      forShowBankCardImageId2: any[]
      acctIdCardImageId1: number
      forShowAcctIdcardImageId1: any[]
      acctIdCardImageId2: number
      forShowAcctIdcardImageId2: any[]
      bankAccountLicenseId: number
      forShowbankAccountLicenseId: any[]
      delegatedAuthorizationId: number
      forShowDelegatedAuthorizationId: any[]
      acctName: string
      acctNo: string
      acctIdCard: string
      merContactMobile: string
      otherImageIds: string
      forShowotherImageIds: any[]
      forShowBankInfo: string[]
      settleProvinceCode: string
      settleProvinceName: string
      settleCityCode: string
      settleCityName: string
      openningBankCode: string
      openningBankName: string
      clearingBankCode: string
      isLong: number
      accountIdDtStart: string
      forShowAccountIdDtStart: number
      accountIdDtEnd: string
      forShowAccountIdDtEnd: number
    }

    type FormMapType = [AccountForm, PersonalForm, MerchantForm, ShopForm, SettlementForm]

    interface ShopApplyDetail {
      accountIdDtEnd: string
      accountIdDtStart: string
      acctIdCard: string
      acctIdCardImageId1: number
      forShowAcctIdcardImageId1: { url: string }[]
      acctIdCardImageId2: number
      forShowAcctIdcardImageId2: { url: string }[]
      acctIdType: string
      acctName: string
      acctNo: string
      dataJsonObject: string
      acctTypeCode: string
      contractId: number
      merChantNo: number
      appId: number
      bankAccountLicenseId: number
      forShowbankAccountLicenseId: { url: string }[]
      bankCardImageId1: number
      forShowbankCardImageId1: { url: string }[]
      bankCardImageId2: number
      forShowBankCardImageId2: { url: string }[]
      businessId: number
      forShowbusinessId: { url: string }[]
      categoryId: number
      categoryPid: null | number
      cityCode: string
      clearingBankCode: string
      code: null | string
      countyCode: string
      delegatedAuthorizationId: number
      forShowDelegatedAuthorizationId: { url: string }[]
      ecNo: string
      idValidity: number
      identityImag1: number
      forShowidentityImag1: { url: string }[]
      identityImag2: number
      forShowidentityImag2: { url: string }[]
      isPerpetual: string
      content: string
      lakalaLedgerBind: {
        appId: number
        applyId: string
        auditStatus: number
        auditstatusText: string
        bindOrderNo: string
        createTime: string
        entrustFileId: unknown
        entrustFileName: string
        entrustFilePath: unknown
        id: number
        isDelete: number
        lakalaOrgCode: string
        merCupNo: string
        merInnerNo: unknown
        receiverNo: string
        remark: string
        updateTime: string
      }
      lakalaLedgerMer: {
        appId: number
        applyId: string
        auditStatus: number
        auditstatusText: string
        contactMobile: string
        createTime: string
        eleContractNo: unknown
        id: number
        isDelete: number
        lakalaFastTermNo: string
        lakalaOrgCode: string
        lakalaScanTermNo: string
        lakalaSupportRefund: string
        lakalaVposId: null | number
        ledgerOpen: number
        merCupNo: string
        merInnerNo: null | string
        merOrderNo: string
        remark: string
        sepFundSource: unknown
        shopSupplierId: number
        splitEntrustFileId: unknown
        splitEntrustFileName: unknown
        splitEntrustFilePath: unknown
        splitLowestRatio: unknown
        splitRange: unknown
        updateTime: string
      }
      larIdCard: string
      larIdCardExpDt: string
      larIdCardStDt: string
      larName: string
      merBlis: string
      merBlisExpDt: string
      merBlisName: string
      merBlisStDt: string
      merBusiContent: string
      merContactEmail: string
      merRegName: string
      mobile: string
      openningBankCode: string
      openningBankName: string
      otherImageIds: string
      forShowotherImageIds: { url: string }[]
      password: string
      provinceCode: string
      qrCode: string
      resultUrl: string
      settleCityCode: string
      settleCityName: string
      settleProvinceCode: string
      settleProvinceName: string
      status: number
      storeName: string
      forShowCashRegisterId: { url: string }[]
      forShowInteriorShotId: { url: string }[]
      forShowlogoImageId: { url: string }[]

      storeParam: {
        address: string
        announcerId: number
        appId: number
        area: string
        businessEndTime: null | string
        businessStartTime: null | string
        cashImageId: number
        cashRegisterId: null | number
        cityId: number
        coordinate: unknown
        createTime: string
        geohash: unknown
        interiorImageId: number
        interiorShotId: number
        isCheck: number
        isDelete: number
        latitude: string
        linkman: string
        logoImageId: number
        longitude: string
        phone: string
        provinceId: number
        regionId: number
        shopHours: string
        shopHoursETime: null | string
        shopHoursSTime: null | string
        shopSupplierId: number
        sort: number
        status: number
        storeId: number
        storeImageId: number
        storeName: string
        summary: string
        updateTime: string
      }
      supplierApplyId: number
      supplierCommissionRate: string
      supplierIsDelete: null
      userId: number
      userName: string
    }
  }

  namespace Message {
    interface ChatListItem {
      appId: number
      avatarUrl: null | string
      createTime: string
      newMessage: {
        appId: number
        chatId: number
        content: string
        createTime: string
        msgType: number
        sendType: number
        serviceUserId: number
        shopSupplierId: number
        status: number
        updateTime: null | string
        userId: number
      }
      nickName: '昵称'
      noReadCount: number
      relationId: number
      serviceLogo: string
      serviceUserId: number
      shopSupplierId: number
      supplier: {
        accountIdDtEnd: unknown
        accountIdDtStart: unknown
        acctIdCard: unknown
        acctIdCardImageId1: unknown
        acctIdCardImageId2: unknown
        acctIdType: unknown
        acctName: unknown
        acctNo: unknown
        acctTypeCode: unknown
        address: string
        appId: number
        area: string
        backgroundPath: string
        bankAccountLicenseId: unknown
        bankCardImageId1: unknown
        bankCardImageId2: unknown
        businessId: number
        cashMoney: string
        categoryId: number
        categoryPid: number
        cityCode: unknown
        cityId: number
        clearingBankCode: unknown
        contractId: unknown
        countyCode: unknown
        createTime: string
        depositMoney: string
        describeScore: string
        description: unknown
        expressScore: string
        favCount: number
        freezeMoney: string
        giftMoney: number
        idValidity: unknown
        identityImag1: unknown
        identityImag2: unknown
        isDelete: number
        isFull: number
        isOpenErp: boolean
        isPerpetual: unknown
        isRecycle: number
        jjyAppkey: string
        jjyPrivatekey: unknown
        jjyPublickey: unknown
        jjySecret: string
        jjyUrl: string
        larIdCard: unknown
        larIdCardExpDt: unknown
        larIdCardStDt: unknown
        larName: unknown
        linkName: string
        linkPhone: string
        logoId: number
        merBlis: unknown
        merBlisExpDt: unknown
        merBlisName: unknown
        merBlisStDt: unknown
        merBusiContent: unknown
        merChantNo: unknown
        merContactEmail: unknown
        merRegName: unknown
        money: string
        name: string
        openService: number
        openningBankCode: unknown
        openningBankName: unknown
        orderNo: unknown
        orgCode: unknown
        otherImageIds: unknown
        productSales: number
        provinceCode: unknown
        provinceId: number
        realName: string
        regionId: number
        score: string
        serverScore: string
        settleCityCode: unknown
        settleCityName: unknown
        settleProvinceCode: unknown
        settleProvinceName: unknown
        shopSupplierId: number
        status: number
        storeType: number
        supplierCommissionRate: string
        supplierLakalaAppTerminalNum: unknown
        supplierLakalaOfflineTerminalNum: unknown
        supplierProductIsCheck: string
        supplierShanBeanRate: string
        totalGift: number
        totalMoney: string
        updateTime: unknown
        userId: number
      }
      updateTime: string
      userId: number
    }
    interface ChatListData {
      list: ChatListItem[]
      url: string
    }

    interface ChatRecordItem {
      appId: number
      avatarUrl: null | string
      chatId: number
      content: string
      contentJson: {
        productImg: string
        productName: string
        productPrice: string
        orderNum: string
        orderPrice: string
        orderNo: string
        createTime: string
        orderId: string
      }
      createTime: string
      msgType: number
      nickName: string
      sendType: number | string
      serviceLogo: string
      serviceUserId: number
      shopSupplierId: number
      status: number
      updateTime: string
      userId: number
    }

    interface ChatRecordData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: ChatRecordItem[]
      total: number
    }
  }

  namespace Forum {
    interface MyArticleItem {
      appId: number
      avatarUrl: string
      collectCount: number
      commentsCount: number
      content: unknown
      createTime: string
      followee: number
      follower: number
      imageList: string
      isCollect: boolean
      isComments: string
      isDelete: number
      images: string[]
      isLike: boolean
      isTop: string
      isVisible: string
      likesCount: number
      mainCategoryId: number
      nickName: string
      postId: string
      subCategoryId: unknown
      title: string
      updateTime: string
      userId: number
    }
    interface MyArticleData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: MyArticleItem[]
      total: number
    }

    interface MyCommentItem {
      avatarUrl: string
      commentId: string
      content: string
      createTime: string
      followee: number
      follower: number
      nickName: string
      parentComment: string
      postId: string
      postTitle: unknown
    }
    interface MyCommentData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: MyCommentItem[]
      total: number
    }

    interface DiscourseDetail {
      appId: number
      avatarUrl: string
      collectCount: number
      commentsCount: number
      content: string
      createTime: string
      followee: number
      follower: number
      imageList: string
      images: string[]
      ip: null | string
      isCollect: boolean
      isComments: string
      isDelete: number
      isFollow: boolean
      isLike: boolean
      isTop: string
      isVisible: string
      likesCount: number
      mainCategoryId: number
      nickName: string
      postId: string
      subCategoryId: null | number
      title: string
      updateTime: string
      userId: number
    }

    interface ReplyItem {
      appId: number
      children: ReplyItem[]
      commentId: string
      content: string
      createTime: string
      isDelete: number
      nickName: string
      replyToNickName: string
      city: string
      isVisible: string
      avatarUrl: string
      parentCommentId: string
      postId: string
      userId: number
      isLike: boolean
      likeCount: number
    }
    interface ReplyData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: ReplyItem[]
      total: number
    }

    interface LikeAndCollectionItem {
      appId: number
      avatarUrl: string
      city: string
      collectCount: number
      commentsCount: number
      content: string
      createTime: string
      followee: number
      follower: number
      imageList: string
      images: string[]
      ip: string
      isCollect: boolean
      isComments: string
      isDelete: number
      isFollow: boolean
      isLike: boolean
      isTop: string
      isVisible: string
      likesCount: number
      mainCategoryId: number
      nickName: string
      postId: string
      subCategoryId: unknown
      title: string
      updateTime: string
      userId: number
    }
    interface LikeAndCollectionData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: LikeAndCollectionItem[]
      total: number
    }

    interface FootprintsData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: LikeAndCollectionItem[]
      total: number
    }
  }

  namespace AI {
    interface ChatListItem {
      role: string
      content: string
      audio_response: string
      aiChatId: string
    }

    interface ChatHistoryItem {
      aiChatContent: null
      aiChatContentJson: { audio_response: string; content: string; role: string }[]
      aiChatId: number
      aiChatQuestion: null
      aiChatQuestionJson: {
        audio_txt: string
        model: string
        session_id: string
      }[]
      appId: null
      createTime: string
      updateTime: string
      userId: number
    }
    interface ChatListData {
      lastPage: number
      pageIndex: number
      pageSize: number
      records: ChatHistoryItem[]
      total: number
    }
  }

  namespace Live {
    interface UserLiveListItem {
      appId: number
      coverImgId: number
      coverImgPath: string
      createTime: string
      id: number
      logoFilePath: string
      logoId: null | number
      remark: string
      shopSupplierId: number
      sphId: string
      sphName: string
      supplierName: string
      online?: number
      updateTime: null | string
      type: number
    }
    interface UserLiveListData {
      list: {
        lastPage: number
        pageIndex: number
        pageSize: number
        records: UserLiveListItem[]
        total: number
      }
    }
  }
}
