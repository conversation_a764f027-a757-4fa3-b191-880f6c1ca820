import { defineStore } from 'pinia'

const initInfo: Api.ShopApply.FormMapType = [
  //账号信息
  { mobile: '', userName: '', password: '' },
  //法人信息
  {
    identityImag1: 0,
    forShowidentityImag1: [],
    identityImag2: 0,
    forShowidentityImag2: [],
    larName: '',
    larIdCard: '',
    idValidity: 0,
    larIdCardStDt: '',
    forShowlarIdcardStDt: 0,
    forShowlarIdcardExpDt: 0,
    larIdCardExpDt: '',
    merContactMobile: '',
    merContactEmail: '',
  },
  //商户信息
  {
    businessId: 0,
    forShowbusinessId: [],
    merBlisName: '',
    merBlis: '',
    isPerpetual: 0,
    merBlisStDt: '',
    forShowMerBlisStDt: 0,
    merBlisExpDt: '',
    forShowMerBlisExpDt: 0,
    supplierCommissionRate: '',
    categoryId: '',
    forShowcategoryId: [],
    forShowBussinessInfo: [],
    provinceCode: '',
    cityCode: '',
    countyCode: '',
    storeName: '',
    merBusiContent: '',
  },
  //门店信息
  {
    storeName: '',
    provinceId: 0,
    cityId: 0,
    regionId: 0,
    forShowarea: [],
    area: '',
    address: '',
    shopHoursSTime: '',
    shopHoursETime: '',
    summary: '',
    logoImageId: 0,
    forShowlogoImageId: [],
    cashRegisterId: 0,
    forShowCashRegisterId: [],
    interiorShotId: 0,
    forShowInteriorShotId: [],
    latitude: '',
    longitude: '',
    showLocation: '请选择位置',
  },
  // 结算信息
  {
    acctIdType: 0,
    acctTypeCode: 58,
    bankCardImageId1: 0,
    forShowbankCardImageId1: [],
    bankCardImageId2: 0,
    forShowBankCardImageId2: [],
    acctIdCardImageId1: 0,
    forShowAcctIdcardImageId1: [],
    acctIdCardImageId2: 0,
    forShowAcctIdcardImageId2: [],
    bankAccountLicenseId: 0,
    forShowbankAccountLicenseId: [],
    delegatedAuthorizationId: 0,
    forShowDelegatedAuthorizationId: [],
    acctName: '',
    acctNo: '',
    forShowBankInfo: [],
    settleProvinceCode: '',
    settleProvinceName: '',
    settleCityCode: '',
    settleCityName: '',
    openningBankCode: '',
    openningBankName: '',
    clearingBankCode: '',
    acctIdCard: '',
    merContactMobile: '',
    otherImageIds: '',
    forShowotherImageIds: [],
    isLong: 0,
    accountIdDtStart: '',
    forShowAccountIdDtEnd: 0,
    accountIdDtEnd: '',
    forShowAccountIdDtStart: 0,
  },
]

export const useShopApplyStore = defineStore(
  'shop-apply',
  () => {
    const shopApplyInfo = ref<Api.ShopApply.FormMapType>(initInfo)

    // 账号信息
    const accountInfo = computed(() => shopApplyInfo.value[0])

    const setShopApplyAccountInfo = (info: Api.ShopApply.AccountForm) => {
      shopApplyInfo.value[0] = info
    }

    const clearShopApplyAccountInfo = () => {
      shopApplyInfo.value[0] = initInfo[0]
    }

    // 法人信息
    const personalInfo = computed(() => shopApplyInfo.value[1])

    const setShopApplyPersonalInfo = (info: Api.ShopApply.PersonalForm) => {
      shopApplyInfo.value[1] = info
    }

    const clearShopApplyPersonalInfo = () => {
      shopApplyInfo.value[1] = initInfo[1]
    }

    // 商户信息
    const merchantInfo = computed(() => shopApplyInfo.value[2])

    const setShopApplyMerchantInfo = (info: Api.ShopApply.MerchantForm) => {
      shopApplyInfo.value[2] = info
    }

    const clearShopApplyMerchantInfo = () => {
      shopApplyInfo.value[2] = initInfo[2]
    }

    // 门店信息
    const shopInfo = computed(() => shopApplyInfo.value[3])

    const setShopApplyShopInfo = (info: Api.ShopApply.ShopForm) => {
      shopApplyInfo.value[3] = info
    }

    const clearShopApplyShopInfo = () => {
      shopApplyInfo.value[3] = initInfo[3]
    }

    // 结算信息
    const settlementInfo = computed(() => shopApplyInfo.value[4])

    const setShopApplySettlementInfo = (info: Api.ShopApply.SettlementForm) => {
      shopApplyInfo.value[4] = info
    }

    const clearShopApplySettlementInfo = () => {
      shopApplyInfo.value[4] = initInfo[4]
    }
    return {
      shopApplyInfo,
      // 账号信息
      accountInfo,
      setShopApplyAccountInfo,
      clearShopApplyAccountInfo,
      // 法人信息
      personalInfo,
      setShopApplyPersonalInfo,
      clearShopApplyPersonalInfo,
      // 商户信息
      merchantInfo,
      setShopApplyMerchantInfo,
      clearShopApplyMerchantInfo,
      // 门店信息
      shopInfo,
      setShopApplyShopInfo,
      clearShopApplyShopInfo,
      // 结算信息
      settlementInfo,
      setShopApplySettlementInfo,
      clearShopApplySettlementInfo,
    }
  },
  { persist: true },
)
