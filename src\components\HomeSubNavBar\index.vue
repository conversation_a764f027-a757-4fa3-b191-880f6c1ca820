<template>
  <div
    :style="{ paddingTop: menuButtonInfo.top + 'px', background: themeStore?.navColor }"
    class="pb20rpx transition-all-300"
  >
    <div
      :style="{
        height: menuButtonInfo.height + 'px',
        marginRight: `${menuButtonInfo.width * 2 + 10}rpx`,
      }"
      class="px26rpx box-border flex items-center gap-x-20px"
    >
      <div v-if="leftTitle" @click="handleBack" class="flex items-center">
        <div class="i-carbon-chevron-left text-20px text-white font-bold"></div>
        <span class="text-20px text-white font-bold">{{ leftTitle }}</span>
      </div>
      <div
        @click="hanldeLocationChoose"
        class="flex items-center gap-x-10rpx font-bold text-white flex-1"
      >
        <div class="i-carbon-location text-16px text-white font-bold"></div>
        <div class="line-clamp-1">{{ locationStore.locationInfo?.cityName ?? '请选择位置' }}</div>
        <div class="i-carbon-chevron-right text-14px text-white font-bold"></div>
      </div>
    </div>
    <div
      :style="{ height: menuButtonInfo.height + 'px' }"
      class="flex items-center flex-1 of-hidden px-26rpx box-border gap-x-16rpx mt10px"
    >
      <div
        class="flex flex-1 of-hidden items-center h-full of-hidden box-border pr-1px gap-x-10rpx rd-30rpx"
      >
        <!-- <wd-icon name="search" size="28rpx" color="#999999"></wd-icon>
        <wd-input
          :placeholder="placeholder"
          inputmode="search"
          no-border
          clearable
          v-model="searchVal"
          confirm-type="search"
          @confirm="handleConfirm"
          @clear="handleClear"
          placeholderClass="text-#999999"
          custom-class="search !flex-1 !text-#999999 !h-full !flex !items-center !text-24rpx"
        ></wd-input>
        <wd-button
          @click="handleConfirm"
          custom-class="!w-108rpx !p-unset !min-w-unset !px30rpx !py14rpx !box-border !rd-30rpx !h-58rpx"
        >
          搜索
        </wd-button> -->
        <FakeSearch
          :hide-scan="hideScan"
          class="flex-1 of-hidden h-full"
          :handle-go-search="handleSearch"
          :keywords="['手机', '牙刷', '洗衣机', '空调', '冰箱']"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import FakeSearch from '@/components/FakeSearch/index.vue'
import { useLocationStore } from '@/store/location'
import { useThemeStore } from '@/store/theme'
import { handleLocationSuccess } from '@/utils'

const themeStore = useThemeStore()
const props = defineProps<{
  leftTitle?: string
  placeholder: string
  voiceSearch?: string
  hideScan?: boolean
}>()

const locationStore = useLocationStore()
const emits = defineEmits<{
  updateInputValue: [keyword: string]
  updateLocation: []
}>()

const menuButtonInfo = ref({
  width: 0,
  height: 32,
  top: 20,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

const searchVal = ref('')

watch(
  () => props?.voiceSearch,
  (val) => {
    if (val) {
      searchVal.value = props.voiceSearch
    }
  },
)

const handleConfirm = () => {
  emits('updateInputValue', searchVal.value)
}

const handleClear = () => {
  searchVal.value = ''
  emits('updateInputValue', searchVal.value)
}

// 选择位置
const hanldeLocationChoose = () => {
  uni.chooseLocation({
    success: handleLocationSuccess(locationStore),
    fail: (err) => {
      console.log(err)
    },
  })
}

const handleBack = () => uni.navigateBack()

// 搜索
const handleSearch = () => {
  uni.navigateTo({ url: '/pages-sub/search/index?from=mall' })
}
</script>

<style lang="scss" scoped>
//
</style>
