import { useShopApplyStore } from '@/store'
import { computed } from 'vue'

export function useShopApplyEditor() {
  const store = useShopApplyStore()

  // 各模块信息
  const account = computed(() => store.accountInfo)
  const personal = computed(() => store.personalInfo)
  const merchant = computed(() => store.merchantInfo)
  const shop = computed(() => store.shopInfo)
  const settlement = computed(() => store.settlementInfo)

  // 修改方法
  const updateAccount = (val: Partial<typeof account.value>) => {
    store.setShopApplyAccountInfo({ ...account.value, ...val })
  }

  const updatePersonal = (val: Partial<typeof personal.value>) => {
    store.setShopApplyPersonalInfo({ ...personal.value, ...val })
  }

  const updateMerchant = (val: Partial<typeof merchant.value>) => {
    store.setShopApplyMerchantInfo({ ...merchant.value, ...val })
  }

  const updateShop = (val: Partial<typeof shop.value>) => {
    store.setShopApplyShopInfo({ ...shop.value, ...val })
  }

  const updateSettlement = (val: Partial<typeof settlement.value>) => {
    store.setShopApplySettlementInfo({ ...settlement.value, ...val })
  }

  // 清空方法
  const resetAll = () => {
    store.clearShopApplyAccountInfo()
    store.clearShopApplyPersonalInfo()
    store.clearShopApplyMerchantInfo()
    store.clearShopApplyShopInfo()
    store.clearShopApplySettlementInfo()
  }

  return {
    // 全部信息
    form: store.shopApplyInfo,

    // 单个模块
    account,
    personal,
    merchant,
    shop,
    settlement,

    // 更新方法
    updateAccount,
    updatePersonal,
    updateMerchant,
    updateShop,
    updateSettlement,

    // 重置
    resetAll,
  }
}
