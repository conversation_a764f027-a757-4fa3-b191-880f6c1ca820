import pagesConfig from '@/pages.json'
import { isMpWeixin } from './platform'
import { useLoginSettingStore } from '@/store'
import type { UploadMethod } from 'wot-design-uni/components/wd-upload/types'
import { fetchAddress, getImgById } from '@/service'
import dayjs from 'dayjs'

//#ifdef H5
import jweixin from 'weixin-js-sdk'
//#endif
const { pages, subPackages, tabBar = { list: [] } } = { ...pagesConfig }

const appId = import.meta.env.VITE_APPID

/*
 * 跳转页面
 */
export const gotoPage = (url, type) => {
  if (!url || url.length == 0) {
    return false
  }

  if (url.substr(0, 1) !== '/') {
    url = '/' + url
  }
  let p = url
  if (url.indexOf('?') != -1) {
    p = url.substr(0, url.indexOf('?'))
    // #ifdef  H5
    if (url.search('appId') == -1) {
      url = url + '&appId=' + appId
    }
    // #endif
  } else {
    // #ifdef  H5
    if (url.search('appId') == -1) {
      url = url + '?appId=' + appId
    }
    // #endif
  }
  if (type == 'redirect') {
    uni.redirectTo({
      url: url,
    })
    return
  }
  if (type == 'reLaunch') {
    uni.reLaunch({
      url: url,
    })
    return
  }
  // 普通页面
  uni.navigateTo({
    url: url,
  })
}

export const getLastPage = () => {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包会报错，所以改用下面这个【虽然我加了 src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/** 判断当前页面是否是 tabbar 页  */
export const getIsTabbar = () => {
  try {
    const lastPage = getLastPage()
    const currPath = lastPage?.route

    return Boolean(tabBar?.list?.some((item) => item.pagePath === currPath))
  } catch {
    return false
  }
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 '/pages/login/index'
 * redirectPath 如 '/pages/demo/base/route-interceptor'
 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  // console.log(path, queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log(key, value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 这里设计得通用一点，可以传递 key 作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的 pages，如果传递了 key, 则表示通过 key 过滤
 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)

/**
 * 根据微信小程序当前环境，判断应该获取的 baseUrl
 */
export const getEnvBaseUrl = () => {
  // 请求基准地址
  let baseUrl = import.meta.env.VITE_SERVER_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_DEVELOP || baseUrl
        break
      case 'trial':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_TRIAL || baseUrl
        break
      case 'release':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_RELEASE || baseUrl
        break
    }
  }

  return baseUrl
}

/**
 * 根据微信小程序当前环境，判断应该获取的 UPLOAD_BASEURL
 */
export const getEnvBaseUploadUrl = () => {
  // 请求基准地址
  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP || baseUploadUrl
        break
      case 'trial':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_TRIAL || baseUploadUrl
        break
      case 'release':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_RELEASE || baseUploadUrl
        break
    }
  }

  return baseUploadUrl
}

export function toDecimal(value: string | number): string {
  const num = Number.parseFloat(value as string)
  if (Number.isNaN(num)) return '0.00'

  const thirdDigit = Math.floor(num * 1000) % 10 // 取第3位小数
  let result = Math.round(num * 100) // 默认四舍五入两位

  if (thirdDigit >= 5) {
    result -= 1 // 向下调整一点
  }

  const final = (result / 100).toFixed(2)
  return final
}

export function getScore(n: number | string, type: 1 | 2): number {
  const value = Number(n)

  if (Number.isNaN(value) || value <= 0) {
    return 0
  }

  const decimalPart = value % 1

  if (type === 1) {
    return value - decimalPart // 取整数部分，向下取整
  }

  if (type === 2) {
    return decimalPart === 0 ? 0 : 1
  }

  return 0 // 兜底
}

export const getVisitcode = (): string => {
  let visitcode: string = uni.getStorageSync('visitcode')

  if (!visitcode) {
    // 生成 UUID v4
    visitcode = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c: string): string => {
      const r = Math.floor(Math.random() * 16)
      const v = c === 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })

    visitcode = visitcode.replace(/-/g, '')
    uni.setStorageSync('visitcode', visitcode)
  }

  return visitcode
}

export function getTotalProductCount(cartList: Api.Home.CartListItem[]): number {
  return cartList.reduce((sum, cartItem) => {
    const count = cartItem.productList.reduce((subSum, product) => {
      return subSum + product.totalNum
    }, 0)
    return sum + count
  }, 0)
}

export const debounce = (fn, delay = 300) => {
  let timer: ReturnType<typeof setTimeout> | null = null
  return (...args) => {
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

export function removePunctuation(input: string): string {
  // 匹配中英文标点符号
  return input.replace(/[!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~，。！？、；：“”‘’（）《》【】]/g, '')
}

export function getSceneData(query: Record<string, any>): Record<string, any> {
  return query.scene ? scene_decode(query.scene) : {}
}
export function scene_decode(e) {
  if (e === undefined) return {}
  const scene = decodeURIComponent(e)
  const params = scene.split(',')
  const data = {}
  for (const i in params) {
    const val = params[i].split(':')
    if (val.length > 0 && val[0]) {
      data[val[0]] = val[1] || null
    }
  }
  return data
}

export const lklPay = (resp_data: any, callBack: () => void) => {
  // let appId = 'wx889424d565967811' // 默认为：拉卡拉聚合收银台小程序的appId
  // if (counterUrl.includes('q.huijingcai.top') || counterUrl.includes('q.lakala.com')) {
  //   appId = 'wxc3e4d1682da3053c' // 拉卡拉收款宝小程序appId
  // }
  // uni.openEmbeddedMiniProgram({
  //   appId,
  //   path: `payment-cashier/pages/checkout/index?source=WECHATMINI&counterUrl=${encodeURIComponent(
  //     counterUrl,
  //   )}`,
  //   envVersion: 'release',
  //   // release: 正式版  trial: 体验版
  //   success: (res) => {
  //     // 打开成功
  //     callBack && callBack()
  //   },
  //   fail: (res) => {
  //     uni.showToast({ title: '打开支付失败，请稍后再试', icon: 'none' })
  //   },
  // })
  console.log('resp_data', resp_data)

  //小程序支付
  // #ifdef  MP-WEIXIN
  uni.requestPayment({
    provider: 'wxpay',
    timeStamp: resp_data.acc_resp_fields.time_stamp,
    nonceStr: resp_data.acc_resp_fields.nonce_str,
    package: resp_data.acc_resp_fields.package,
    signType: resp_data.acc_resp_fields.sign_type,
    paySign: resp_data.acc_resp_fields.pay_sign,
    orderInfo: '', // 仅微信支付可为空字符串
    success: (res) => {
      // 打开成功
      callBack && callBack()
    },
    fail: (res) => {
      console.log(res)
      uni.showToast({ title: '打开支付失败，请稍后再试', icon: 'none' })
    },
  })
  // #endif
  //公众号支付
  // #ifdef  H5
  if (isWeixin()) {
    console.log('isWeixin()', isWeixin())
    WeixinJSBridge.invoke(
      'getBrandWCPayRequest',
      {
        timeStamp: resp_data.acc_resp_fields.time_stamp,
        nonceStr: resp_data.acc_resp_fields.nonce_str,
        package: resp_data.acc_resp_fields.package,
        signType: resp_data.acc_resp_fields.sign_type,
        paySign: resp_data.acc_resp_fields.pay_sign,
        appId: resp_data.acc_resp_fields.app_id,
      },
      function (res) {
        console.log('res', res)
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          // 打开成功
          callBack && callBack()
        } else if (res.err_msg == 'get_brand_wcpay_request:cancel') {
          uni.showToast({ title: '支付取消', icon: 'none' })
        } else {
          uni.showToast({ title: '订单未支付成功', icon: 'none' })
        }
      },
    )
  }
  // else {
  //   if (result.data.wxPayVersion == 2) {
  //     window.location.href = result.data.payment.mwebUrl + '&redirect_url=' + result.data.returnUrl;
  //   } else {
  //     window.location.href = result.data.payment + '&redirect_url=' + result.data.returnUrl;
  //   }
  //   return;
  // }
  if (isAlipay()) {
    window.location.href = resp_data.acc_resp_fields.code
    return
    // 通过传入交易号唤起快捷调用方式(注意tradeNO大小写严格)
    // AlipayJSBridge.call("tradePay", {
    //   tradeNO: resp_data.trade_no
    // }, function (data) {
    //   alert(JSON.stringify(data))
    //   if ("9000" == data.resultCode) {
    //     console.log('支付成功');
    //   }
    // });
  }
  // #endif
  // #ifdef  APP-PLUS
  //微信支付
  // wxAppPay(result, self, success, fail);
  // #endif
}

export const isWeixin = (): boolean => {
  const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : ''
  return /micromessenger/.test(ua)
}

export const isAlipay = (): boolean => {
  const ua = typeof navigator !== 'undefined' && navigator.userAgent?.toLowerCase()
  return !!ua && ua.includes('alipaydefined')
}

// 获取平台标识
export const getPlatform = (): 'android' | 'ios' | 'mp' | 'zfb' | 'h5' | 'wx' => {
  let platform: 'android' | 'ios' | 'mp' | 'zfb' | 'h5' | 'wx' = 'wx'

  // #ifdef APP-PLUS
  const sysPlatform = uni.getSystemInfoSync().platform
  platform = sysPlatform === 'android' ? 'android' : 'ios'
  // #endif

  // #ifdef H5
  platform = isWeixin() ? 'mp' : isAlipay() ? 'zfb' : 'h5'
  // #endif

  return platform
}

export const mpMessage = (signPackage?: {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
}): void => {
  // #ifdef H5
  if (!signPackage) return

  jweixin.config({
    debug: false,
    appId: signPackage.appId,
    timestamp: signPackage.timestamp,
    nonceStr: signPackage.nonceStr,
    signature: signPackage.signature,
    jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
    openTagList: ['wx-open-subscribe', 'wx-open-launch-app', 'wx-open-launch-weapp'],
  })
  // #endif
}

export function compareVersion(v1: string, v2: string): number {
  const v1Parts = v1.split('.').map(Number)
  const v2Parts = v2.split('.').map(Number)
  const len = Math.max(v1Parts.length, v2Parts.length)

  for (let i = 0; i < len; i++) {
    const num1 = v1Parts[i] || 0
    const num2 = v2Parts[i] || 0
    if (num1 > num2) return 1
    if (num1 < num2) return -1
  }
  return 0
}

export const subMessage = (temlIds: string[], callback: () => void): void => {
  // #ifdef MP-WEIXIN
  const version = uni.getSystemInfoSync().SDKVersion

  if (temlIds && temlIds.length !== 0 && compareVersion(version, '2.8.2') >= 0) {
    uni.hideLoading()

    uni.requestSubscribeMessage({
      tmplIds: temlIds,
      success: (res: WechatMiniprogram.RequestSubscribeMessageSuccessCallbackResult) => {
        // 可以根据需要处理 res
      },
      fail: (res: any) => {
        // 可处理失败信息
      },
      complete: (res: any) => {
        callback()
      },
    })
  } else {
    callback()
  }
  // #endif

  // #ifndef MP-WEIXIN
  callback()
  // #endif
}

// 工具函数：安全地设置存储
export const setStorageIfValid = (key: string, value: any, condition: boolean = true) => {
  if (condition && value != null && Number(value) > 0) {
    uni.setStorageSync(key, value)
  }
}

export function doLogin() {
  const loginSettingStore = useLoginSettingStore()

  const pages = getCurrentPages()
  if (pages.length) {
    const currentPage = pages[pages.length - 1] as {
      route: string
      $page?: { options: Record<string, any> }
    }

    const loginPages = [
      'pages-sub/login/login',
      'pages-sub/login/weblogin',
      'pages-sub/login/openlogin',
    ]

    if (!loginPages.includes(currentPage.route)) {
      uni.setStorageSync('currentPage', currentPage.route)

      // #ifdef MP-WEIXIN || MP-ALIPAY
      // 小程序端安全读取 $page.options
      if (currentPage.$page?.options) {
        uni.setStorageSync('currentPageOptions', currentPage.$page.options)
      }
      // #endif

      // #ifdef H5
      // H5 不一定有 $page，可以手动解析 query 或跳过
      // 例如：使用 uni.getCurrentPage().options 替代
      try {
        const h5Page = currentPage as unknown as {
          options?: Record<string, any>
        }
        if (h5Page.options) {
          uni.setStorageSync('currentPageOptions', h5Page.options)
        }
      } catch (e) {
        console.warn('Failed to read options in H5:', e)
      }
      // #endif
    }
  }

  const invitationId = uni.getStorageSync('invitationId') || 0
  const refereeId = uni.getStorageSync('refereeId') || 0
  const type = uni.getStorageSync('type') || ''

  // #ifdef H5
  if (isWeixin() && loginSettingStore.loginSetting?.mpState == 1) {
    if (!type) {
      window.location.href = `${import.meta.env.VITE_SERVER_BASEURL}/api/front/user/userMp/login?appId=${import.meta.env.VITE_APPID}&refereeId=${refereeId}&invitationId=${invitationId}`
    } else {
      window.location.href = `${import.meta.env.VITE_SERVER_BASEURL}/api/front/user/userMp/login?appId=${import.meta.env.VITE_APPID}&refereeId=${refereeId}&invitationId=${invitationId}&type=${type}`
    }
  } else if (isAlipay()) {
    window.location.href = `${import.meta.env.VITE_SERVER_BASEURL}/api/front/user/userZfb/login?appId=${import.meta.env.VITE_APPID}&refereeId=${refereeId}&invitationId=${invitationId}`
  } else {
    uni.navigateTo({ url: '/pages-sub/login/weblogin' })
  }
  // #endif

  // #ifdef APP-PLUS
  uni.redirectTo({ url: '/pages-sub/login/openlogin' })
  // #endif
  // #ifndef  H5
  /*
			wxOpen true 开启微信授权 false 不开启微信授权
		*/
  let wxOpen = loginSettingStore?.loginSetting?.wxOpen
  if (wxOpen) {
    uni.navigateTo({ url: '/pages-sub/login/login' })
  } else {
    uni.navigateTo({ url: '/pages-sub/login/weblogin' })
  }
  // #endif
}

export const createUploadHandler = (fileType: 'image' | 'video' = 'image'): UploadMethod => {
  return (file, formData, options) => {
    console.log(`[UPLOAD-${fileType.toUpperCase()}]`, { file, formData, options })

    formData.appId = import.meta.env.VITE_APPID

    const uploadTask = uni.uploadFile({
      url: options.action,
      name: options.name || 'iFile',
      fileName: options.name || 'iFile',
      fileType,
      header: options.header || {},
      formData,
      filePath: file.url,
      success: (res) => {
        try {
          const data = JSON.parse(res?.data || '{}')
          if (res.statusCode === options.statusCode) {
            options.onSuccess(data, file, formData)
          } else {
            options.onError({ ...res, errMsg: res.errMsg || 'Upload failed' }, file, formData)
          }
        } catch (e) {
          options.onError({ errMsg: 'Invalid JSON response' }, file, formData)
        }
      },
      fail: (err) => {
        console.error(`[UPLOAD-FAIL-${fileType.toUpperCase()}]`, err)
        options.onError(err, file, formData)
      },
    })

    uploadTask.onProgressUpdate?.((progress) => {
      options.onProgress(progress, file)
    })
  }
}

export function convertCategoryArrayToPickerMap(categoryArray: Api.User.ShopCategoryItem[]) {
  const map = {}

  for (const node of categoryArray) {
    const parentId = node.parentId.toString()
    const currentId = node.categoryId.toString()

    // 一级分类挂在 parentId（通常是 '0'）
    if (!map[parentId]) {
      map[parentId] = []
    }

    map[parentId].push({
      label: node.name,
      value: currentId,
    })

    // 只处理两级结构：把当前节点的 children（如果有）挂载到当前节点 ID 下
    if (node.children?.length) {
      map[currentId] = node.children.map((child) => ({
        label: child.name,
        value: child.categoryId.toString(),
      }))
    } else {
      // 没有 children 的话也要补空数组，防止联动报错
      map[currentId] = [{ label: '', value: '' }]
    }
  }

  return map
}

export const chunkCategoryList = (list: any[], size = 10) => {
  const result = []
  for (let i = 0; i < list.length; i += size) {
    result.push(list.slice(i, i + size))
  }
  return result
}
// 定义函数，接收消息列表，返回未读总数或空字符串
export const getNoReadCounts = (
  messageList:
    | {
        noReadCount: number
      }[]
    | undefined
    | null,
): number | '' => {
  if (!messageList || messageList.length === 0) return ''
  return messageList.reduce((sum, curItem) => sum + curItem.noReadCount, 0)
}
/*复制*/
export const copyQQ = (message) => {
  //#ifdef MP-WEIXIN
  uni.setClipboardData({
    //准备复制的数据
    data: message,
    success: function (res) {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
        mask: true,
        duration: 2000,
      })
    },
  })
  //#endif
  //#ifdef H5
  var input = document.createElement('input')
  input.value = message
  document.body.appendChild(input)
  input.select()
  input.setSelectionRange(0, input.value.length), document.execCommand('Copy')
  document.body.removeChild(input)
  uni.showToast({
    title: '复制成功',
    icon: 'success',
    mask: true,
    duration: 2000,
  })
  //#endif
}
/*拨打电话*/
export const callPhone = (phone) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

/**
 * 格式化大数值，超过5位（>=10000）时转为"x.xxw"格式，否则原样返回
 * @param value 数值或字符串
 * @returns string
 */
export function formatLargeNumber(value: string | number): string {
  const num = Number(value)
  if (isNaN(num)) return '0'
  if (Math.abs(num) >= 10000) {
    return (num / 10000).toFixed(2) + '万'
  }
  return num.toString()
}

export function parseUrlParams(): Record<string, string> {
  const queryString = window.location.search
  const params: Record<string, string> = {}

  if (queryString.startsWith('?')) {
    queryString
      .substring(1)
      .split('&')
      .forEach((param) => {
        const [key, value = ''] = param.split('=')
        if (key) {
          params[decodeURIComponent(key)] = decodeURIComponent(value)
        }
      })
  }

  return params
}

export function toQueryString(params: Record<string, string>): string {
  return Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&')
}

export function convertLatexToDollarSyntax(content: string) {
  return content
    .replace(/\\\[/g, '$$') // 替换 \[ 为 $$
    .replace(/\\\]/g, '$$') // 替换 \] 为 $$
    .replace(/\\\(/g, '$') // 替换 \( 为 $
    .replace(/\\\)/g, '$') // 替换 \) 为 $
}

export const handleLocationSuccess = (locationStore: any) => {
  return async (res: UniApp.ChooseLocationSuccess) => {
    try {
      const { data } = await fetchAddress({
        lon: res?.longitude,
        lat: res?.latitude,
        ver: 1,
      })

      const addressInfo = JSON.parse((data as unknown as string) ?? '{}')

      locationStore.setLocationInfo({
        poi: addressInfo?.result?.addressComponent?.poi ?? '',
        cityName:
          addressInfo?.result?.addressComponent?.city ||
          addressInfo?.result?.addressComponent?.province ||
          '',
        location: {
          lat: res?.latitude,
          lon: res?.longitude,
        },
      })
    } catch (err) {
      console.error('处理定位信息失败:', err)
    }
  }
}

export async function convertBackendDataToFormMap(
  raw: Api.ShopApply.ShopApplyDetail,
): Promise<Api.ShopApply.FormMapType> {
  const accountForm: Api.ShopApply.AccountForm = {
    mobile: raw?.mobile || '',
    userName: raw?.userName || '',
    password: raw?.password || '',
  }

  const personalForm: Api.ShopApply.PersonalForm = {
    identityImag1: raw?.identityImag1 || 0,
    forShowidentityImag1: raw?.forShowidentityImag1 || [],
    identityImag2: raw?.identityImag2 || 0,
    forShowidentityImag2: raw?.forShowidentityImag2 || [],
    larName: raw?.larName || '',
    larIdCard: raw?.larIdCard || '',
    idValidity: (raw?.idValidity as 0 | 1) ?? 0,
    larIdCardStDt: raw?.larIdCardStDt || '',
    forShowlarIdcardStDt: raw?.larIdCardStDt ? dayjs(raw?.larIdCardStDt).valueOf() : 0,
    larIdCardExpDt: raw?.larIdCardExpDt || '',
    forShowlarIdcardExpDt: raw?.larIdCardExpDt ? dayjs(raw?.larIdCardExpDt).valueOf() : 0,
    merContactMobile: raw?.mobile || '',
    merContactEmail: raw?.merContactEmail || '',
  }

  const merchantForm: Api.ShopApply.MerchantForm = {
    businessId: raw?.businessId || 0,
    forShowbusinessId: raw?.forShowbusinessId || [],
    merBlisName: raw?.merBlisName || '',
    merBlis: raw?.merBlis || '',
    isPerpetual: (Number(raw?.isPerpetual) as 0 | 1) ?? 0,
    merBlisStDt: raw?.merBlisStDt || '',
    forShowMerBlisStDt: raw?.merBlisStDt ? dayjs(raw?.merBlisStDt).valueOf() : 0,
    merBlisExpDt: raw?.merBlisExpDt || '',
    forShowMerBlisExpDt: raw?.merBlisExpDt ? dayjs(raw?.merBlisExpDt).valueOf() : 0,
    supplierCommissionRate: raw?.supplierCommissionRate || '',
    categoryId: raw?.categoryId?.toString() || '',
    forShowcategoryId: [raw?.categoryPid ?? 0, raw?.categoryId ?? 0],
    forShowBussinessInfo: [raw?.provinceCode, raw?.cityCode, raw?.countyCode],
    provinceCode: raw?.provinceCode || '',
    cityCode: raw?.cityCode || '',
    countyCode: raw?.countyCode || '',
    merBusiContent: raw?.merBusiContent || '',
    storeName: raw?.storeName || '',
  }

  const shopForm: Api.ShopApply.ShopForm = {
    storeName: raw?.storeParam?.storeName || '',
    provinceId: raw?.storeParam?.provinceId || 0,
    area: raw?.storeParam?.area || '',
    forShowarea: [
      raw?.storeParam?.provinceId || 0,
      raw?.storeParam?.cityId || 0,
      raw?.storeParam?.regionId || 0,
    ],
    cityId: raw?.storeParam?.cityId || 0,
    regionId: raw?.storeParam?.regionId || 0,
    address: raw?.storeParam?.address || '',
    shopHoursSTime: raw?.storeParam?.shopHoursSTime || '',
    shopHoursETime: raw?.storeParam?.shopHoursETime || '',
    summary: raw?.storeParam?.summary || '',
    logoImageId: raw?.storeParam?.logoImageId || 0,
    forShowlogoImageId: raw?.forShowlogoImageId || [],
    cashRegisterId: raw?.storeParam?.cashRegisterId || 0,
    forShowCashRegisterId: raw?.forShowCashRegisterId || [],
    interiorShotId: raw?.storeParam?.interiorShotId || 0,
    forShowInteriorShotId: raw?.forShowInteriorShotId || [],
    latitude: raw?.storeParam?.latitude || '',
    longitude: raw?.storeParam?.longitude || '',
    showLocation: '',
  }

  const settlementForm: Api.ShopApply.SettlementForm = {
    acctIdType: Number(raw?.acctIdType) as 0 | 1,
    acctTypeCode: Number(raw?.acctTypeCode) as 57 | 58,
    bankCardImageId1: raw?.bankCardImageId1 || 0,
    forShowbankCardImageId1: raw?.forShowbankCardImageId1 || [],
    bankCardImageId2: raw?.bankCardImageId2 || 0,
    forShowBankCardImageId2: raw?.forShowBankCardImageId2 || [],
    acctIdCardImageId1: raw?.acctIdCardImageId1 || 0,
    forShowAcctIdcardImageId1: raw?.forShowAcctIdcardImageId1 || [],
    acctIdCardImageId2: raw?.acctIdCardImageId2 || 0,
    forShowAcctIdcardImageId2: raw?.forShowAcctIdcardImageId2 || [],
    bankAccountLicenseId: raw?.bankAccountLicenseId || 0,
    forShowbankAccountLicenseId: raw?.forShowbankAccountLicenseId || [],
    delegatedAuthorizationId: 0,
    forShowDelegatedAuthorizationId: raw?.forShowDelegatedAuthorizationId || [],
    acctName: raw?.acctName || '',
    acctNo: raw?.acctNo || '',
    acctIdCard: raw?.acctIdCard || '',
    merContactMobile: raw?.mobile || '',
    otherImageIds: raw?.otherImageIds || '',
    forShowotherImageIds: raw?.forShowotherImageIds || [],
    forShowBankInfo: ['', '', raw?.openningBankCode],
    settleProvinceCode: raw?.settleProvinceCode || '',
    settleProvinceName: raw?.settleProvinceName || '',
    settleCityCode: raw?.settleCityCode || '',
    settleCityName: raw?.settleCityName || '',
    openningBankCode: raw?.openningBankCode || '',
    openningBankName: raw?.openningBankName || '',
    clearingBankCode: raw?.clearingBankCode || '',
    isLong: raw?.idValidity ?? 0,
    accountIdDtStart: raw?.accountIdDtStart || '',
    forShowAccountIdDtStart: raw?.accountIdDtStart ? dayjs(raw?.accountIdDtStart).valueOf() : 0,
    accountIdDtEnd: raw?.accountIdDtEnd || '',
    forShowAccountIdDtEnd: raw?.accountIdDtEnd ? dayjs(raw?.accountIdDtEnd).valueOf() : 0,
  }

  if (raw?.otherImageIds) {
    const imageIds = raw.otherImageIds.split(',')
    const imagePromises = imageIds.map(async (id) => {
      const { data } = await getImgById({ imageId: Number(id) })
      return { url: data }
    })
    settlementForm.forShowotherImageIds = await Promise.all(imagePromises)
  }

  return [accountForm, personalForm, merchantForm, shopForm, settlementForm]
}
