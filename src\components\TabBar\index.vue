<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <wd-tabbar
    fixed
    @change="onTabClick"
    inactiveColor="#999999"
    activeColor="#FF7D26"
    v-model="current"
    safeAreaInsetBottom
    :zIndex="10000"
    :custom-style="`${!bottom ? 'padding-bottom:30rpx' : ''}`"
    :bordered="false"
    placeholder
  >
    <wd-tabbar-item
      v-for="(item, index) in tabs"
      :key="index"
      :value="item.badge"
      :title="item.title"
    >
      <template #icon="{ active }">
        // #ifdef MP-WEIXIN
        <template v-if="item.speak">
          <div class="flex justify-center items-center">
            <wd-img
              @touchstart.stop.prevent="handleTouchStart"
              @touchend.stop.prevent="handleTouchEnd"
              round
              height="160rpx"
              width="160rpx"
              mode="aspectFit"
              customClass="relative top--5px"
              :src="voiceState.isRecording ? item.activeIcon : item.icon"
            ></wd-img>
          </div>
          <div
            v-if="voiceState.isRecording"
            class="absolute top--154rpx left-50% translate-x--50% flex items-center justify-center voicebg pb-15px box-border"
          >
            <LottieAnimation :animation-data="animationData" />
          </div>
        </template>
        // #endif
        <template v-if="!item.speak">
          <wd-img v-if="!active" height="40rpx" width="40rpx" :src="item.icon"></wd-img>
          <wd-img v-if="active" height="40rpx" width="40rpx" :src="item.activeIcon"></wd-img>
        </template>
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<script lang="ts" setup>
import LottieAnimation from '@/components/LottieAnimation/index.vue'
import animationData from '@/static/animations/Animation.json'
import { useVoiceInput } from '@/hooks/useVoiceInput'

import { useMessageStore, useUserStore } from '@/store'
import { useSearchStore } from '@/store/search'
import { getNoReadCounts } from '@/utils'
import { voiceSelectStore, voiceSelectProduct } from '@/service'

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const emits = defineEmits<{
  updateVoiceRes: [text: string]
}>()

const messageStore = useMessageStore()

const userStore = useUserStore()

onMounted(() => {
  if (!!userStore?.isLogined && messageStore?.messageData.length === 0)
    messageStore.getMessageData()
})

const searchStore = useSearchStore()

// #ifdef MP-WEIXIN
const { voiceState, handleVoiceInputTouchStart, handleVoiceInputTouchEnd, initVoiceListeners } =
  useVoiceInput()
// #endif

const pages = getCurrentPages()
const currPage = computed(() => pages[pages.length - 1]?.route)
const msgNoRead = computed(() => getNoReadCounts(messageStore?.messageData ?? []) || 0)

const tabs = computed(() => [
  {
    title: '首页',
    icon: 'https://file.shanqianqi.com/image/2025/06/13/6721f6611ba348a5b20a7ce6d917c263.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/06/13/788e5bbeca614c12895ff56b0f1746d7.png',
    url: '/pages/index/index',
  },
  {
    title: '消息',
    icon: 'https://file.shanqianqi.com/image/2025/06/13/b4863c266f374eaba7bceedfa29912ab.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/06/13/495b5345e1354801987849bde5a7053a.png',
    url: '/pages/message/index',
    badge: msgNoRead.value,
  },
  {
    title: '',
    icon:
      currPage.value === 'pages-sub/home/<USER>/index' || currPage.value === 'pages/index/index'
        ? 'https://file.shanqianqi.com/image/2025/06/16/012f152283804ccc9190bee7f894e74e.png'
        : 'https://file.shanqianqi.com/image/2025/06/16/2c69c8c1156247d28ec758f4404758db.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/06/16/b79783a85c774a0183f0231b29266ed7.png',
    url: '',
    speak: true,
  },
  {
    title: '商城',
    icon: 'https://file.shanqianqi.com/image/2025/06/13/b6345eb9429f45a3a7156633b274521e.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/06/13/65eb4dd33d394910ba0511c2a3ff8ddd.png',
    url: '/pages-sub/home/<USER>/index',
  },
  {
    title: '我的',
    icon: 'https://file.shanqianqi.com/image/2025/06/13/5a1044be4b0043f88416564c6fb4ee36.png',
    activeIcon: 'https://file.shanqianqi.com/image/2025/06/13/f123b9c95f4b41eca82c98667827920b.png',
    url: '/pages/my/index',
  },
])

const current = ref(tabs.value.findIndex((tab) => tab.url === `/${currPage.value}`))

// 手指按下时
const handleTouchStart = () => {
  if (currPage.value === 'pages-sub/home/<USER>/index' || currPage.value === 'pages/index/index') {
    handleVoiceInputTouchStart()
  }
}

// 手指松开时
const handleTouchEnd = () => {
  if (currPage.value === 'pages-sub/home/<USER>/index' || currPage.value === 'pages/index/index') {
    // 松开时结束录音
    handleVoiceInputTouchEnd()
  }
}

const searchType = computed(() => {
  return currPage.value === 'pages-sub/home/<USER>/index' ? 'product' : 'store'
})

// #ifdef MP-WEIXIN
// 监听事件
initVoiceListeners(
  async (res: { tempFilePath: string; duration: number; fileSize: number; result: string }) => {
    const uploadUrl =
      searchType.value === 'store'
        ? `/api/front/AI/search/selectStore?appId=${import.meta.env.VITE_APPID}`
        : `/api/front/AI/search/selectProduct?appId=${import.meta.env.VITE_APPID}`
    console.log('🚀 ~ res:', res)
    if (currPage.value === 'pages-sub/home/<USER>/index') {
      emits('updateVoiceRes', res?.result.trim())
    } else {
      uni.showLoading({ title: '生成中', mask: true })

      if (res?.result) {
        if (searchType.value === 'store') {
          const { data } = await voiceSelectStore({
            datatype: '20',
            searchWord: res?.result.trim(),
          })
          console.log('🚀 ~ data:', data)

          searchStore.setSearchKeyword(res?.result?.trim())
          searchStore.setGoodsList(data || [])
          uni.navigateTo({ url: `/pages-sub/goods/list/index?searchType=${searchType.value}` })
        } else {
          const { data } = await voiceSelectProduct({
            datatype: '10',
            searchWord: res?.result.trim(),
          })
          console.log('🚀 ~ data:', data)

          searchStore.setSearchKeyword(res?.result?.trim())
          searchStore.setGoodsList(data || [])
          uni.navigateTo({ url: `/pages-sub/goods/list/index?searchType=${searchType.value}` })
        }

        // uni.uploadFile({
        //   url: `${import.meta.env.VITE_SERVER_BASEURL}${uploadUrl}`,
        //   filePath: res?.tempFilePath,
        //   name: 'file',
        //   formData: { searchWord: res?.result.trim() },
        //   timeout: 60 * 1000,
        //   success: (uploadFileRes) => {
        //     console.log('🚀 ~ initVoiceListeners ~ uploadFileRes:', uploadFileRes)
        //     searchStore.setSearchKeyword(res?.result.trim())
        //     searchStore.setGoodsList(JSON.parse(uploadFileRes?.data || '[]'))
        //     uni.navigateTo({ url: `/pages-sub/goods/list/index?searchType=${searchType.value}` })
        //   },
        //   fail: (err) => {
        //     uni.showToast({
        //       title: '生成失败，请稍后再试',
        //       icon: 'none',
        //       duration: 1000,
        //     })
        //     console.error('uni.uploadFile err->', err)
        //   },
        //   complete: () => {
        //     setTimeout(() => {
        //       uni.hideLoading()
        //     }, 500)
        //   },
        // })
      }
    }
  },
)
// #endif

const onTabClick = ({ value }: { value: number }) => {
  const tab = tabs.value[value]
  if (tab?.url && currPage.value !== tab.url.replace(/^\//, '')) {
    uni.reLaunch({ url: tab.url })
  }
}
</script>

<style lang="scss" scoped>
//
</style>
