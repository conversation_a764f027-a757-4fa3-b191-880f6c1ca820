<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="">
    <z-paging
      ref="paging"
      empty-view-text="没有回复哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :paging-style="{ background: '#F7F7FA', 'padding-bottom': `${bottom || 15}px` }"
      fixed
    >
      <template #top>
        <wd-navbar left-arrow safeAreaInsetTop placeholder fixed @click-left="handleBack">
          <template #title>
            <view class="">
              <span class="ml-20rpx">我的回复</span>
            </view>
          </template>
        </wd-navbar>
      </template>
      <div class="list content-container">
        <div
          class="li mb-20rpx"
          v-for="(item, index) in dataList"
          :key="item?.commentId"
          @click="details(item.postId)"
        >
          <div class="li-top flex justify-between">
            <div class="li-top-l flex">
              <image :src="item?.avatarUrl"></image>
              <div class="flex flex-col justify-center">
                <span class="text-24rpx mb-5rpx">{{ item?.nickName }}</span>
                <div class="flex">
                  <span class="text-22rpx text-#999999 mr-30rpx">
                    关注：{{ formatCount(item?.follower) }}
                  </span>
                  <span class="text-22rpx text-#999999">
                    粉丝：{{ formatCount(item?.followee) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="" @click.stop="showPicker(index)">
              <img
                src="https://file.shanqianqi.com/image/2025/06/25/10abd4489e3b46308255f1e8560e938b.png"
                alt=""
                class="h-30rpx w-30rpx"
              />
            </div>
          </div>
          <div class="li-cont flex flex-col">
            <span class="text-26rpx">
              {{ item.content }}
            </span>
            <div>
              <wd-img
                :src="item.image"
                width="150rpx"
                height="150rpx"
                radius="12rpx"
                mode="aspectFit"
                v-if="item?.image"
              ></wd-img>
            </div>

            <span class="text-22rpx text-#999999">{{ item.createTime }}</span>
          </div>
          <div class="bg-#F7F7FA p-20rpx rounded-10rpx mt-15rpx">
            <span v-if="item?.parentComment" class="text-#333333 text-28rpx">
              原评论：{{ item?.parentComment }}
            </span>
            <span v-if="item?.postTitle" class="text-#333333 text-28rpx">
              原文章：{{ item?.postTitle }}
            </span>
            <span v-if="!item?.parentComment && !item?.postTitle" class="text-#333333 text-28rpx">
              原评论:[图片]
            </span>
          </div>
        </div>
      </div>
      <!-- 删除确认选择器 -->
      <wd-action-sheet
        title="确认删除评论"
        v-model="showActionSheet"
        :actions="actions"
        @select="handleActionSelect"
        cancel-text="取消"
      ></wd-action-sheet>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { fetchMyComment, deleteComment } from '@/service'
const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const dataList = ref<Api.Forum.MyCommentItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchMyComment({ pageIndex, pageSize })
      paging?.value?.complete(data?.records)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

// 格式化数字显示
const formatCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  return count.toString()
}

// 操作菜单相关
const showActionSheet = ref(false)
const currentIndex = ref(-1)
const actions = ref([
  {
    name: '删除',
    color: '#FF4D4F',
  },
])

// 显示选择器
const showPicker = (index: number) => {
  currentIndex.value = index
  showActionSheet.value = true
}

// 处理选择操作
const handleActionSelect = ({ item }: { item: { name: string } }) => {
  if (item.name === '删除') {
    uni.showModal({
      title: '提示',
      content: '确定要删除吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await deleteComment({ commentId: dataList.value[currentIndex.value]?.commentId })
            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })

            paging?.value?.reload()
          } catch (error) {}
        }
      },
    })
  }
  showActionSheet.value = false
}
//列表详情
const details = (e) => {
  uni.navigateTo({
    url: `/pages-category/forum/details/index?postId=${e}`,
  })
}
// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
}

.list {
  margin-top: 25rpx;
  padding: 0 25rpx;
  .li {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx 40rpx;
    .li-top-l {
      image {
        width: 70rpx;
        height: 70rpx;
        border-radius: 20rpx;
        background-color: #ffe5d4;
        margin-right: 20rpx;
      }
    }
    .li-cont {
      margin-top: 10rpx;
      line-height: 1.6;
    }
    .li-more {
      span {
        margin-left: 10rpx;
        color: #666;
      }
    }
  }
}
</style>
