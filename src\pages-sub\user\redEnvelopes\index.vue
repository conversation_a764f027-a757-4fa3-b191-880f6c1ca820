<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '红包',
  },
}
</route>

<template>
  <view class="flex flex-col h-full pb-2 box-border bg-#F6F6F6">
    <div
      class="bgTop"
      :style="{
        paddingTop: menuButtonInfo.top + 'px',
        backgroundSize: 'cover',
        backgroundPosition: 'top',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'scroll',
      }"
    >
      <div
        class="flex items-center of-hidden box-border"
        :style="{
          height: menuButtonInfo.height + 'px',
          position: 'relative',
        }"
      >
        <div @click="goback()" class="flex items-center justify-center px42rpx">
          <wd-icon name="thin-arrow-left" size="28rpx" color="white"></wd-icon>
        </div>
        <div class="wallet flex-1 flex justify-center items-center text-white">我的红包</div>
      </div>
    </div>

    <div
      :style="{
        backgroundSize: 'cover',
        backgroundPositionY: `-${menuButtonInfo.top + menuButtonInfo.height}px`,
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'scroll',
      }"
      class="bgTop box-border w-full h-300rpx relative"
    >
      <view class="body-head px42rpx text-white pt-50rpx pb-70rpx box-border">
        <view class="flex-1 flex flex-col gap-y-20rpx">
          <view class="name">补贴总额</view>
          <view class="text-70rpx fw-bold">{{ formatLargeNumber(state.totalAmount) }}</view>
        </view>
        <view class="flex-1 text-30rpx flex flex-col justify-end items-end gap20rpx text-white">
          <view class="flex-1 flex flex-col gap-y-20rpx">
            <view class="name">红包余额</view>
            <view class="text-70rpx fw-bold">
              {{ formatLargeNumber(state.totalRemainingAmount) }}
            </view>
          </view>
        </view>
      </view>
      <view
        class="absolute left-[32rpx] right-[32rpx] bottom-[-50rpx]"
        style="width: calc(100% - 64rpx)"
      >
        <view class="h100rpx bg-white rounded-10rpx">
          <wd-calendar type="daterange" v-model="timePickerValue" @confirm="handleConfirm">
            <view class="flex items-center bg-none lh-90rpx">
              <view class="flex-1 flex justify-center items-center gap26rpx">
                <text>{{ dayjs(timePickerValue[0]).format('YYYY-MM-DD') }}</text>

                <image
                  class="w24rpx h24rpx"
                  src="https://file.shanqianqi.com/image/2025/06/14/5adb0c28f4594a26a41ab763417390d5.png"
                  mode="aspectFit"
                ></image>
              </view>
              <view class="flex-1 flex justify-center items-center l-h-100rpx gap26rpx">
                <text>{{ dayjs(timePickerValue[1]).format('YYYY-MM-DD') }}</text>
                <image
                  class="w24rpx h24rpx"
                  src="https://file.shanqianqi.com/image/2025/06/14/5adb0c28f4594a26a41ab763417390d5.png"
                  mode="aspectFit"
                ></image>
              </view>
            </view>
          </wd-calendar>
        </view>
      </view>
    </div>

    <div class="pt90rpx border-box">
      <!-- tab标签 -->
      <wd-tabs
        v-model="stateActive"
        @click="handleTabChange(stateActive)"
        slidable="always"
        auto-line-width
      >
        <block v-for="(item, index) in state.tabs" :key="index" custom-class="!bg-none">
          <wd-tab :title="`${item.name}`"></wd-tab>
        </block>
      </wd-tabs>
    </div>
    <!-- 中间内容 -->
    <view class="body flex-1 flex flex-col overflow-y-auto">
      <z-paging
        ref="paging"
        v-model="state.tableData"
        :default-page-size="10"
        :fixed="false"
        class="flex-1"
        @query="queryList"
      >
        <view class="px24rpx box-border">
          <view
            class="flex flex-col py20rpx gap20rpx"
            v-for="(item, index) in groupedData"
            :key="index"
          >
            <view class="flex justify-between items-center">
              <text class="text-28rpx fw-500 text-#333333">{{ item.title }}</text>
            </view>

            <div
              v-for="(ele, ix) in item?.records"
              :key="ix"
              @click="goFlowingWaterDetail(ele)"
              class="red-envelope-item"
            >
              <div
                :class="[
                  'red-envelope-left text-center flex flex-col gap26rpx text-24rpx px20rpx py36rpx divider-line card_one',
                  ele.isFinished == '3' ? 'text-#999999' : 'text-#FF3347 text-28rpx',
                ]"
              >
                <!-- text-80rpx  -->
                <text
                  :class="['text-40rpx fw-bold', ele.isFinished == '4' ? 'text-#FFA2AB' : '']"
                  class="text-40rpx fw-bold"
                >
                  {{ formatLargeNumber(ele.amount) }}
                </text>
                <view :class="['fw-400 text-20rpx', ele.isFinished == '4' ? 'text-#FFA2AB' : '']">
                  剩余({{ formatLargeNumber(ele.remainingAmount) }})
                </view>
              </div>

              <div class="red-envelope-right zilong h-full bg-white flex items-center">
                <div class="flex flex-col gap-y-20rpx px20rpx box-border text-20rpx">
                  <view class="flex items-center text-#999999">
                    <view>获得日期:</view>
                    <view>{{ dayjs(ele.createTime).format('YYYY.MM.DD HH:mm:ss') }}</view>
                  </view>
                  <view
                    :class="[
                      'flex items-center',
                      ele.isFinished == '3' ? 'text-#999999' : 'text-#F65458',
                    ]"
                  >
                    <view>失效日期:</view>
                    <view>{{ dayjs(ele.validityPeriod).format('YYYY.MM.DD HH:mm:ss') }}</view>
                  </view>
                </div>
                <div class="flex flex-col gap-y20rpx py20rpx box-border">
                  <div class="flex justify-center">
                    <div
                      style="border-bottom-color: #ff7d26"
                      class="px20rpx py8rpx rounded-10rpx bg-#FFECDF text-#FF7D26 text-20rpx fw-400"
                    >
                      明细记录
                    </div>
                  </div>
                  <!-- 待使用 -->
                  <image
                    v-if="ele.isFinished == '0'"
                    class="w160rpx h112rpx"
                    src="@/static/svg/toBeUsed.svg"
                    mode="aspectFit"
                  ></image>
                  <!-- 已用完 -->
                  <image
                    v-if="ele.isFinished == '2'"
                    class="w160rpx h112rpx"
                    src="@/static/svg/haveBeenExhausted.svg"
                    mode="aspectFit"
                  ></image>
                  <!-- 已过期 -->
                  <image
                    v-if="ele.isFinished == '3'"
                    class="w160rpx h112rpx"
                    src="@/static/svg/expired.svg"
                    mode="aspectFit"
                  ></image>
                  <!-- 已作废 -->
                  <image
                    v-if="ele.isFinished == '4'"
                    class="w160rpx h112rpx"
                    src="@/static/svg/voided.svg"
                    mode="aspectFit"
                  ></image>
                </div>
              </div>
            </div>
          </view>
        </view>
      </z-paging>
    </view>

    <wd-popup
      v-if="showCancelPopup"
      v-model="showCancelPopup"
      position="center"
      :closeable="false"
      custom-style="width: 600rpx;
      height: 800rpx;
      border-radius: 32rpx; padding: 0;"
      @close="handleClose"
    >
      <flowingWater
        :redPacketId="state.redPacketId"
        :remainingAmount="state.redPacketRemainingAmount"
        :totalAmount="state.redPacketTotalAmount"
        @close="handleClose"
      ></flowingWater>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { fetchUserRedPacketList } from '@/service'
import { onMounted, ref } from 'vue'
import { formatLargeNumber } from '@/utils'
import flowingWater from './components/flowingWater.vue'
let showCancelPopup = ref(false)
// 响应式状态
const topBarHeight = ref<number>(0)
const topBarTop = ref<number>(0)
// 0未使用  2 已用完 3 已过期 4作废 5 全部
const isFinished = ref<any>(5)
// 获取当天的开始时间和结束时间
const startOfDay = dayjs().startOf('week').format('YYYY-MM-DD')
const endOfDay = dayjs().endOf('week').format('YYYY-MM-DD')

let stateActive = ref(0)
const state = reactive({
  tabs: [
    {
      id: 5,
      name: '全部',
    },
    {
      id: 0,
      name: '待使用',
    },
    // {
    //   id: 1,
    //   name: '有余额',
    // },
    {
      id: 2,
      name: '已用完',
    },
    {
      id: 3,
      name: '已过期',
    },
    {
      id: 4,
      name: '已作废 ',
    },
  ],
  points: 0,
  totalAmount: 0 as any,
  totalRemainingAmount: 0 as any,
  tableData: [] as Api.User.UserRedPacketRecords[],
  redPacketId: null,
  redPacketTotalAmount: 0,
  redPacketRemainingAmount: 0,
})

// 设置 timePickerValue 的默认值
const timePickerValue = ref<any[]>([startOfDay, endOfDay])
const menuButtonInfo = ref({
  height: 32,
  top: 50,
  width: 0,
})

// #ifdef MP-WEIXIN
// 获取屏幕边界到安全区域距离
const menuButton = uni.getMenuButtonBoundingClientRect()
menuButtonInfo.value.height = menuButton.height
menuButtonInfo.value.top = menuButton.top
menuButtonInfo.value.width = menuButton.width
// #endif

// 获取顶部栏高度（示例逻辑，实际根据平台调整）
onMounted(() => {
  // #ifdef MP-WEIXIN || APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  topBarHeight.value = systemInfo.statusBarHeight
  topBarTop.value = systemInfo.statusBarHeight
  // #endif
  // fetchSetting()
  // fetchWalletData()
})

// 定义分组后的数据
const groupedData = ref<{ title: string; records: Api.User.UserRedPacketRecords[] }[]>([])

// 按日期分组数据的函数
const groupDataByDate = (records: any[]) => {
  // 使用对象存储不同日期的记录，避免重复
  const dateMap: { [date: string]: any[] } = {}

  // 遍历原始记录
  records.forEach((record) => {
    // 提取日期部分
    const date = dayjs(record.createTime).format('YYYY-MM-DD')

    // 如果对象中没有该日期的数组，就创建一个新数组
    if (!dateMap[date]) {
      dateMap[date] = []
    }

    // 将记录添加到对应日期的数组中
    dateMap[date].push(record)
  })

  // 将对象转换为所需的数组格式
  groupedData.value = Object.entries(dateMap).map(([date, records]) => ({
    title: date,
    records: records,
  }))

  // 按日期降序排序（最新日期在前）
  groupedData.value.sort((a, b) => dayjs(b.title).diff(dayjs(a.title)))
}
const paging = ref()
const allRecords = ref<any[]>([]) // 新增：用于累加所有分页数据

const queryList = async (pageNo: number, pageSize: number) => {
  try {
    const { data } = await fetchUserRedPacketList({
      pageIndex: pageNo,
      isFinished: isFinished.value,
      pageSize: pageSize,
      startDate: dayjs(timePickerValue.value[0]).startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
      endDate: dayjs(timePickerValue.value[1]).endOf('day').format('YYYY-MM-DDTHH:mm:ss'),
      appId: import.meta.env.VITE_APPID,
    })
    const records = data.redPacket.records
    state.totalAmount = data.totalAmount
    state.totalRemainingAmount = data.totalRemainingAmount

    if (pageNo === 1) {
      allRecords.value = [...records] // 第一页重置
    } else {
      allRecords.value = [...allRecords.value, ...records] // 累加
    }

    groupDataByDate(allRecords.value) // 用累加后的数据分组
    console.log('groupedData', groupedData.value)
    paging.value.complete(records)
  } catch (err) {
    paging.value.complete(false)
  }
}
//关闭流水弹窗
const handleClose = () => {
  showCancelPopup.value = false
  state.redPacketId = null
  state.redPacketTotalAmount = 0
  state.redPacketRemainingAmount = 0
}
//防抖处理
let favTimeout: ReturnType<typeof setTimeout> | null = null
// 标签切换处理
const handleTabChange = (index: number) => {
  if (favTimeout) clearTimeout(favTimeout)
  favTimeout = setTimeout(() => {
    switch (index) {
      case 0:
        isFinished.value = 5
        break
      case 1:
        isFinished.value = 0
        break
      case 2:
        isFinished.value = 2
        break
      case 3:
        isFinished.value = 3
        break
      case 4:
        isFinished.value = 4
        break
    }

    paging.value?.reload()
  }, 300)
}
const handleConfirm = (e: any) => {
  console.log('e', e)
  // 这里可以添加其他处理逻辑
  paging?.value?.reload() // 如果需要重新加载数据
}

const goFlowingWaterDetail = (ele: any) => {
  //已作废不让点明细
  if (ele.isFinished == 4) {
    return false
  }

  state.redPacketId = ele?.redPacketId
  state.redPacketTotalAmount = ele?.amount
  state.redPacketRemainingAmount = ele?.remainingAmount

  showCancelPopup.value = true
  // uni.navigateTo({ url: `/pages-sub/user/redEnvelopes/flowingWater?redPacketId=${redPacketId}` })
}
// 返回
const goback = () => {
  uni.reLaunch({ url: '/pages/my/index' })
}

// 通用跳转方法（保持原逻辑）
const gotoPage = (url: string) => {
  uni.navigateTo({ url })
}
</script>

<style lang="scss" scoped>
.card_one {
  position: relative;
  background-color: rgb(255, 224, 225); // 卡片背景色
  border-radius: 20rpx;
  overflow: hidden;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: -12rpx;
    width: 24rpx;
    height: 24rpx;
    background: #fff6f6;
    border-radius: 50%;
    z-index: 3;
    box-shadow: 0 0 0 0 #fff6f6;
    top: 0;
    transform: translateY(-50%);
  }
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -12rpx;
    width: 24rpx;
    height: 24rpx;
    background: #fff6f6;
    border-radius: 50%;
    z-index: 3;
    box-shadow: 0 0 0 0 #fff6f6;
    transform: translateY(50%);
  }
}
:deep(.wd-tabs) {
  background: none !important;
}
:deep(.wd-tabs__nav) {
  background: none !important;
}
.wallet {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.bgTop {
  background: url('https://file.shanqianqi.com/image/2025/06/20/bac7e1bbd69644e79820e62012d74d9c.png')
    no-repeat;

  background-size: contain;
}
.font-color-ccc {
  color: #cccccc;
}

.icon-jiantou::before {
  font-size: 24rpx;
}

.font-24 {
  font-size: 24rpx;
}

.font-28 {
  font-size: 28rpx;
}

.font-32 {
  font-size: 32rpx;
}

.font-72 {
  font-size: 72rpx;
}

.width-150 {
  width: 150rpx;
  text-align: center;
}

.wallet-content {
  margin-top: -93rpx;
}
.index-head {
  width: 710rpx;
  margin: 0 auto;
  height: 160rpx;
  background: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 25rpx;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.08);
}

.bg-image {
  width: 660rpx;
  height: 340rpx;
  background-image: url('../../../static/card.png');
  background-size: 100% 100%;
  margin: 0 auto;
  margin-top: 50rpx;
  display: flex;
  flex-direction: column;
}

.card-top {
  width: 750rpx;
  height: 256rpx;
  padding-left: 47rpx;
  padding-right: 22rpx;
  box-sizing: border-box;
}

.card-bottom {
  /* width: 660rpx; */
  height: 160rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.index-body {
  width: 706rpx;
  /* height: 1036rpx; */
  background: #ffffff;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 15rpx;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
}

.body-head {
  display: flex;
  justify-content: space-between;
  //flex-direction: column;
}
.body-head .name {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-500;
  font-weight: 500;
}
.body-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 126rpx;
  border-bottom: 1rpx #f2f2f2 solid;
}

.body-item-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333333;
  margin-bottom: 10rpx;
}

.body-item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top_bg {
  width: 750rpx;
  height: 368rpx;
  background: linear-gradient(180deg, #ff774d 0%, #ff422e 100%);
}

.none_line {
  width: 1rpx;
  height: 80rpx;
  background-color: #d9d9d9;
}

.body-item-top-right {
  color: #ff5649;
  margin-bottom: -30rpx;
}
.underline {
  display: inline-block;
  line-height: 2;
  border-bottom: 1px solid #fff;
}
.text-btn {
  width: 189rpx;
  height: 62rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.15);
  border-radius: 30rpx;
  color: #ff5649;
}
.more-btn {
  width: 142rpx;
  height: 52rpx;
  background: #ffffff;
  opacity: 0.98;
  border-radius: 26rpx;
  font-size: 24rpx;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}
/* 红包卡片容器 - 使用更稳定的flex布局 */
.red-envelope-item {
  display: flex;
  width: 100%;
  min-height: 120rpx; /* 确保最小高度 */
  align-items: stretch; /* 确保子元素高度一致 */
  position: relative; /* 为伪元素提供定位上下文 */
  overflow: hidden; /* 防止伪元素溢出 */
}

.red-envelope-left {
  width: 220rpx;
  flex-shrink: 0; /* 防止收缩 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box; /* 确保padding计算正确 */
}

.red-envelope-right {
  flex: 1;
  min-width: 0; /* 防止flex项目溢出 */
  display: flex;
  align-items: center;
  box-sizing: border-box; /* 确保padding计算正确 */
}

.divider-line {
  border-right: 2rpx dashed #ffaab3;
}

/* 右侧容器半圆 - 优化定位方式 */
.zilong {
  position: relative;

  /* 上半圆 */
  &::after {
    content: '';
    position: absolute;
    top: -12rpx; /* 直接使用负值定位，避免transform */
    left: -12rpx;
    width: 24rpx;
    height: 24rpx;
    background: #fff6f6;
    border-radius: 50%;
    z-index: 3;
    /* 添加兼容性处理 */
    box-sizing: border-box;
  }

  /* 下半圆 */
  &::before {
    content: '';
    position: absolute;
    bottom: -12rpx; /* 直接使用负值定位，避免transform */
    left: -12rpx;
    width: 24rpx;
    height: 24rpx;
    background: #fff6f6;
    border-radius: 50%;
    z-index: 3;
    /* 添加兼容性处理 */
    box-sizing: border-box;
  }
}

/* 针对可能的兼容性问题，添加备用方案 */
@supports not (display: flex) {
  .red-envelope-item {
    display: block;
    overflow: hidden;
  }

  .red-envelope-left {
    float: left;
    width: 220rpx;
  }

  .red-envelope-right {
    margin-left: 220rpx;
  }
}
</style>
