<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '发票中心',
  },
}
</route>

<template>
  <view class="flex overflow-hidden pb-2 h-full bg-#f5f5f5">
    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      class="flex-1"
      @query="queryList"
    >
      <template #top>
        <!-- Tab 导航 -->
        <view class="bg-white">
          <wd-tabs
            v-model="activeTab"
            @change="handleTabChange"
            line-width="60rpx"
            line-height="6rpx"
          >
            <block v-for="(ele, index) in state.ordrerTypeTabs" :key="index">
              <wd-tab :title="`${ele.name}`" :name="ele.value"></wd-tab>
            </block>
          </wd-tabs>
        </view>
      </template>

      <!-- 发票列表 -->
      <view class="px-30rpx pt-20rpx">
        <view class="space-y-20rpx">
          <view
            v-for="(invoice, index) in dataList"
            :key="index"
            @click="handleItemClick(invoice)"
            class="bg-white rd-20rpx p-30rpx shadow-sm relative"
          >
            <div class="flex items-center justify-between">
              <!-- 店铺名 -->
              <view class="text-24rpx font-500 text-#333333">
                {{ invoice.shopSupplierName }}
                {{ invoice?.storeName ? `(${invoice?.storeName})` : '' }}
              </view>
              <!-- 状态标识 -->
              <view
                v-if="invoice?.ticketCheckStatus === 10"
                class="px-16rpx py-8rpx rd-20rpx text-24rpx"
                :class="getStatusClass(invoice?.ticketCheckStatus)"
              >
                申请中
              </view>
              <view
                v-if="invoice?.ticketCheckStatus === 20"
                class="px-16rpx py-8rpx rd-20rpx text-24rpx"
                :class="getStatusClass(invoice?.ticketCheckStatus)"
              >
                已开票
              </view>
              <view
                v-else-if="invoice?.ticketCheckStatus === 30"
                class="px-16rpx py-8rpx rd-20rpx text-24rpx"
                :class="getStatusClass(invoice?.ticketCheckStatus)"
              >
                申请失败
              </view>
            </div>

            <!-- 商品信息 -->
            <!-- <view class="mb-30rpx">
            <view class="flex items-center justify-between mb30rpx">
              <view class="text-28rpx text-#333333 ellipsis flex-1 mr-20rpx">
                {{ invoice.productName }}
              </view>
              <view class="text-32rpx font-600 text-#ff4444 flex-shrink-0">
                ¥{{ invoice.totalPrice }}
              </view>
            </view>

            <scroll-view scroll-x class="whitespace-nowrap">
              <view class="flex gap-16rpx pb-10rpx">
                <image
                  v-for="(product, index) in invoice.products"
                  :key="index"
                  :src="product.image"
                  class="w-120rpx h-120rpx rd-12rpx bg-#f5f5f5 flex-shrink-0"
                  mode="aspectFill"
                />
              </view>
            </scroll-view>
          </view> -->

            <!-- 商品列表 -->
            <!-- <view
              v-if="invoice.products.length > 1"
              class="flex justify-between flex-wrap relative pt20rpx pb30rpx"
            >
              <scroll-view :scroll-x="true">
                <view class="pr100rpx flex justify-start items-center">
                  <view class="cover mr10rpx" v-for="(img, num) in invoice.products" :key="num">
                    <image
                      class="w-150rpx h-150rpx rounded-12rpx bg-#f5f5f5 flex-shrink-0"
                      :src="img.image"
                      mode="aspectFit"
                    />
                  </view>
                </view>
              </scroll-view>
              <view class="total-count pl20rpx">
                <view class="left-shadow"></view>
                <view
                  class="text-#FF7D26 flex justify-center items-end text-22rpx pl20rpx box-border"
                >
                  ¥
                  <text class="text-40rpx">{{ invoice.totalPrice }}</text>
                </view>
                <view class="text-gray text-22rpx">共{{ invoice.totalNum }}件</view>
              </view>
            </view> -->

            <!-- 单个商品 -->
            <view class="flex items-center justify-center py20rpx gap20rpx">
              <view class="cover">
                <image
                  :src="invoice.orderPic"
                  class="w-150rpx h-150rpx rounded-8rpx"
                  mode="aspectFit"
                />
              </view>
              <view class="flex-1 text-#333333 text-25rpx px15rpx">
                {{ invoice.productName }}
              </view>
              <view class="">
                <view class="left-shadow"></view>
                <view class="text-#FF7D26">
                  ¥
                  <text class="">{{ invoice.orderMoney }}</text>
                </view>
              </view>
            </view>

            <!-- 个人抬头 -->
            <view class="flex items-center justify-between mb30rpx">
              <view class="text-24rpx text-#999999">
                {{ invoice.headUpType == 10 ? '个人抬头' : '公司抬头' }}
              </view>
              <view class="text-24rpx text-#333333">{{ invoice.ticketHeadUp }}</view>
            </view>

            <!-- 发票类型 -->
            <view class="flex items-center justify-between mb30rpx">
              <view class="text-24rpx text-#999999">发票类型</view>
              <view class="text-24rpx text-#FF7D26">
                {{ invoice.ticketType == 10 ? '普通发票-电子' : '普通发票-电子' }}
              </view>
            </view>

            <!-- 开票日期 -->
            <view class="flex items-center justify-between">
              <view class="text-24rpx text-#999999">
                {{ invoice.ticketType == 20 ? '开票日期' : '申请日期' }}
              </view>
              <view class="text-24rpx text-#333333">
                {{ invoice.ticketType == 20 ? invoice.updateTime : invoice.createTime }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { getOrderTicketsByUserld } from '@/service'
import { useUserStore } from '@/store'
interface ProductItem {
  image: string
  productName: string
  price: string
}

interface InvoiceItem {
  id: string
  shopName: string
  productName: string
  totalPrice: string
  products: ProductItem[]
  invoiceTitle: string
  invoiceType: string
  invoiceDate: string
  totalNum: number
  status: 'applying' | 'completed' | 'failed'
}
const userStore = useUserStore()

const activeTab = ref<number>(0)
let dataList = ref([])
const state = reactive({
  ordrerTypeTabs: [
    {
      value: 0,
      name: '全部',
    },
    {
      value: 10,
      name: '申请中',
    },
    {
      value: 20,
      name: '已开票',
    },
    {
      value: 30,
      name: '申请失败',
    },
  ],
})
const ticketCheckstatus = computed(() => {
  switch (activeTab.value) {
    case 0:
      return ''
    case 10:
      return 10
    case 20:
      return 20
    case 30:
      return 30
    default:
      return ''
  }
})
// 模拟数据
const mockInvoices: InvoiceItem[] = [
  {
    id: '1',
    shopName: '优品数码专营店',
    productName: 'iPhone 15 Pro Max等3件商品',
    totalPrice: '12999.00',
    totalNum: 5,
    products: [
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: 'iPhone 15 Pro Max 256GB',
        price: '9999.00',
      },
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: 'AirPods Pro 2代',
        price: '1899.00',
      },
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: '手机保护壳',
        price: '99.00',
      },
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: 'AirPods Pro 2代',
        price: '1899.00',
      },
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: '手机保护壳',
        price: '99.00',
      },
    ],
    invoiceTitle: '张三',
    invoiceType: '普通发票-电子',
    invoiceDate: '2024-01-15',
    status: 'applying',
  },
  {
    id: '2',
    shopName: '时尚服饰旗舰店',
    productName: '春季新款休闲外套等2件商品',
    totalPrice: '598.00',
    totalNum: 2,
    products: [
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: '春季新款休闲外套',
        price: '299.00',
      },
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: '休闲牛仔裤',
        price: '299.00',
      },
    ],
    invoiceTitle: '李四',
    invoiceType: '电子专用发票',
    invoiceDate: '2024-01-10',
    status: 'completed',
  },
  {
    id: '3',
    shopName: '家居生活馆',
    productName: '北欧简约实木餐桌椅组合',
    totalPrice: '1299.00',
    totalNum: 1,
    products: [
      {
        image: 'https://file.shanqianqi.com/image/2025/06/12/51310fd1dced4ff0af4469367baf5518.jpg',
        productName: '北欧简约实木餐桌椅组合',
        price: '1299.00',
      },
    ],
    invoiceTitle: '王五',
    invoiceType: '普通发票-电子',
    invoiceDate: '2024-01-08',
    status: 'failed',
  },
]

const paging = ref()
const queryList = async (pageNo: number, pageSize: number) => {
  uni.showLoading({ title: '加载中' })
  try {
    let { data } = await getOrderTicketsByUserld({
      appId: import.meta.env.VITE_APP_ID,
      userId: userStore?.userInfo?.userId,
      ticketCheckstatus: ticketCheckstatus.value,
    })

    uni.hideLoading()
    paging.value.complete(data.records)
  } catch (err) {
    uni.hideLoading()
    paging.value.complete(false)
  }
}
const invoiceList = ref<any[]>([])

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case 10:
      return 'bg-#fff7e6 text-#ff8800'
    case 30:
      return 'bg-#fff2f0 text-#ff4444'
    default:
      return ''
  }
}

// 处理 Tab 切换
const handleTabChange = (tabName: string) => {
  // activeTab.value = tabName
  // loadInvoiceList()
  paging?.value?.reload()
}

// 初始化
onMounted(() => {})
const handleItemClick = (invoice: any) => {
  uni.navigateTo({
    url: `/pages-sub/orderInvoice/orderInvoiceDetail?id=${invoice.orderTicketId}`,
  })
}
</script>

<style lang="scss" scoped>
// 自定义样式
.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.total-count {
  background: rgba(255, 255, 255, 0.9);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  padding-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}
</style>
