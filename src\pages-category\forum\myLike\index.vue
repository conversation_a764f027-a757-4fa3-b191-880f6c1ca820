<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的点赞',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f6f6f6 pb-2 box-border h-full">
    <z-paging
      ref="paging"
      empty-view-text="没有点赞哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :paging-style="{ background: '#F7F7FA', 'padding-bottom': `${bottom || 15}px` }"
      fixed
      :show-scrollbar="false"
    >
      <template #top>
        <wd-navbar
          left-arrow
          :bordered="false"
          safeAreaInsetTop
          fixed
          placeholder
          @click-left="handleBack"
        >
          <template #title>
            <view class="">
              <span class="ml-20rpx">我的点赞</span>
            </view>
          </template>
        </wd-navbar>
      </template>
      <view class="pl-25rpx pr-25rpx mt-25rpx">
        <!-- 列表循环 -->
        <div class="overflow-hidden mb-25rpx" v-for="item in dataList" :key="item?.postId">
          <div class="flex bg-#ffffff rounded-15rpx p-30rpx flex-col">
            <div class="flex items-center" @click="goDetail(item.postId)">
              <div class="text-#333333 text-26rpx flex-1 mr-15rpx">
                {{ item?.title }}
              </div>
              <div class="w-120rpx h-120rpx rounded-16rpx mr-15rpx">
                <img :src="item?.images?.[0] || ''" class="w-120rpx h-120rpx rounded-16rpx" />
              </div>
            </div>
            <div class="flex justify-between items-center mt-30rpx">
              <div class="flex items-center">
                <img :src="item?.avatarUrl || ''" class="w-70rpx h-70rpx rounded-50% mr-15rpx" />
                <span class="text-#333333 text-26rpx">{{ item?.nickName }}</span>
              </div>
              <div class="flex">
                <!-- <div class="flex mr-20rpx items-center">
                  <img
                    src="https://file.shanqianqi.com/image/2025/06/24/b17beaacc78944b69ec4f85d17876218.png"
                    alt=""
                    class="w-30rpx h-30rpx mr-10rpx"
                  />
                  <span class="text-#666666 text-22rpx">
                    {{ formatCount(item?.collectCount) }}
                  </span>
                </div> -->
                <!-- <div class="flex mr-20rpx items-center">
                  <img
                    src="https://file.shanqianqi.com/image/2025/06/24/35a0af52d1f444ff9b67413b8e85159f.png"
                    alt=""
                    class="w-30rpx h-30rpx mr-10rpx"
                  />
                  <span class="text-#666666 text-22rpx">
                    {{ formatCount(item?.commentsCount) }}
                  </span>
                </div> -->
                <div class="flex items-center" @click="toggleLike(item)">
                  <img
                    :src="
                      item?.isLike
                        ? 'https://file.shanqianqi.com/image/2025/06/24/37ec1f17f8864efdb3d7108233fa0217.png'
                        : 'https://file.shanqianqi.com/image/2025/06/24/91d17d7940f84f96b477ee4f31c11494.png'
                    "
                    alt=""
                    class="w-30rpx h-30rpx mr-10rpx"
                  />
                  <span class="text-22rpx" :class="item?.isLike ? 'text-#FF7D26' : 'text-#666666'">
                    {{ formatCount(item?.likesCount) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { discourseCancelLikes, discourseAddLikes, fetchUserLikeAndCollect } from '@/service'

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const dataList = ref<Api.Forum.LikeAndCollectionItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    try {
      const { data } = await fetchUserLikeAndCollect({ pageIndex, pageSize, source: 2 })
      paging?.value?.complete(data?.records)
      console.log('data?.records', data?.records)
    } catch (error) {}
  } catch (err) {
    paging?.value?.complete(false)
  }
}

// 格式化数字显示
// const formatCount = (count: number) => {
//   if (count >= 10000) {
//     return (count / 10000).toFixed(1) + 'w'
//   }
//   return count.toString()
// }
const formatCount = (count?: number) => {
  // 处理undefined/null/NaN等情况
  const num = Number(count) || 0
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

// 处理关注/取消关注操作
const toggleLike = async (item: Api.Forum.LikeAndCollectionItem) => {
  try {
    uni.showLoading({ title: '处理中...', mask: true })

    if (item.isLike) {
      await discourseCancelLikes({
        postId: item?.postId,
        source: 2,
      })
      uni.showToast({
        title: '已取消点赞',
        icon: 'none',
      })
    } else {
      await discourseAddLikes({
        postId: item?.postId,
        source: 2,
      })
      uni.showToast({
        title: '点赞成功',
        icon: 'none',
      })
    }

    paging?.value?.reload()
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
//进入详情
const goDetail = (e) => {
  uni.navigateTo({
    url: `/pages-category/forum/details/index?postId=${e}`,
  })
}
</script>

<style lang="scss" scoped>
.flex.items-center {
  &:active {
    opacity: 0.7;
  }
}
</style>
