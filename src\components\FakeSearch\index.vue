<template>
  <div
    @click="handleGoSearch"
    class="flex items-center h-full bg-[rgba(255,255,255,0.5)] of-hidden box-border pl-20rpx pr-1px gap-x-10rpx rd-30rpx"
  >
    <!-- <wd-icon @click="handleSearch" name="search" size="28rpx" color="#ffffff"></wd-icon> -->
    <div class="flex-1 text-28rpx text-#999999 px-10rpx box-border">
      <wd-transition
        v-for="(item, index) in keywords"
        :key="item"
        :show="index === keywordIndex"
        name="fade-down"
        :duration="{ enter: 1000 }"
      >
        {{ item }}
      </wd-transition>
    </div>
    <!-- 非h5环境 -->
    <!--  #ifndef H5-->
    <div v-if="!hideScan" class="px30rpx box-border">
      <div
        @click.stop="handleScan"
        class="i-carbon-scan-alt text-[rgba(255,255,255,0.5)] text-40rpx"
      ></div>
    </div>

    <!-- #endif -->
    <!-- <div
          class="px30rpx py14rpx box-border rd-30rpx bg-#FF7D26 text-24rpx font-500 text-#ffffff"
        >
          搜索
        </div> -->
    <!-- <wd-button
      @click="handleSearch"
      :custom-style="`background: ${themeStore.navColor}`"
      custom-class="!w-108rpx !p-unset !min-w-unset !px30rpx !py14rpx !box-border !rd-30rpx !h-58rpx transition-all-300"
    >
      搜索
    </wd-button> -->
  </div>
</template>

<script lang="ts" setup>
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()

const props = defineProps<{
  handleGoSearch?: () => void
  handleSearch?: () => void
  keywords: string[]
  hideScan?: boolean
}>()

// 搜索关键词数组

// 当前显示的 placeholder
const placeholder = ref(props.keywords[0])

const keywordIndex = ref(0)
let timer: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  timer = setInterval(() => {
    keywordIndex.value = (keywordIndex.value + 1) % props.keywords.length
    placeholder.value = props.keywords[keywordIndex.value]
  }, 3000) // 每1秒切换一次
})

onUnmounted(() => {
  if (timer) clearInterval(timer)
})

//扫码
const handleScan = () => {
  uni.scanCode({
    scanType: ['qrCode'],
    success: (res) => {
      let result = res.result

      if (result?.includes('offline')) {
        const queryString = result.split('?')[1] || ''
        uni.navigateTo({
          url: '/pages-sub/offline/offline-pay' + (queryString ? `?${queryString}` : ''),
        })
      }
      if (result?.includes('addMer')) {
        const queryString = result.split('?')[1] || ''
        uni.navigateTo({
          url: '/pages-my/my-shop/shop-apply/index' + (queryString ? `?${queryString}` : ''),
        })
      }
      if (result?.includes('extract')) {
        const queryString = result.split('?')[1] || ''
        uni.navigateTo({
          url: '/pages-my/store/clerkorder' + (queryString ? `?${queryString}` : ''),
        })
      }
    },
    fail: (err) => {
      console.log(err)
    },
  })
}
</script>

<style lang="scss" scoped>
//
</style>
