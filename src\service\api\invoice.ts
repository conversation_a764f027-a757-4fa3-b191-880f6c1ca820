import { http, httpPostForm } from '@/utils/http'

//发票列表查询
export const getOrderTicketsByUserld = (data: {
  userId: number
  appId: number
  ticketCheckstatus?: string | number
}) => {
  return http.post<any>('/api/front/order/ticket/getOrderTicketsByUserId', data)
}
//发票查询
export const getOrderTicketsById= (data: {
  userId: number
  appId: number
  orderTicketId?: any
}) => {
  return http.get<any>('/api/front/order/ticket/getOrderTicketById', data)
}