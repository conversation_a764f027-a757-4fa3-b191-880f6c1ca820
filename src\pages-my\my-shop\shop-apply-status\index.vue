<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <div
    class="size-full grid grid-cols-1 grid-rows-[auto_1fr] px-20rpx pb60rpx gap-y-40rpx bg-#ffffff box-border"
  >
    <SimpleNavBar title="审核状态" />
    <div class="flex flex-col justify-between items-center">
      <div class="w-full flex-1 flex flex-col items-center justify-center gap-y-60rpx">
        <div class="w-full text-30rpx">
          <wd-steps :active="active" align-center>
            <wd-step
              :icon="!data?.ecNo ? 'close' : ''"
              :title="!data?.ecNo ? '签约失败' : '已签约'"
            />
            <wd-step :icon="data?.status === 2 ? 'close' : ''" title="商户进件" />
            <wd-step
              :icon="
                data?.lakalaLedgerBind?.auditStatus === 2 ||
                data?.lakalaLedgerMer?.auditStatus === 2 ||
                (data?.status === 1 && !data?.lakalaLedgerBind && !data?.lakalaLedgerMer)
                  ? 'close'
                  : ''
              "
              title="分账审核中"
            />
            <!-- <wd-step v-if="data?.lakalaLedgerBind?.auditStatus === 1" title="审核通过" />
            <wd-step
              icon="close"
              v-if="status === 2 || data?.lakalaLedgerBind?.auditStatus === 2"
              title="审核失败"
            /> -->
          </wd-steps>
        </div>
        <div class="text-#ff5704" v-if="data?.lakalaLedgerMer?.auditStatus === 2">
          失败原因:{{ data?.lakalaLedgerMer?.remark }}
        </div>
        <div class="text-#ff5704" v-if="data?.lakalaLedgerBind?.auditStatus === 2">
          失败原因:{{ data?.lakalaLedgerBind?.remark }}
        </div>
        <div class="text-#ff5704" v-if="data?.status === 2">失败原因:{{ data?.content }}</div>
      </div>

      <div class="w-full flex flex-col gap-y-10px">
        <!-- <wd-button v-if="data?.ecNo" @click="handleCheckEcNo" custom-class="!h90rpx" block>
          查看合同
        </wd-button> -->
        <wd-button
          v-if="
            data?.status === 2 ||
            data?.lakalaLedgerBind?.auditStatus === 2 ||
            data?.lakalaLedgerMer?.auditStatus === 2 ||
            (data?.status === 1 && !data?.lakalaLedgerBind && !data?.lakalaLedgerMer)
          "
          @click="handleReApply"
          custom-class="!h90rpx"
          block
        >
          重新进件
        </wd-button>
        <!-- <wd-button
          v-if="
            data?.lakalaLedgerBind?.auditStatus === 2 ||
          "
          @click="handleReApply"
          custom-class="!h90rpx"
          block
        >
          重新申请分账
        </wd-button> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { userShopApplyDetail } from '@/service'
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'

const { data } = useRequest(() => userShopApplyDetail({ userId: '' }), { immediate: true })
// const status = computed(() => data.value?.status)

const active = ref<number>(0)

watch(
  () => data?.value,
  (detail) => {
    if (!detail) return

    // 分账
    if (detail?.status === 1) {
      active.value = 2
    }
    // 进件
    else if (detail?.status === 0 || detail?.status === 2) {
      active.value = 1
    }
    // 电签
    else if (!detail?.ecNo) {
      active.value = 0
    }
  },
  { immediate: true },
)

const handleCheckEcNo = () => {
  uni.navigateTo({ url: `/pages-sub/webview/webview?url=${data?.value?.resultUrl}` })
}

const handleReApply = () => {
  uni.navigateTo({ url: '/pages-my/my-shop/shop-apply/index?type=edit' })
}
</script>
