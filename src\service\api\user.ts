import { http, httpPostForm } from '@/utils/http'

export const getIndex = (data) => {
  return http.post('/api/front/user/index/index', data)
}

export const getCodeType = (data) => {
  return http.get('/api/front/index/loginSetting', data)
}

export const sendCode = (data) => {
  return httpPostForm('/api/front/user/userOpen/sendCode', data)
}

export const getPolicy = () => {
  return http.get<any>('/api/front/user/userapple/policy')
}

export const userPhoneLogin = (data) => {
  return httpPostForm<Api.User.WxLoginInfo>('/api/front/user/userOpen/phoneLogin', data, {})
}

export const userSmsLogin = (data) => {
  return httpPostForm<Api.User.WxLoginInfo>('/api/front/user/userOpen/smsLogin', data, {})
}

export const userResetpassword = (data) => {
  return httpPostForm('/api/front/user/userOpen/resetpassword', data)
}

export const supplierDetail = (data) => {
  return http.post('/api/front/supplier/index/detail', data)
}

export const storeDetail = (data) => {
  return http.get('/api/front/store/store/toEdit', data)
}

export const bindMobile = (data) => {
  return httpPostForm<string>('/api/front/user/userWx/bindMobile', data)
}

export const bindMobile1 = (data) => {
  return httpPostForm('/api/front/user/userWx/bindMobile1', data)
}

export const offlinePay = (data) => {
  return http.post('/api/front/supplier/index/offlinePay', data, {}, {}, true)
}

export const getSchemeUrl = (data) => {
  return httpPostForm('/api/front/user/userWx/getSchemeUrl', data)
}

export const getSession = (data) => {
  return httpPostForm<string>('/api/front/user/userWx/getSession', data)
}

export const userWxLogin = (data: {
  appId: number
  code: string
  refereeId?: string
  invitationId?: string
}) => {
  return http.post<Api.User.WxLoginInfo>('/api/front/user/userWx/login', data)
}

export const userCenterConfig = (data: { appId: number }) => {
  return http.post<Api.User.UserCenterConfig>('/api/front/user/index/center', data)
}
export const userAgent = () => {
  return http.get<Api.User.UserAgent>('/api/front/user/agent/center')
}
export const fetchUserBalanceLogIndex = (params: { appId: number }) => {
  return http.get<Api.User.UserBalanceLogIndex>('/api/front/balance/log/index', params)
}
export const fetchUserBalanceLogList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  type: string
}) => {
  return http.post<Api.User.UserBalanceLogList>('/api/front/balance/log/lists', data)
}

export const fetchUserAgentQrCode = (params: { appId: number; source: string }) => {
  return http.get<string>('/api/front/plus/agent/qrcode/poster', params)
}

export const shareQrocde = () => {
  return http.get('/api/front/points/log/share', {}, {}, true)
}
export const fetchUserBalancePlanIndex = (
  params: { appId: number },
  header: { paySource: string },
) => {
  return http.get<Api.User.UserBalancePlanIndex>('/api/front/balance/plan/index', params, header)
}
export const submitUserBalancePlan = (
  data: {
    appId: number
    planId: number
    userMoney: number
  },
  header,
) => {
  return http.post<Api.User.SubmitUserBalancePlan>(
    '/api/front/balance/plan/submit',
    data,
    data,
    header,
  )
}
//订单核销码
export const fetchUserOrderCode = (params: { orderId: string; source: string }) => {
  return http.get<any>('/api/front/user/order/qrcode', params)
}
export const fetchUserOrderList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  type: string
  orderSource?: any
  orderType?: any
}) => {
  return http.post<Api.User.UserOrderLists>('/api/front/user/order/lists', data, data)
}
export const UserOrderDetail = (params: { orderId: number }) => {
  return http.get<Api.User.UserOrderDetail>('/api/front/user/order/detail', params)
}
//取消订单
export const CancelUserOrder = (data: { orderId: number }) => {
  return http.get('/api/front/user/order/cancel', data, data)
}
// 商圈取消订单(备注原因)
export const CancelReasonUserOrder = (data: { orderId: number; cancelReason: string }) => {
  return http.post('/api/front/order/order/cancelUnderLineOrderById', data)
}
export const RetractUserOrder = (data: { appId: number; orderId: number | string }) => {
  return http.post('/api/front/user/order/retract', data, data)
}
export const ReceiptUserOrder = (data: { appId: number; orderId: number | string }) => {
  return http.post('/api/front/user/order/receipt', data, data)
}
export const DeleteUserOrder = (data: { appId: number; orderId: number | string }) => {
  return http.post('/api/front/user/order/delete', data, data)
}

export const fetchRegionList = () => {
  return http.post<any>('/api/front/settings/getRegion')
}
export const GetUserCommentToOrder = (params: { appId: number; orderId: number | string }) => {
  return http.get<Api.User.UserCommentToOrder[]>('/api/front/user/comment/toOrder', params)
}
export const SubmitUserCommentToOrder = (data: {
  appId: number
  params: any
  orderId: number | string
}) => {
  return http.post('/api/front/user/comment/order', data, data)
}
export const FetchUserCommentToOrderData = (data: {
  appId: number
  pageIndex: number
  pageSize: number
}) => {
  return http.post<any>('/api/front/product/comment/userLists', data)
}

export const fetchUserPointsList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  flowType?: any
  startDate?: any
  endDate?: any
}) => {
  return http.post<any>('/api/front/credits/log/lists', data)
}
export const fetchUserGoodBeanIndex = (data: {
  appId: number
  pageIndex?: number
  pageSize?: number
  flowType?: any
  startDate?: any
  endDate?: any
}) => {
  return http.post<Api.User.UserGoodBeanIndex>('/api/front/points/log/index', data)
}

export const fetchUserRedPacketList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  isFinished: any
  startDate?: any
  endDate?: any
}) => {
  return http.post<Api.User.UserRedPacketList>('/api/front/redPacket/lists', data)
}
export const fetchUserRedPacketLogList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  redPacketId: number | string
}) => {
  return http.post<Api.User.UserRedPacketLogList>('/api/front/redPacketLog/lists', data)
}
//订单红包释放查询
export const fetchUserOrderRedPacketList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  orderId: string
  userId: number
}) => {
  return http.post<any>('/api/front/user/userRedPacket/index', data)
}
export const fetchUserCartList = (data: { appId: number; token: string }) => {
  return httpPostForm<Api.Home.CartData>('/api/front/order/cart/lists', data)
}

export const addCart = (data: {
  appId: number
  token: string
  productId: number
  specSkuId: string
  totalNum: number
}) => {
  return httpPostForm<string>('/api/front/order/cart/add', data)
}

export const removeCart = (data: {
  appId: number
  token: string
  productId: number
  specSkuId: string
}) => {
  return httpPostForm<string>('/api/front/order/cart/sub', data)
}

export const deleteCart = (data: {
  appId: number
  token: string
  cartIds: string[] | number[]
}) => {
  return httpPostForm<string>('/api/front/order/cart/delete', data)
}

export const cartPreBuy = (data: {
  appId: number
  cartIds: string
  couponId?: number
  delivery: number
  isUsePoints: number
  paySource: string
  roomId?: number
  storeId: number
}) => {
  return http.post<Api.Order.PreBuyData>('/api/front/order/order/toCart', data)
}

export const userSetting = (params: { appId: number; token: string }) => {
  return http.get<{ userInfo: Api.User.UserInfo }>('/api/front/user/index/setting', params)
}

export const userUpdate = (data: Api.User.UserInfo & { token: string }) => {
  return httpPostForm<null>('/api/front/user/user/update', data)
}

export const setUserPaymentPassword = (data: { paymentPassword: string }) => {
  return http.post<null>('/api/front/user/user/paymentPassword', {}, data)
}

export const checkUserPaymentPassword = (data: { password: string }) => {
  return http.post<string>('/api/front/user/user/verifyPassword', {}, data)
}

export const updateMobile = (data: {
  token: string
  mobile: string | number
  code?: string
  appId: number
}) => {
  return httpPostForm<null>('/api/front/user/userOpen/changeMobile', data)
}

export const fetchUserAddressList = (params: { token: string; appId: number }) => {
  return http.get<Api.User.UserAddressData>('/api/front/user/address/lists', params)
}

export const setUserAddressDefault = (data: {
  token: string
  appId: number
  addressId: number
}) => {
  return httpPostForm<Api.User.UserAddressData>('/api/front/user/address/setDefault', data)
}

export const userAddressDelete = (data: { token: string; appId: number; addressId: number }) => {
  return http.get<Api.User.UserAddressData>('/api/front/user/address/delete', data)
}

export const fetchRegions = (data: { token: string; appId: number }) => {
  return httpPostForm<unknown[]>('/api/front/settings/getRegion', data)
}

export const addressAdd = (data: any) => {
  return http.post<unknown[]>('/api/front/user/address/add', data)
}

export const userAddressDetail = (data: { token: string; appId: number; addressId: number }) => {
  return http.get<Api.User.UserAddressData>('/api/front/user/address/detail', data)
}

export const userAddressEdit = (data: { token: string; appId: number; addressId: number }) => {
  return http.post<null>('/api/front/user/address/edit', data)
}

export const userFavoriteList = (data: {
  appId: number
  pageIndex: number
  pageSize: number
  type: number
}) => {
  return http.post<Api.User.CollectionData>('/api/front/user/favorite/list', data)
}
export const userCancelFav = (data: { appId: number; token: string; productId: number }) => {
  return httpPostForm<Api.User.CollectionData>('/api/front/user/favorite/cancelFav', data)
}

export const userCashList = (data: { pageIndex: number; pageSize: number; status: number }) => {
  return http.post<Api.User.CashListData>('/api/front/plus/agent/cash/lists', data)
}

export const userCashReceipt = (data: { id: number }) => {
  return httpPostForm('/api/front/plus/agent/cash/receipt', data)
}

export const getWxSignPackage = (data: { url: string; paySource: string }) => {
  return http.post<{
    signPackage: { appId: string; timestamp: number; nonceStr: string; signature: string }
  }>('/api/front/index/getSignPackage', data)
}

export const userOrderList = (data: {
  pageIndex: number
  pageSize: number
  flowType: string | number
}) => {
  return http.post<Api.User.orderCapitalData>('/api/front/plus/agent/order/capital', data)
}

export const userTeamList = (data: {
  pageIndex: number
  pageSize: number
  level: number
  lastMonth: boolean
  startDate: string
  endDate: string
}) => {
  return http.post<Api.User.MyTeamUserData>('/api/front/plus/agent/team/lists', data)
}

export const userTeamSupplierLists = (data: {
  pageIndex: number
  pageSize: number
  level: number
  lastMonth: boolean
  startDate: string
  endDate: string
}) => {
  return http.post<Api.User.MyTeamShopData>('/api/front/plus/agent/team/supplierLists', data)
}

export const getTeamPerformance = (data: {
  pageIndex: number
  pageSize: number
  level: number
  lastMonth: boolean
  startDate: string
  endDate: string
}) => {
  return http.post<Api.User.TeamPerformanceData>(
    '/api/front/plus/agent/team/getTeamPerformance',
    data,
  )
}

export const fetchUserRefundToApply = (params: {
  appId: number
  orderProductId: number
  platform: string
}) => {
  return http.get<any>('/api/front/user/refund/toApply', params)
}
export const fetchUserRefunDetail = (params: {
  appId: number
  orderRefundId: any
  productId: any
  orderId: any
}) => {
  return http.get<Api.refund.RerefundDetail>('/api/front/user/refund/detail', params)
}
// 申请平台介入
export const refundPlateApply = (data: { appId: number; orderRefundId: number }) => {
  return http.post<any>('/api/front/user/refund/plateApply', data, data)
}
// 售后退货
export const refundDelivery = (data: {
  appId: number
  orderRefundId: any
  expressId: any
  expressNo: string
}) => {
  return http.post<any>('/api/front/user/refund/delivery', data, data)
}
export const fetchRefundList = (data: {
  appId: number
  state: number
  pageIndex: number
  pageSize: number
}) => {
  return http.post<any>('/api/front/user/refund/lists', data)
}
export const applyRefund = (data: {
  appId: number
  type: any
  orderProductId: any
  images: any[]
  content: string
}) => {
  return http.post('/api/front/user/refund/apply', data)
}
export const applyUserCash = (params: { platform: string }) => {
  return http.get<Api.User.ApplyData>('/api/front/user/agent/cash', params)
}

export const fetchLoginSetting = (params: { appId: string }) => {
  return http.get<Api.User.LoginSettingData>('/api/front/index/loginSetting', params)
}

export const userApplySubmit = (data: {
  alipayName: string
  alipayAccount: string
  bankName: string
  bankAccount: string
  bankCard: string
  payType: number
  money: string | number
  fees: number
  amountReceived: string
}) => {
  return http.post<Api.User.LoginSettingData>('/api/front/plus/agent/cash/submit', data)
}

export const userCashFees = (params: { money: string }) => {
  return http.get<{ amountReceived: string; fees: number }>(
    '/api/front/plus/agent/cash/amountReceived',
    params,
  )
}

export const userIndex = () => {
  return http.post<Api.User.IndexData>('/api/front/user/index/index')
}

export const userSupplierApply = (data: {
  storeName: string
  userName: string
  mobile: string
  password: string
  code?: string
  businessId?: number
  categoryId?: string
  categoryPid?: string
}) => {
  return http.post('/api/front/supplier/apply/index', data, {}, {}, true)
}

export const userSupplierCategory = () => {
  return http.post<Api.User.ShopCategoryItem[]>('/api/front/supplier/category/index')
}

export const getBankRegistration = (parentCode: number) => {
  return httpPostForm<unknown[]>('/api/front/lkl/getBankRegistration', { parentCode })
}
export const getBankList = (data: { areaCode: number; bankName?: string }) => {
  return httpPostForm<unknown[]>('/api/front/lkl/getBankList', data)
}
export const getRegistration = (parentCode: number) => {
  return httpPostForm<unknown[]>('/api/front/lkl/getRegistration', { parentCode })
}

export const addProductSetting = (data: { supplierId: string }) => {
  return http.get<Api.User.AddProductSetting>('/api/front/supplier/product/add', data)
}

export const addProduct = (data: Api.User.AddProductSettingBody) => {
  return http.post('/api/front/supplier/product/add', data)
}

export const userApplyDetail = () => {
  return httpPostForm<{
    categoryId: number
    content: unknown
    depositMoney: string
    status: number
    storeName: string
    supplierApplyId: number
    supplierIsDelete: unknown
    userId: number
    mobile: string
    userName: string
    password: string
    resultUrl: string
    ecNo: string
    merChantNo: string
    contractId: number
    shopSupplierId: number
    lakalaLedgerBind: Record<string, any>
    qrCode: string
    lakalaLedgerMer: Record<string, any>
  }>('/api/front/supplier/apply/detail')
}

export const userShopApplyDetail = (data: { userId: string }) => {
  return httpPostForm<Api.ShopApply.ShopApplyDetail>('/api/front/supplier/apply/detailApply', data)
}

export const getImgById = (data: { imageId: number }) => {
  return httpPostForm<string>('/api/front/file/upload/getImageUrl', data)
}

export const userTradeData = () => {
  return httpPostForm<Api.User.UserTradeData>('/api/front/supplier/index/tradeData')
}
//获取商户商品订单数据
export const fetchUserStoreOrderList = (data: {
  paySource?: string
  shopSupplierId: any
  type: string
  appId: number
  pageIndex: number
  pageSize: number
  orderType: number
}) => {
  return http.post<any>('/api/front/supplier/order/index', data)
}
// 获取商户订单(商圈/商城/全部)
export const getOrderListBysupplierldList = (data: {
  paySource?: string
  shopSupplierId: any
  type: string
  appId: number
  pageIndex: number
  pageSize: number
  orderType: number
}) => {
  return http.post<any>('/api/front/order/order/getOrderListBySupplierId', data)
}
//商户取消订单
export const cancelUserStoreOrder = (data: {
  orderNo: string
  appId: number
  cancelRemark?: string
}) => {
  return http.post<any>('/api/front/supplier/order/orderCancel', data)
}
export const userStoreData = (data: { pageIndex: number }) => {
  return http.post<Api.User.UserStoreData>('/api/front/supplier/index/storedata', data)
}

export const fetchUserStoreProduct = (data: {
  pageIndex: number
  pageSize: number
  shopSupplierId: string
  type: string
}) => {
  return http.post<Api.User.UserStoreProductData>('/api/front/supplier/product/index', data)
}

export const productModify = (data: {
  productStatus: number
  productId: number
  shopSupplierId: string
}) => {
  return http.post('/api/front/supplier/product/modify', data)
}

export const fetchTodayInviteRanking = (data: {
  limit: number
  startDate: string
  endDate: string
}) => {
  return http.post<Api.User.InviteItem[]>('/api/front/user/ranking/getTodayInviteRanking', data)
}

export const getAllHotProductLabelsByType = (data: { appId: any; type: number }) => {
  return http.get<Api.Search.HotSearchItem[]>(
    '/api/front/product/hotLabel/getAllHotProductLabelsByType',
    data,
  )
}

export const picRecognize = (data: {
  url: string
  type: 'IdCard' | 'BankCard' | 'BusinessLicense'
}) => {
  return httpPostForm<Api.ShopApply.PicRec>('/api/front/ali/ocr/recognize', data, {}, {}, true)
}

export const cardBin = (data: { cardNo: string }) => {
  return httpPostForm<string>('/api/front/supplier/apply/cardBin', data)
}

export const businessPoster = (data: {
  source: string
  shopSupplierId: number
  storeId: number
}) => {
  return http.get<string>('/api/front/plus/agent/qrcode/businessPoster', data)
}

export const getLikeProductLabelList = (data: { labelName: string }) => {
  return http.post<Api.Search.LinkSearchItem[]>(
    '/api/front/product/product/getLikeProductLabelList',
    data,
  )
}

//获取论坛列表
export const discourseLists = (data: {
  pageIndex: number
  pageSize: number
  follow: boolean
  mainCategoryId: number
}) => {
  return http.post<any>('/api/front/discourse/discourse/lists', data)
}
//发布帖子
export const discourseAddDiscourse = (data: {
  title: string
  imageList: any[]
  content: string
  mainCategoryId: number
}) => {
  return http.post<any>('/api/front/discourse/discourse/addDiscourse', data)
}
//论坛分类列表
export const categoryList = (data: {}) => {
  return http.get('/api/front/discourse/category/list', data)
}
//我的关注
export const discourseFollower = (data: { userId: any }) => {
  return http.get('/api/front/discourse/discourse/follower', data)
}
//取消关注
export const discourseCancelFollower = (data: { userId: any }) => {
  return http.get('/api/front/discourse/discourse/cancelFollower', data)
}
//帖子详情
export const discourseDetail = (data: { postId: string }) => {
  return http.get<Api.Forum.DiscourseDetail>('/api/front/discourse/discourse/detail', data)
}
//评论列表
export const discourseCommentList = (data: {
  postId: string
  pageIndex: number
  pageSize: number
}) => {
  return http.post<Api.Forum.ReplyData>('/api/front/discourse/discourse/commentList', data)
}
//添加评论
export const discourseAddComment = (data: {
  postId: string
  content?: string
  parentCommentId: string | number
  city: string | null
  image?: string
}) => {
  return http.post<any>('/api/front/discourse/discourse/addComment', data)
}

export const fetchUserAIChatList = (data: {
  userId: number
  pageIndex: number
  pageSize: number
}) => {
  return http.post<Api.AI.ChatListData>('/api/front/AI/chat/getAIChatByUserId', data)
}

export const fetchUserAIChatDetail = (data: { aiChatId: string | number }) => {
  return http.get<Api.AI.ChatHistoryItem>('/api/front/AI/chat/getAiChatDetail', data)
}

export const addAiChat = (data: {
  userId: number
  audio_txt: string
  session_id: number
  model: string
  aiChatId: string | number
}) => {
  return http.post<Api.AI.ChatListItem>('/api/front/AI/chat/addAiChat', data)
}
