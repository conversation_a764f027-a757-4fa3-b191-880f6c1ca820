<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="size-full grid grid-rows-[1fr] gap-y-10px px11px box-border">
    <z-paging
      ref="paging"
      safe-area-inset-bottom
      v-model="dataList"
      empty-view-text="没有门店哦~"
      :default-page-size="9999"
      fixed
      :loading-more-enabled="false"
      @query="queryList"
    >
      <template #top>
        <SimpleNavBar :bordered="false" title="门店管理" />
      </template>
      <div class="w-full box-border grid grid-cols-1 gap-y-10px pt-10px">
        <div
          v-for="item in dataList"
          :key="item?.storeId"
          class="bg-#ffffff w-full p-20rpx box-border rd-10px flex flex-col gap-y-10px"
        >
          <div class="flex items-center gap-x-5px">
            <div class="i-carbon-store" />
            {{ item?.storeName }}
          </div>
          <div class="flex items-center self-end gap-x-10px">
            <wd-button @click="() => handleSettingId(item?.storeId)" v-if="!item?.announcerId">
              设置语音播报器设备号
            </wd-button>
            <wd-button v-else @click="() => handleTesting(item?.storeId)">测试语音播报器</wd-button>
            <wd-button v-if="storeisRecycle === 0" @click="handleBeforeEnter(item)">
              门店收款码
            </wd-button>
          </div>
        </div>
      </div>
      <wd-message-box selector="wd-message-box-slot">
        <div class="flex flex-col gap-y-60rpx">
          <wd-radio-group shape="dot" inline v-model="voiceType">
            <wd-radio value="支付宝">支付宝</wd-radio>
            <wd-radio value="微信">微信</wd-radio>
            <!-- <wd-radio :value="2">
            <div class="flex items-start flex-col">
              测试
              <wd-picker :columns="['支付宝', '微信']" v-model="payInfo" />

              到账
              <div class="flex items-start">
                <wd-input v-model="inputMoney" no-border placeholder="请输入金额" type="digit" />
                元
              </div>
              用户使用
              <div class="flex items-start">
                <wd-input v-model="discountMoney" no-border placeholder="请输入金额" type="digit" />
                元
              </div>
              优惠
            </div>
          </wd-radio> -->
          </wd-radio-group>
          <div class="flex items-center gap-x-16rpx">
            <span class="w-120rpx text-right">到账</span>
            <div class="flex items-start w180rpx">
              <wd-input v-model="inputMoney" placeholder="请输入金额" type="digit" />
            </div>
            元
          </div>
          <div class="flex items-center gap-x-16rpx">
            <span class="w-120rpx">用户使用</span>
            <div class="flex items-start w180rpx">
              <wd-input v-model="discountMoney" placeholder="请输入金额" type="digit" />
            </div>
            <span>元优惠</span>
          </div>
        </div>
      </wd-message-box>
      <wd-popup
        v-model="payCodePop"
        custom-style="border-radius:32rpx;width:80%"
        @close="payCodePop = !payCodePop"
      >
        <div class="p-20rpx flex flex-col items-center gap-y-20rpx">
          <wd-img :src="payCodeUrl" width="100%" enable-preview mode="widthFix"></wd-img>
        </div>
      </wd-popup>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import {
  fetchSupplierStoreList,
  settingStoreDeviceId,
  playDeviceVoice,
  businessPoster,
} from '@/service'
import { useMessage } from 'wot-design-uni'
import { getPlatform } from '@/utils'

const message = useMessage()

const messageTest = useMessage('wd-message-box-slot')

const paging = ref()
const dataList = ref<Api.Supplier.storeListItem[]>([])

const storeisRecycle = ref(0)

const queryList = async () => {
  try {
    const { data } = await fetchSupplierStoreList()

    storeisRecycle.value = data?.isRecycle

    paging.value.completeByNoMore(data?.storelist)
  } catch (err) {
    paging.value.complete(false)
  }
}

const deviceId = ref('')
const handleSettingId = (id: number) => {
  message
    .prompt({
      title: '语音播报器设备号',
      inputValue: deviceId.value,
      inputPlaceholder: '请输入语音播报器设备号',
      inputPattern: /^[A-Za-z0-9]{13,}$/,
      inputError: '设备号输入不正确',
    })
    .then(async (res) => {
      await settingStoreDeviceId({
        announcerId: res.value,
        storeId: id,
      })
      paging.value?.reload()
    })
    .catch((error) => {
      console.log(error)
    })
}

const voiceType = ref('支付宝')

const inputMoney = ref(10)

const discountMoney = ref(2)

const handleTesting = (id: number) => {
  messageTest
    .confirm({
      title: '选择测试类型',
    })
    .then(async () => {
      const testString = `测试${voiceType.value}到账${inputMoney.value}元${discountMoney.value ? `用户使用${discountMoney.value}元优惠` : ''}`
      await playDeviceVoice({ storeId: id, voice: testString })
    })
    .catch((error) => {
      console.log(error)
    })
}

const payCodePop = ref(false)

const payCodeUrl = ref('')

const handleBeforeEnter = async (item: Api.Supplier.storeListItem) => {
  try {
    const { data } = await businessPoster({
      source: getPlatform(),
      shopSupplierId: item?.shopSupplierId,
      storeId: item?.storeId,
    })

    payCodeUrl.value = data

    payCodePop.value = true
  } catch (error) {
    uni.showToast({ title: '下载二维码失败', icon: 'none' })
  }
}
</script>

<style lang="scss" scoped>
:deep(.wd-picker__cell) {
  padding: unset !important;
}
</style>
