<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的收藏',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f6f6f6 pb-2 box-border h-full">
    <z-paging
      ref="paging"
      empty-view-text="没有收藏哦~"
      v-model="dataList"
      :default-page-size="10"
      @query="queryList"
      :paging-style="{ background: '#F7F7FA', 'padding-bottom': `${bottom || 15}px` }"
      fixed
      :show-scrollbar="false"
    >
      <template #top>
        <wd-navbar
          left-arrow
          :bordered="false"
          safeAreaInsetTop
          fixed
          placeholder
          @click-left="handleBack"
        >
          <template #title>
            <view class="">
              <span class="ml-20rpx">我的收藏</span>
            </view>
          </template>
        </wd-navbar>
      </template>
      <view class="pl-25rpx pr-25rpx mt-25rpx">
        <!-- 列表循环 -->
        <div class="mb-25rpx" v-for="(item, index) in dataList" :key="item?.postId">
          <wd-swipe-action>
            <div class="flex bg-#ffffff rounded-15rpx p-30rpx flex-col">
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <img :src="item?.avatarUrl || ''" class="w-70rpx h-70rpx rounded-50% mr-15rpx" />
                  <span class="text-#333333 text-26rpx">{{ item?.nickName }}</span>
                </div>
                <div
                  v-if="item && item.userId !== userStore?.userInfo?.userId"
                  class="li-r h-50rpx rounded-80rpx w-110rpx text-center flex items-center justify-center"
                  :class="item.isFollow ? 'bg-#F2F2F2' : 'bg-#ff7d2633'"
                  @click.stop="handleFollow(item)"
                >
                  <span
                    class="text-22rpx"
                    :class="item?.isFollow ? 'text-#999999' : 'text-#FF7D26'"
                  >
                    {{ item.isFollow ? '已关注' : '关注' }}
                  </span>
                </div>
              </div>
              <div class="flex mt-30rpx items-center" @click="goDetail(item.postId)">
                <div v-if="item?.images?.length" class="w-120rpx h-120rpx rounded-16rpx mr-15rpx">
                  <img
                    :src="item?.images?.[0] || ''"
                    class="w-120rpx h-120rpx rounded-16rpx mr-15rpx"
                  />
                </div>
                <div class="text-#333333 text-26rpx flex-1">
                  {{ item?.title }}
                </div>
              </div>
            </div>
            <!-- 滑动删除按钮 -->
            <template #right>
              <view
                class="action flex justify-center items-center h-100% ml-25rpx w-120rpx"
                @click="deleteItem(item)"
              >
                <img
                  src="https://file.shanqianqi.com/image/2025/06/24/a76f8d400bd34ba9a6e4614ac798d6ee.png"
                  alt=""
                  class="w-40rpx h-40rpx"
                />
              </view>
            </template>
          </wd-swipe-action>
        </div>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import {
  discourseCancelLikes,
  discourseFollower,
  discourseCancelFollower,
  fetchUserLikeAndCollect,
} from '@/service'
import { useUserStore } from '@/store'

const userStore = useUserStore()

const {
  safeAreaInsets: { bottom },
} = uni.getSystemInfoSync()

const paging = ref()
const dataList = ref<Api.Forum.LikeAndCollectionItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  uni.showLoading({ title: '加载中' })
  try {
    const { data } = await fetchUserLikeAndCollect({ pageIndex, pageSize, source: 1 })
    uni.hideLoading()
    paging?.value?.complete(data?.records)
  } catch (err) {
    uni.hideLoading()
    paging?.value?.complete(false)
  }
}

// 处理关注/取消关注操作
const handleFollow = async (item) => {
  console.log('item', item)

  try {
    uni.showLoading({ title: '处理中...', mask: true })

    if (item.isFollow) {
      // 取消关注
      await discourseCancelFollower({
        userId: item.userId,
      })
      uni.showToast({
        title: '已取消关注',
        icon: 'none',
      })
    } else {
      // 添加关注
      await discourseFollower({
        userId: item.userId,
      })
      uni.showToast({
        title: '关注成功',
        icon: 'none',
      })
    }

    paging?.value?.reload()
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 删除项目
const deleteItem = (item: Api.Forum.LikeAndCollectionItem) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除这条收藏吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await discourseCancelLikes({ postId: item?.postId, source: 1 })

          uni.showToast({ title: '删除成功', icon: 'success' })

          paging.value?.reload()
        } catch (error) {}
      }
    },
  })
}
// 返回按钮点击事件
const handleBack = () => {
  uni.navigateBack({
    delta: 1, // 返回的页面数
  })
}
//进入详情
const goDetail = (e) => {
  uni.navigateTo({
    url: `/pages-category/forum/details/index?postId=${e}`,
  })
}
</script>

<style lang="scss" scoped>
.action {
  border-radius: 0 15rpx 15rpx 0;
}

.li-r {
  transition: all 0.3s;

  &:active {
    opacity: 0.7;
  }
}
</style>
