<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '开票详情',
  },
}
</route>

<template>
  <view class="min-h-screen bg-#f5f5f5 flex flex-col">
    <!-- 申请中状态 - 发票背景图 -->
    <view
      v-if="invoiceDetail.ticketCheckStatus == 10"
      class="flex-1 px-30rpx pt-40rpx pb-40rpx flex justify-center"
    >
      <view
        class="invoice-bg w-700rpx h-620rpx relative rd-20rpx overflow-hidden"
        :style="{
          backgroundImage: `url('https://file.shanqianqi.com/image/2025/06/30/6a6970f238614865aef84415b6095970.png')`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <!-- 发票信息内容 -->
        <view class="absolute inset-0 px-60rpx py-80rpx flex flex-col justify-center space-y-35rpx">
          <!-- 发票抬头 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-400 w-140rpx flex-shrink-0">发票抬头：</view>
            <view class="text-24rpx text-#999999 font-400 flex-1">
              {{ invoiceDetail.ticketHeadUp }}
            </view>
          </view>

          <!-- 税号 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-400 w-140rpx flex-shrink-0">税号：</view>
            <view class="text-24rpx text-#999999 font-400 flex-1">
              {{
                (invoiceDetail.ticketType && !invoiceDetail.taxNumber) ||
                invoiceDetail.taxNumber == ''
                  ? '-'
                  : invoiceDetail.taxNumber
              }}
            </view>
          </view>

          <!-- 发票金额 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-400 w-140rpx flex-shrink-0">发票金额：</view>
            <view class="text-24rpx text-#999999 font-400 flex-1">
              ¥{{ invoiceDetail.orderMoney }}
            </view>
          </view>

          <!-- 发票内容 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-400 w-140rpx flex-shrink-0 leading-relaxed">
              发票内容：
            </view>
            <view class="text-24rpx text-#999999 font-400 flex-1 leading-relaxed">明细</view>
          </view>

          <!-- 申请日期 -->
          <view class="flex items-center gap50rpx">
            <view class="text-28rpx text-#333333 font-400 w-140rpx flex-shrink-0">申请日期：</view>
            <view class="text-24rpx text-#999999 font-400 flex-1">
              {{ invoiceDetail.createTime }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 已开票/开票失败状态 - 白底卡片 -->
    <view
      v-else-if="invoiceDetail.ticketCheckStatus == 20 || 30"
      class="flex-1 px-30rpx pt-40rpx pb-40rpx"
      @click="invoiceDetail.ticketCheckStatus == 20 ? goInvoicePreview(invoiceDetail) : null"
    >
      <view class="bg-white rd-20rpx p-40rpx shadow-sm mb-30rpx">
        <view class="flex items-center justify-between gap-20rpx">
          <!-- 发票抬头 -->
          <view class="text-28rpx text-#333333 font-400">
            {{ invoiceDetail.ticketHeadUp }}
          </view>
          <wd-tag round bg-color="#F7F7FA">
            {{ invoiceDetail.ticketType == 10 ? '普通发票-电子' : '普通发票-电子' }}
          </wd-tag>
        </view>

        <!-- 价格、标签和日期 -->
        <view class="flex items-center justify-between">
          <!-- 左侧：价格和标签 -->
          <view class="text-48rpx text-#ff7d26 font-bold">
            <span class="text-24rpx font-medium">¥</span>
            <span class="text-40rpx font-bold">
              {{ invoiceDetail.orderMoney?.split('.')?.[0] }}
            </span>
            <span class="text-28.8rpx font-bold">
              .{{ invoiceDetail.orderMoney?.split('.')?.[1] }}
            </span>
          </view>

          <!-- 右侧：日期 -->
          <view class="text-24rpx text-#999999 flex-shrink-0">
            {{ invoiceDetail.createTime }}
          </view>
        </view>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="px-30rpx pb-40rpx pt-20rpx bg-#F6F6F6">
      <!-- 发票中心标题 -->
      <view class="text-center mb-14rpx" @click="handleInvoiceCenter">
        <view class="invoice-center-title">发票中心</view>
      </view>

      <!-- 修改申请按钮 -->
      <wd-button
        @click="handleModifyApply"
        custom-class="!bg-#ff7d26 !h-88rpx !text-32rpx !font-600"
        block
        round
      >
        修改申请
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getOrderTicketsById } from '@/service'
import { useUserStore } from '@/store'
const userStore = useUserStore()

const orderTicketId = ref<any>(null)

const invoiceDetail = ref<any>({
  ticketHeadUp: '',
  taxNumber: '',
  orderMoney: '',
  createTime: '',
  ticketType: 10,
})
const handleInvoiceCenter = () => {
  uni.navigateTo({
    url: '/pages-sub/orderInvoice/invoiceCenter',
  })
}
onLoad((options) => {
  if (options.id) {
    orderTicketId.value = options.id
  }
  fetchData()
})
// 修改申请
const handleModifyApply = () => {
  uni.showToast({
    title: '跳转到修改页面',
    icon: 'none',
  })
  // 这里可以跳转到修改申请页面
  // uni.navigateTo({
  //   url: '/pages-sub/orderInvoice/modifyInvoice'
  // })
}
//跳转发票预览
const goInvoicePreview = (invoice: any) => {
  uni.navigateTo({
    url: `/pages-sub/orderInvoice/invoicePreview?id=${invoice.orderTicketId}`,
  })
}
const fetchData = async () => {
  uni.showLoading({ title: '加载中' })
  try {
    let { data } = await getOrderTicketsById({
      appId: import.meta.env.VITE_APP_ID,
      userId: userStore?.userInfo?.userId,
      orderTicketId: Number(orderTicketId.value),
    })
    invoiceDetail.value = data
    if (invoiceDetail.value.ticketCheckStatus == 10) {
      uni.setNavigationBarTitle({
        title: '申请中', // 动态标题内容
      })
    } else if (invoiceDetail.value.ticketCheckStatus == 20) {
      uni.setNavigationBarTitle({
        title: '已开票', // 动态标题内容
      })
    } else if (invoiceDetail.value.ticketCheckStatus == 30) {
      uni.setNavigationBarTitle({
        title: '未通过', // 动态标题内容
      })
    }

    uni.hideLoading()
  } catch (err) {
    uni.hideLoading()
  }
}
// 页面加载时获取发票详情
onMounted(() => {})
</script>

<style lang="scss" scoped>
.invoice-bg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

// 确保文字在背景图上清晰可见
.invoice-bg view {
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

// 发票中心标题样式
.invoice-center-title {
  color: #ff2736;
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 32rpx;
  text-decoration: underline;
  text-decoration-color: #ff2736;
  text-underline-offset: 8rpx;
}

// 发票类型标签样式
.invoice-type-label {
  color: #999999;
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 24rpx;
  line-height: 34rpx;
}
</style>
