<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view
    :style="{ paddingBottom: `${safeAreaInsets?.bottom || 15}px` }"
    class="size-full flex flex-col items-center px-30rpx box-border"
  >
    <SimpleNavBar :delta="2" title="收银台" />
    <div class="flex-1 w-full pt100rpx box-border flex flex-col items-center">
      <div class="text-32rpx text-#333333 mb20rpx">待支付</div>

      <view
        class="text-center text-32rpx"
        :class="countdown === '订单已超时' ? 'text-red-500' : 'text-#999999'"
      >
        订单剩余时间：{{ countdown }}
      </view>

      <div class="text-60rpx font-500 mb100rpx flex items-center flex-col gap-y-10rpx">
        <span class="text-44rpx text-#333333">￥{{ payPrice }}</span>
        <span class="text-28rpx text-#999999">
          (抵扣 ￥{{ (Number(payPrice) - realPayAmount).toFixed(2) }})
        </span>
        <div class="flex items-center font-bold text-26px text-#FF7D26 h-136rpx">
          实付款
          <wd-count-to
            prefix="￥"
            :startVal="0"
            :duration="500"
            :decimals="2"
            separator=""
            :endVal="realPayAmount"
            :fontSize="32"
            color="#FF7D26"
          ></wd-count-to>
        </div>
        <!-- ￥{{ payPrice }} -->
        <!-- 总额 ￥{{ payPrice }} - 抵扣 ￥{{ (Number(payPrice) - realPayAmount).toFixed(2) }} = -->
      </div>

      <div class="w-full bg-#ffffff rd-10px px-20px box-border">
        <!-- 善豆 -->
        <div
          v-if="checkedPay.includes(50) && moneyInfo?.points !== 0 && !storeId"
          class="flex items-center justify-between"
        >
          <div class="flex flex-col">
            <div class="w-full py30rpx box-border min-h-50rpx flex items-center gap-x-5px">
              <!-- <div class="i-hugeicons-coffee-beans text-#FF7D26"></div> -->
              <wd-img
                src="https://file.shanqianqi.com/image/2025/06/16/d51939ead57b496f8d5497ab64b1b025.png"
                width="52rpx"
                height="52rpx"
                mode="aspectFill"
              />
              <span class="text-26rpx">善豆抵扣：(剩余：{{ moneyInfo?.points ?? '0' }} )</span>
            </div>
            <div
              v-if="moneyInfo?.totalPointsMoney"
              class="w-full box-border min-h-50rpx flex items-center gap-x-5px"
            >
              <div class="i-hugeicons-information-circle text-#FF7D26"></div>
              <span class="text-26rpx">善豆至多抵扣{{ moneyInfo?.totalPointsMoney }}</span>
            </div>
          </div>

          <wd-switch
            :disabled="moneyInfo?.points === 0"
            v-model="discountCheckMap.pointChecked"
            size="18px"
          />
        </div>
        <!-- 红包 -->
        <div
          v-if="checkedPay.includes(60) && moneyInfo?.redPacket !== '0.00'"
          class="flex items-center justify-between"
        >
          <div class="w-full py30rpx box-border min-h-50rpx flex items-center gap-x-5px">
            <wd-img
              src="https://file.shanqianqi.com/image/2025/06/16/83317643ec6d4e5ab36a97daba7e5c5d.png"
              width="52rpx"
              height="52rpx"
              mode="aspectFill"
            />
            <span class="text-26rpx">红包抵扣：(剩余：{{ moneyInfo?.redPacket ?? '0' }})</span>
          </div>
          <wd-switch
            :disabled="moneyInfo?.redPacket === 0"
            v-model="discountCheckMap.redPocketChecked"
            size="18px"
          />
        </div>
        <!-- 余额 -->
        <div
          v-if="checkedPay.includes(10) && moneyInfo?.balance !== '0.00'"
          class="flex items-center justify-between"
        >
          <div class="w-full py30rpx box-border min-h-50rpx flex items-center gap-x-5px">
            <div class="i-hugeicons-coins-01 text-#FF7D26 w-52rpx h-52rpx"></div>
            <span class="text-26rpx">余额抵扣：(剩余：{{ moneyInfo?.balance ?? '0' }})</span>
          </div>
          <wd-switch
            :disabled="moneyInfo?.balance === 0"
            v-model="discountCheckMap.balanceChecked"
            size="18px"
          />
        </div>
        <!-- 拉卡拉 -->
        <div v-if="checkedPay.includes(40)" class="flex items-center justify-between">
          <div class="w-full py30rpx box-border min-h-50rpx flex items-center gap-x-5px">
            <!-- #ifdef MP-WEIXIN -->
            <div class="i-ri-wechat-pay-line text-#FF7D26 w-52rpx h-52rpx"></div>
            <span class="text-26rpx">微信支付</span>

            <!-- #endif -->
            <!-- #ifdef H5 -->
            <template v-if="isWeixin()">
              <div class="i-ri-wechat-pay-line text-#FF7D26 w-52rpx h-52rpx"></div>
              <span class="text-26rpx">微信支付</span>
            </template>
            <template v-else-if="isAlipay()">
              <div class="i-ri-alipay-line text-#FF7D26 w-52rpx h-52rpx"></div>
              <span class="text-26rpx">支付宝支付</span>
            </template>
            <template v-else>
              <!-- <div class="i-hugeicons-wallet-01 text-#FF7D26"></div> -->
              <wd-img
                src="https://file.shanqianqi.com/image/2025/06/16/0c473971274a489c9abc241aac1d1339.png"
                width="52rpx"
                height="52rpx"
                mode="aspectFill"
              />

              <span class="text-26rpx">拉卡拉聚合支付</span>
            </template>
            <!-- #endif -->
          </div>
          <wd-checkbox size="large" :disabled="disableWxPay" v-model="payCheck"></wd-checkbox>
        </div>
      </div>
    </div>
    <!-- <wd-keyboard
      v-model="paymentPassword"
      :maxlength="6"
      mode="custom"
      safeAreaInsetBottom
      v-model:visible="paymentVisible"
      title="六位数字密码"
      close-text="完成"
      @close="handlePasswordConfirm"
    ></wd-keyboard> -->
    <pay-pop
      txt="支付密码"
      :pwdlength="6"
      ref="payPopRef"
      :is_rpwd="isSetPwd"
      @pwd_reset="handleSetPassword"
      @reset-change="handleResetChange"
      @pwd_e="handlePasswordConfirm"
    />
    <wd-button
      @click="handlePrePay"
      custom-class="!w-full !min-w-unset !m-unset !h-100rpx !text-30rpx"
    >
      立即支付
    </wd-button>
  </view>
</template>

<script lang="ts" setup>
import SimpleNavBar from '@/components/SimpleNavBar/index.vue'
import {
  balanceToPay,
  orderPay,
  balanceOrderPay,
  toPay,
  UserOrderDetail,
  checkUserPaymentPassword,
  setUserPaymentPassword,
} from '@/service'
import { lklPay, isAlipay, isWeixin } from '@/utils'
import { getPlatform } from '@/utils'
import dayjs from 'dayjs'
import { useUserStore } from '@/store'

const userStore = useUserStore()

// #ifdef MP-WEIXIN

// #endif

defineOptions({
  name: 'PayOrder',
})

const { safeAreaInsets } = uni.getSystemInfoSync()

const discountCheckMap = ref({
  pointChecked: false,
  balanceChecked: false,
  redPocketChecked: false,
})

const payCheck = ref<boolean>(true)

const orderInfo = ref<{ orderId: string; orderType: string }>({
  orderId: '',
  orderType: '',
})

const storeId = ref('')
onLoad((option) => {
  console.log('🚀 ~ onLoad ~ option:', option)
  if (option) {
    orderInfo.value.orderId = option.orderId

    orderInfo.value.orderType = option.orderType

    if (option?.storeId) storeId.value = option?.storeId
    getData()
  }
})

const moneyInfo = ref<{
  balance: string | number
  points: number
  redPacket: string | number
  totalPointsMoney: string
}>({
  balance: '',
  points: 0,
  redPacket: '',
  totalPointsMoney: '',
})

const checkedPay = ref([])

const payPrice = ref('')

const prePayMap = {
  '30': balanceToPay,
  default: toPay,
}

const payMap = {
  '30': balanceOrderPay,
  default: orderPay,
}

const countdown = ref('')
const intervalTimer = ref(null)

function updateCountdown(payEndtime) {
  if (!payEndtime) {
    countdown.value = '订单已超时'
    clearInterval(intervalTimer.value)
    return
  }
  const end = dayjs(payEndtime)
  const now = dayjs()
  const diff = end.diff(now, 'second')

  if (diff <= 0) {
    countdown.value = '订单已超时'
    clearInterval(intervalTimer.value)
    return
  }

  const hours = String(Math.floor(diff / 3600)).padStart(2, '0')
  const minutes = String(Math.floor((diff % 3600) / 60)).padStart(2, '0')
  const seconds = String(diff % 60).padStart(2, '0')

  countdown.value = `${hours}:${minutes}:${seconds}`
}

onUnmounted(() => {
  intervalTimer.value && clearInterval(intervalTimer.value)
})

const getData = async () => {
  uni.showLoading({
    title: '加载中',
  })
  // let url = 'user/order/toPay'
  // if (orderInfo.value.orderType === '20') {
  //   url = 'plus/package/package/toPay'
  // }
  // if (orderInfo.value.orderType === '30') {
  //   url = 'balance/plan/toPay'
  // }
  // if (orderInfo.value.orderType === '70') {
  //   url = 'plus/live/plan/toPay'
  // }
  // if (orderInfo.value.orderType === '60') {
  //   url = 'supplier/index/toPay'
  // }
  // /* 支付尾款 */
  // if (orderInfo.value.orderType === '80') {
  //   url = 'plus/advance/order/toPay'
  // }

  const params = {
    orderId: orderInfo.value.orderId,
    paySource: getPlatform(),
    appId: import.meta.env.VITE_APPID,
  }

  const payHandler = prePayMap[orderInfo.value.orderType] || prePayMap?.default

  try {
    const { data } = await payHandler(params)

    const payEndtime = data?.order?.payEndTime

    intervalTimer.value = setInterval(() => {
      updateCountdown(payEndtime)
    }, 1000)

    moneyInfo.value.balance = data?.balance
    moneyInfo.value.points = data?.points
    moneyInfo.value.redPacket = data?.redPacket
    moneyInfo.value.totalPointsMoney = data?.totalPointsMoney

    checkedPay.value = data?.payTypes

    payPrice.value = data?.payPrice
  } catch (err) {}
  uni.hideLoading()
}
const disableWxPay = computed(() => {
  const pay = Number(payPrice.value)
  const balance = Number(moneyInfo.value.balance)
  const points = Number(moneyInfo.value.points)
  const redPacket = Number(moneyInfo.value.redPacket)
  const totalPointsMoney = Number(moneyInfo.value.totalPointsMoney)

  let total = 0

  // 余额抵扣
  if (discountCheckMap.value.balanceChecked) {
    total += Math.min(Number.isFinite(balance) ? balance : 0, pay)
  }

  // 善豆抵扣（最多 totalPointsMoney，且不能超 pay）
  if (discountCheckMap.value.pointChecked) {
    const usablePoints = Math.min(
      Number.isFinite(points) ? points : 0,
      Number.isFinite(totalPointsMoney) ? totalPointsMoney : 0,
    )
    total += usablePoints
  }

  // 红包抵扣
  if (discountCheckMap.value.redPocketChecked) {
    total += Number.isFinite(redPacket) ? redPacket : 0
  }

  return total >= pay
})

const realPayAmount = computed(() => {
  const pay = Number(payPrice.value)
  const balance = Number(moneyInfo.value.balance)
  const points = Number(moneyInfo.value.points)
  const totalPointsMoney = Number(moneyInfo.value.totalPointsMoney)
  const redPacket = Number(moneyInfo.value.redPacket)

  // 计算实际可抵扣金额
  const balanceDiscount = discountCheckMap.value.balanceChecked ? Math.min(balance, pay) : 0

  const pointDiscount = discountCheckMap.value.pointChecked ? Math.min(points, totalPointsMoney) : 0

  const redPacketDiscount = discountCheckMap.value.redPocketChecked ? redPacket : 0

  const totalDiscount = balanceDiscount + pointDiscount + redPacketDiscount

  // 实付金额 = 总额 - 抵扣
  const finalPay = pay - totalDiscount

  return finalPay > 0 ? finalPay : 0
})

watch(
  () => disableWxPay.value,
  (val) => {
    if (val) payCheck.value = false
    else {
      payCheck.value = true
    }
  },
)

const isSetPwd = ref(!userStore?.userInfo?.paymentPassword)

const handleSetPassword = async (password: string) => {
  await setUserPaymentPassword({ paymentPassword: password })
  await userStore.fetchUserCenterConfig()
  uni.showToast({ title: '设置成功', icon: 'none' })
}

const handlePasswordConfirm = async (password: string) => {
  const { data } = await checkUserPaymentPassword({ password })
  if (data) {
    handlePay()
  } else {
    uni.showToast({ title: '密码错误', icon: 'none' })
  }
}

const handleResetChange = () => {
  if (!userStore?.userInfo?.paymentPassword) {
    return
  }
  isSetPwd.value = !isSetPwd.value
}

const payPopRef = ref()

const handlePrePay = () => {
  if (disableWxPay.value) {
    isSetPwd.value = !userStore?.userInfo?.paymentPassword
    payPopRef.value?.Open()

    // paymentVisible.value = true
  } else {
    handlePay()
  }
}

const handlePay = async () => {
  // if (!payCheck.value) {
  //   uni.showToast({ title: '请勾选支付方式', icon: 'error' })
  //   return
  // }

  const payHandler = payMap[orderInfo.value.orderType] || payMap?.default

  let payType = 0
  if (checkedPay.value[0] !== 10) {
    payType = checkedPay.value[0] || checkedPay.value[1] || 20
  } else {
    payType = checkedPay.value[1] || checkedPay.value[0]
  }
  const payBody = {
    appId: import.meta.env.VITE_APPID,
    paySource: getPlatform(),
    orderId: orderInfo.value.orderId,
    payType,
    useBalance: Number(discountCheckMap.value?.balanceChecked),
    useShanDouMoney: Number(discountCheckMap.value?.pointChecked),
    useHongBaoMoney: Number(discountCheckMap.value?.redPocketChecked),
  }
  uni.showLoading({
    title: '加载中',
  })
  try {
    const { data } = await payHandler(payBody)
    if (data?.payment) {
      lklPay(data?.payment?.resp_data, () => {
        getState()
      })

      const getState = async () => {
        const { data: state } = await UserOrderDetail({ orderId: data.orderId })
        if (state?.detail?.payStatus === 20) {
          setTimeout(() => {
            if (orderInfo.value.orderType === '30') {
              uni.navigateBack({ delta: 2 })
            } else {
              uni.navigateTo({ url: `/pages-sub/goods/pay-success/index?orderId=${data.orderId}` })
            }
          }, 1000)
        } else {
          getState()
        }
      }
    } else {
      uni.navigateTo({ url: `/pages-sub/goods/pay-success/index?orderId=${data.orderId}` })
    }
  } catch (err) {
  } finally {
    uni.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox__label) {
  display: none !important;
}
</style>
