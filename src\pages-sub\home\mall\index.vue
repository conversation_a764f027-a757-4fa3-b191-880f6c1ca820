<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-#f2f2f2 overflow-hidden pb-2 box-border h-full">
    <l-fab axis="xy" v-model:offset="cartOffset" magnetic="x">
      <template #default>
        <div
          v-if="userStore.isLogined"
          @click="handleGoCart"
          class="rd-1/2 flex items-center justify-center z-1000 transition-all-300"
        >
          <wd-badge :modelValue="getTotalProductCount(cartStore?.cartData?.productList ?? [])">
            <wd-img
              src="https://file.shanqianqi.com/image/2025/06/13/66fca48b23e545ae920896d2d3534529.png"
              width="100rpx"
              height="100rpx"
              mode="aspectFill"
            ></wd-img>
            <!-- <div class="i-carbon-shopping-cart text-#333333 text-40rpx" /> -->
          </wd-badge>
        </div>
      </template>
    </l-fab>

    <z-paging
      ref="paging"
      v-model="dataList"
      :default-page-size="10"
      :fixed="false"
      :show-loading-more-no-more-view="false"
      @query="queryList"
    >
      <template #top>
        <div class="flex flex-col">
          <HomeSubNavBar
            :hide-scan="true"
            @update-input-value="handleSearchGoodName"
            @update-location="handleLocationChange"
            placeholder="搜索商品"
            left-title=""
          />
        </div>
      </template>
      <div class="grid grid-cols-1 gap-y-20rpx">
        <!-- 轮播 -->
        <div
          :style="{ background: themeStore?.navColor }"
          class="px5px box-border transition-all-300"
        >
          <wd-swiper
            :list="swiperList"
            value-key="imgUrl"
            autoplay
            v-model:current="swiperCurrent"
            height="110"
            @change="handleSwiperChange"
          ></wd-swiper>
        </div>
        <template v-if="dataList.length">
          <div class="px-20rpx pb-20rpx grid grid-cols-1 gap-y-20rpx grid-auto-rows-min">
            <!-- 导航 -->
            <scroll-view scroll-x class="bg-#ffffff rd-8px w-full px-14px box-border">
              <div class="flex flex-nowrap gap-x-14px">
                <div
                  class="grid grid-cols-5 gap-y-10px gap-x-20px py12px min-w-full box-border grid-rows-[auto_auto]"
                  v-for="(group, inxex) in categoryList"
                  :key="inxex"
                >
                  <div
                    v-for="category in group"
                    @click="handleCategoryChange(category?.categoryId)"
                    :key="category?.categoryId"
                    class="w-full flex aspect-square flex-col items-center gap-y-14rpx justify-center"
                  >
                    <div class="flex items-center justify-center rd-1/2 transition-all-300">
                      <wd-img
                        :src="category?.imagePath"
                        width="96rpx"
                        height="96rpx"
                        radius="50%"
                        mode="aspectFit"
                      ></wd-img>
                    </div>
                    <span class="text-24rpx text-#333333 inline-block transition-all-300">
                      {{ category?.name }}
                    </span>
                  </div>
                </div>
              </div>
            </scroll-view>

            <!-- 广告 -->
            <div v-if="advertisementData?.style?.layout === -2" class="grid grid-cols-2 gap-x-5px">
              <div
                class="w-full border border-solid border-#ffffff rd-10px box-border bg-gradient-to-b from-#FFE7DE to-#ffffff flex flex-col justify-between gap-y-2px p8px"
              >
                <div class="flex items-center justify-between">
                  <span class="text-28rpx text-#333333 font-500">家居日用</span>
                  <div class="flex items-center gap-x-2px">
                    <span class="text-20rpx text-#999999">抢购中</span>
                    <div class="i-carbon-chevron-right text-10px text-#333333 font-bold"></div>
                  </div>
                </div>
                <wd-img
                  width="314rpx"
                  height="262rpx"
                  mode="aspectFill"
                  :src="advertisementData?.data?.[5]?.imgUrl"
                ></wd-img>
              </div>
              <div class="grid grid-rows-[auto_auto] gap-y-5px">
                <div
                  class="size-full border border-solid border-#ffffff rd-10px box-border bg-gradient-to-b from-#FEF5D1 to-#ffffff py7px px-10px box-border flex flex-col justify-between"
                >
                  <div class="flex items-center justify-between">
                    <span class="text-28rpx text-#333333 font-500">超市便利</span>
                    <div
                      class="flex items-center justify-center w-140rpx h30rpx bg-#FF7D26 rd-14px text-20rpx text-#ffffff"
                    >
                      家庭生活好物
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex flex-col items-center gap-y-5px">
                      <wd-img
                        width="110rpx"
                        height="72rpx"
                        :src="advertisementData?.data?.[6]?.imgUrl"
                        mode="aspectFill"
                      ></wd-img>
                      <span class="text-#FF7D26 text-20rpx font-500">¥9.9起</span>
                    </div>
                    <div class="flex flex-col items-center gap-y-5px">
                      <wd-img
                        width="110rpx"
                        height="72rpx"
                        :src="advertisementData?.data?.[7]?.imgUrl"
                        mode="aspectFill"
                      ></wd-img>
                      <span class="text-#FF7D26 text-20rpx font-500">¥13.9起</span>
                    </div>
                  </div>
                </div>
                <div
                  class="size-full border border-solid border-#ffffff rd-10px box-border bg-gradient-to-b from-#DEF5E1 to-#ffffff py7px px-10px box-border flex flex-col justify-between"
                >
                  <div class="flex items-center justify-between">
                    <span class="text-28rpx text-#333333 font-500">生鲜果蔬</span>
                    <div
                      class="flex items-center justify-center w-140rpx h30rpx bg-#FF7D26 rd-14px text-20rpx text-#ffffff"
                    >
                      好品质更用心
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex flex-col items-center gap-y-5px">
                      <wd-img
                        width="110rpx"
                        height="72rpx"
                        :src="advertisementData?.data?.[8]?.imgUrl"
                        mode="aspectFill"
                      ></wd-img>
                      <span class="text-#38E791 text-20rpx font-500">新鲜血橙</span>
                    </div>
                    <div class="flex flex-col items-center gap-y-5px">
                      <wd-img
                        width="110rpx"
                        height="72rpx"
                        :src="advertisementData?.data?.[9]?.imgUrl"
                        mode="aspectFill"
                      ></wd-img>
                      <span class="text-#38E791 text-20rpx font-500">甜美蓝莓</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 商品 -->
            <div class="grid grid-cols-2 gap-10px">
              <div
                @click="() => goGoodsDetail(item?.productId)"
                v-for="item in dataList"
                :key="item?.productId"
                class="flex flex-col bg-#ffffff shadow-[0rpx_4rpx_8rpx_0rpx_rgba(0,0,0,0.02)] rd-20rpx of-hidden box-border"
              >
                <div class="relative">
                  <wd-img
                    width="100%"
                    height="460rpx"
                    radius="10px"
                    mode="aspectFill"
                    :src="item?.productImage"
                  ></wd-img>
                  <div
                    v-if="item?.productStock <= 0"
                    class="overlay text-#ffffff bg-[rgba(0,0,0,0.5)] absolute top-0 left-0 size-full flex items-center justify-center"
                  >
                    <span>售罄</span>
                  </div>
                </div>

                <div class="w-full p-20rpx box-border grid grid-cols-1">
                  <span class="text-25rpx fw-700 text-#333333 line-clamp-2">
                    {{ item?.productName }}
                  </span>
                  <div class="flex items-baseline justify-between">
                    <div class="flex items-baseline fw-700 flex-1 min-w-0">
                      <span class="text-20rpx text-#FF7D26">¥</span>
                      <span class="text-28rpx text-#FF7D26 text-20rpx break-all">
                        {{ item?.productEnumPrice?.split('.')?.[0] }}
                      </span>
                      <span class="text-20rpx text-#FF7D26 break-all">
                        .{{ item?.productEnumPrice?.split('.')?.[1] }}
                      </span>
                    </div>
                    <div class="text-20rpx flex items-center text-#999999 flex-shrink-0">
                      已售{{ formatLargeNumber(item?.productSales) }}+
                    </div>
                  </div>

                  <div class="flex flex-col">
                    <div
                      v-if="item?.extraRedPocketPrice && item?.extraRedPocketPrice !== '0.00'"
                      class="flex items-center gap-x-2px"
                    >
                      <span class="text-#FF7D26 text-24rpx fw-400 break-all flex-1 min-w-0">
                        +{{ item?.extraRedPocketPrice }}
                        <image
                          class="w28rpx h28rpx flex-shrink-0"
                          src="https://file.shanqianqi.com/image/2025/06/16/4c5ad84a06aa4491a4e5475e01f14986.png"
                        ></image>
                      </span>
                    </div>
                    <div
                      v-if="item?.isShowBeanValue === 'Y'"
                      class="flex items-center text-24rpx text-24rpx fw-400"
                    >
                      <span class="mx-0.5 text-#FF7D26">-</span>
                      <span class="text-#FF7D26">{{ item?.beanDeductionValue }}</span>
                      <image
                        class="w30rpx h30rpx flex-shrink-0"
                        src="https://file.shanqianqi.com/image/2025/06/24/e50f41403ded42a18709e739b74e8b91.png"
                      ></image>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { formatLargeNumber } from '@/utils'
import HomeSubNavBar from '@/components/HomeSubNavBar/index.vue'
import { fetchGoodsCategory, fetchIndexGoods } from '@/service'
import { useThemeStore } from '@/store/theme'
import { getPlatform } from '@/utils'
import { getTotalProductCount, chunkCategoryList } from '@/utils'
import { useCartStore, useUserStore, useLocationStore, useLoginSettingStore } from '@/store'

const themeStore = useThemeStore()
const cartStore = useCartStore()
const userStore = useUserStore()
const loginSettingStore = useLoginSettingStore()
// const locationStore = useLocationStore()

const { windowHeight, windowWidth, platform, safeAreaInsets } = uni.getSystemInfoSync()

console.log('🚀 ~ windowHeight:', windowHeight)
// 获取胶囊按钮信息（小程序环境）
let menuButtonInfo = { width: 0, height: 0, top: 0, right: 0 }
try {
  // #ifdef MP-WEIXIN
  menuButtonInfo = uni.getMenuButtonBoundingClientRect()
  // #endif
} catch (e) {
  console.log('获取胶囊信息失败:', e)
}

// 计算 tabBar 高度（参考 TabBar 组件）
const getTabBarHeight = () => {
  // 不同平台的 tabBar 基础高度不同
  let baseTabBarHeight = 50 // 默认高度 (px)

  // 加上底部安全区域和 TabBar 组件的 30rpx 内边距
  return baseTabBarHeight + (safeAreaInsets?.bottom || 15)
}

const tabBarHeight = getTabBarHeight()
const cartIconSize = uni.upx2px(100) // 购物车图标大小
const minY = (safeAreaInsets?.top || 0) + 20 // 最小 Y 坐标，不能超出状态栏
const maxY = windowHeight - tabBarHeight - cartIconSize - minY - 10 // 最大 Y 坐标，确保不被 tabBar 遮挡

// 确保初始位置给徽标留出空间
const badgeSize = 20 // 徽标大小估算
const initialX = windowWidth - cartIconSize - badgeSize - 10 // 图标大小 + 徽标空间 + 10px边距
const cartOffset = ref([initialX, Math.min(windowHeight / 1.5, maxY)])

// 监听购物车位置变化，限制拖拽边界
let isAdjusting = false
watch(
  cartOffset,
  (newOffset) => {
    if (isAdjusting) return // 防止无限循环

    const [x, y] = newOffset
    let adjustedX = x
    let adjustedY = y
    let needsAdjustment = false

    // 限制 Y 轴最小值，不能超出屏幕顶部
    if (y < minY) {
      console.log('触发顶部限制:', y, '<', minY)
      adjustedY = minY
      needsAdjustment = true
    }

    // 限制 Y 轴最大值，确保不会被 tabBar 遮挡
    if (y > maxY) {
      console.log('触发底部限制:', y, '>', maxY)
      adjustedY = maxY
      needsAdjustment = true
    }

    // 限制 X 轴范围，确保徽标不被屏幕边缘遮盖
    const badgeSize = 20 // 徽标大小估算
    const minX = badgeSize // 左边界：给徽标留出空间
    const maxX = windowWidth - cartIconSize - badgeSize // 右边界：图标大小 + 徽标空间

    if (x < minX) {
      console.log('触发左边界限制:', x, '<', minX)
      adjustedX = minX
      needsAdjustment = true
    }

    if (x > maxX) {
      console.log('触发右边界限制:', x, '>', maxX)
      adjustedX = maxX
      needsAdjustment = true
    }

    // 限制胶囊区域，确保不会遮挡胶囊按钮（只在微信小程序中）
    // #ifdef MP-WEIXIN
    if (menuButtonInfo.width > 0) {
      const capsuleLeft = windowWidth - menuButtonInfo.width - 20 // 胶囊左边界（留20px边距）
      const capsuleTop = menuButtonInfo.top - 10 // 胶囊上边界（留10px边距）
      const capsuleBottom = menuButtonInfo.top + menuButtonInfo.height + 10 // 胶囊下边界（留10px边距）

      // 如果购物车在胶囊区域内，完全禁止拖拽到这个区域
      if (y >= capsuleTop && y <= capsuleBottom && x >= capsuleLeft) {
        console.log('触发胶囊禁区限制:', { x, y, capsuleLeft, capsuleTop, capsuleBottom })
        // 将购物车移到胶囊左侧或下方，选择距离更近的位置
        const distanceToLeft = Math.abs(x - (capsuleLeft - cartIconSize))
        const distanceToBottom = Math.abs(y - (capsuleBottom + 10))

        if (distanceToLeft <= distanceToBottom) {
          // 移到胶囊左侧，确保徽标有空间显示
          const badgeSize = 20
          adjustedX = Math.max(badgeSize, capsuleLeft - cartIconSize - 10)
        } else {
          // 移到胶囊下方
          adjustedY = capsuleBottom + 10
        }
        needsAdjustment = true
      }
    }
    // #endif

    // 如果需要调整，更新位置
    if (needsAdjustment) {
      isAdjusting = true
      nextTick(() => {
        cartOffset.value = [adjustedX, adjustedY]
        setTimeout(() => {
          isAdjusting = false
        }, 100)
      })
    }
  },
  { deep: true },
)

// 添加调试信息
onMounted(() => {
  const badgeSize = 20
  const minX = badgeSize
  const maxX = windowWidth - cartIconSize - badgeSize

  console.log('购物车拖拽限制信息:', {
    windowHeight,
    windowWidth,
    platform,
    tabBarHeight,
    cartIconSize,
    badgeSize,
    边界限制: {
      minY,
      maxY,
      minX,
      maxX,
    },
    safeAreaInsets,
    menuButtonInfo,
    initialOffset: cartOffset.value,
  })

  // 专门测试胶囊信息
  // #ifdef MP-WEIXIN
  console.log('胶囊按钮禁区信息:', {
    原始胶囊: {
      width: menuButtonInfo.width,
      height: menuButtonInfo.height,
      top: menuButtonInfo.top,
      right: menuButtonInfo.right,
    },
    禁区范围: {
      capsuleLeft: windowWidth - menuButtonInfo.width - 20,
      capsuleTop: menuButtonInfo.top - 10,
      capsuleBottom: menuButtonInfo.top + menuButtonInfo.height + 10,
      capsuleRight: windowWidth,
    },
    说明: '购物车不能拖拽到此区域内',
  })
  // #endif
})

onShow(() => {
  if (!!userStore.isLogined) {
    cartStore.getCart()
  }
})

defineOptions({
  name: 'Mall',
})

const searchVal = ref('')
//fetchGoodsCategory
const { data: category } = useRequest(() => fetchGoodsCategory(), { immediate: true })
const categoryList = computed<Api.Home.GoodCategoryItem[][]>(() =>
  chunkCategoryList(category.value?.list ?? []),
)

const activeCategory = ref<number>()

const handleCategoryChange = (categoryId: number) => {
  activeCategory.value = categoryId
  // paging.value.reload()
  uni.navigateTo({ url: `/pages-my/home_mall_detail/index?categoryPid=${categoryId}` })
}

// 广告
const advertisementData = ref()

const paging = ref()
const dataList = ref<Api.Store.productItem[]>([])
const queryList = async (pageIndex: number, pageSize: number) => {
  try {
    const { data } = await fetchIndexGoods({
      source: getPlatform(),
    })
    if (data?.page?.items?.find((item) => item?.name === '商城图片轮播')?.data.length) {
      themeStore.setNavColor(
        data?.page?.items?.find((item) => item?.name === '商城图片轮播')?.data[
          swiperCurrent.value ?? 0
        ]?.background,
      )
    }
    swiperList.value = data?.page?.items?.find((item) => item?.name === '商城图片轮播')?.data ?? []

    advertisementData.value = data?.page?.items?.find((item) => item?.name === '首页图片橱窗')

    // const { data: mallData } = await fetchIndexMall({
    //   longitude: locationStore?.locationInfo?.location?.lon,
    //   latitude: locationStore?.locationInfo?.location?.lat,
    //   pageIndex,
    //   pageSize,
    // })
    // console.log('🚀 ~ queryList ~ mallData:', mallData)
    let records = data?.page?.items?.find((item) => item?.name === '商品组')?.data ?? []
    records.forEach((item) => {
      if (item) {
        // 处理价格显示：超过4位数转换为万元
        const price = parseFloat(item?.productPrice || '0')
        if (!isNaN(price) && price >= 10000) {
          // 超过4位数，转换为万元，保留两位小数
          const priceInWan = Math.round(price / 100) / 100 // 四舍五入到分
          item.productEnumPrice = (priceInWan / 100).toFixed(2) + '万'
        } else if (!isNaN(price)) {
          // 未超过4位数，保持原价格
          item.productEnumPrice = item?.productPrice
        } else {
          // 价格无效，设置默认值
          item.productEnumPrice = '0.00'
        }
      }
    })
    paging.value?.complete(records)
  } catch (err) {
    paging.value?.complete(false)
  }
}

const swiperCurrent = ref(0)

const swiperList = ref([])

const handleSwiperChange = ({ current }) => {
  themeStore.setNavColor(swiperList.value[current]?.background)
}

const handleSearchGoodName = (keyword: string) => {
  searchVal.value = keyword
  paging.value.reload()
}

const handleLocationChange = () => paging?.value?.reload()

const goGoodsDetail = (id: number) => {
  uni.navigateTo({ url: `/pages-sub/goods/detail/index?id=${id}&from=mall` })
}

const handleGoCart = () => {
  uni.navigateTo({ url: '/pages/cart/index' })
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs__nav-item) {
  background: #ff7d26 !important;
  color: #ffffff !important;
}
:deep(.wd-tabs__line) {
  background: #ffffff !important;
}
:deep(.l-fab) {
  background: transparent;
  // width: 0;
  // height: 0;
}

/* 移除 l-fab 的默认边距限制，允许全屏宽度拖拽，但限制顶部 */
:deep(.l-fab-wrapper) {
  left: 0 !important;
  right: 0 !important;
  width: 100vw !important;
  top: calc(env(safe-area-inset-top) + 20px) !important;
}

/* 针对不同平台设置底部限制，与 JavaScript 计算保持一致 */
/* #ifdef MP-WEIXIN */
:deep(.l-fab-wrapper) {
  /* Android: 56px + 30rpx + 安全区域 */
  bottom: calc(56px + 30rpx + env(safe-area-inset-bottom)) !important;
}
/* #endif */

/* #ifdef H5 */
:deep(.l-fab-wrapper) {
  /* 默认: 50px + 30rpx */
  bottom: calc(50px + 30rpx) !important;
}
/* #endif */

/* #ifdef APP-PLUS */
:deep(.l-fab-wrapper) {
  /* iOS: 49px + 30rpx + 安全区域 */
  bottom: calc(49px + 30rpx + env(safe-area-inset-bottom)) !important;
}
/* #endif */
</style>
